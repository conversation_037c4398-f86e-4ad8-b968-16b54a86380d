import { confirmPaymentIntent } from "@/lib/stripe";
import { supabase } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

export async function GET(_request: NextRequest, { params }: { params: { reservationId: string } }) {
	try {
		const { reservationId } = params;

		console.log("=== PAYMENT STATUS BY RESERVATION API ===");
		console.log("Reservation ID:", reservationId);

		if (!reservationId) {
			console.log("ERROR: No reservation ID provided");
			return NextResponse.json({ error: "Reservation ID is required" }, { status: 400 });
		}

		// Get the most recent payment for this reservation
		const { data: payment, error: paymentError } = await supabase
			.from("payments")
			.select(
				`
        *,
        reservation:reservations!payments_reservation_id_fkey(
          id,
          reservation_number,
          status,
          total_amount,
          service:services(name)
        )
      `
			)
			.eq("reservation_id", reservationId)
			.order("created_at", { ascending: false })
			.limit(1)
			.single();

		if (paymentError || !payment) {
			return NextResponse.json({ error: "Payment not found for this reservation" }, { status: 404 });
		}

		// Also check with <PERSON><PERSON> for the latest status if we have a payment intent ID
		let latestStatus = payment.status;
		if (payment.payment_intent_id) {
			const stripeResult = await confirmPaymentIntent(payment.payment_intent_id);

			if (stripeResult.success && stripeResult.paymentIntent) {
				latestStatus = stripeResult.paymentIntent.status;

				// Update database if status has changed
				if (latestStatus !== payment.status) {
					await supabase
						.from("payments")
						.update({
							status: latestStatus,
							updated_at: new Date().toISOString(),
						})
						.eq("payment_intent_id", payment.payment_intent_id);
				}
			}
		}

		return NextResponse.json({
			success: true,
			payment: {
				id: payment.id,
				paymentIntentId: payment.payment_intent_id,
				amount: payment.amount,
				currency: payment.currency,
				status: latestStatus,
				paymentDate: payment.payment_date,
				failureReason: payment.failure_reason,
				createdAt: payment.created_at,
				updatedAt: payment.updated_at,
			},
			reservation: payment.reservation,
		});
	} catch (error) {
		console.error("Error getting payment status by reservation:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}

"use client";

import { useState, useEffect } from "react";
import { Users, Plus, Minus } from "lucide-react";

interface PricingTier {
	id: string;
	tier_name: string;
	price: number;
	min_age?: number;
	max_age?: number;
}

interface TierParticipant {
	tierId: string;
	count: number;
}

interface Service {
	id: string;
	name: string;
	max_participants: number;
	min_age?: number;
	max_age?: number;
	pricing_tiers?: PricingTier[];
	fixed_price: boolean;
	base_price: number;
}

interface ParticipantCountSelectorProps {
	service: Service | null;
	tierParticipants: TierParticipant[];
	onTierParticipantsChange: (tierParticipants: TierParticipant[]) => void;
	disabled?: boolean;
}

export default function ParticipantCountSelector({
	service,
	tierParticipants,
	onTierParticipantsChange,
	disabled = false,
}: ParticipantCountSelectorProps) {
	const [localTierParticipants, setLocalTierParticipants] = useState<TierParticipant[]>(tierParticipants);

	useEffect(() => {
		setLocalTierParticipants(tierParticipants);
	}, [tierParticipants]);

	// Initialize tier participants when service changes
	useEffect(() => {
		if (service && service.pricing_tiers && service.pricing_tiers.length > 0) {
			// If no tier participants set, initialize all tiers with 0 count
			if (tierParticipants.length === 0) {
				const initialTierParticipants = service.pricing_tiers.map((tier) => ({
					tierId: tier.id,
					count: 0,
				}));
				setLocalTierParticipants(initialTierParticipants);
				onTierParticipantsChange(initialTierParticipants);
			}
		} else if (service && (!service.pricing_tiers || service.pricing_tiers.length === 0)) {
			// For services without pricing tiers, use a simple count
			if (tierParticipants.length === 0) {
				const simpleTierParticipants = [
					{
						tierId: "default",
						count: 0,
					},
				];
				setLocalTierParticipants(simpleTierParticipants);
				onTierParticipantsChange(simpleTierParticipants);
			}
		}
	}, [service]);

	const updateTierCount = (tierId: string, newCount: number) => {
		const updatedTierParticipants = localTierParticipants.map((tp) =>
			tp.tierId === tierId ? { ...tp, count: Math.max(0, newCount) } : tp
		);

		// Keep all tiers, even with 0 count, so users can increment them back up
		setLocalTierParticipants(updatedTierParticipants);
		onTierParticipantsChange(updatedTierParticipants);
	};

	const addTierParticipant = (tierId: string) => {
		const existingTier = localTierParticipants.find((tp) => tp.tierId === tierId);
		if (existingTier) {
			updateTierCount(tierId, existingTier.count + 1);
		} else {
			const newTierParticipants = [...localTierParticipants, { tierId, count: 1 }];
			setLocalTierParticipants(newTierParticipants);
			onTierParticipantsChange(newTierParticipants);
		}
	};

	const getTotalParticipants = () => {
		return localTierParticipants.reduce((sum, tp) => sum + tp.count, 0);
	};

	const getTierCount = (tierId: string) => {
		return localTierParticipants.find((tp) => tp.tierId === tierId)?.count || 0;
	};

	const formatAgeRange = (minAge?: number, maxAge?: number) => {
		if (!minAge && !maxAge) return "tous âges";
		if (!minAge) return `jusqu'à ${maxAge} ans`;
		if (!maxAge) return `${minAge}+ ans`;
		if (minAge === maxAge) return `${minAge} ans`;
		return `${minAge}-${maxAge} ans`;
	};

	const calculatePrice = () => {
		if (!service) return 0;

		if (service.fixed_price) {
			// Fixed price regardless of participant count
			if (service.pricing_tiers && service.pricing_tiers.length > 0) {
				// Use the first tier's price for fixed pricing
				return service.pricing_tiers[0].price;
			}
			return service.base_price;
		}

		// Variable pricing based on participants
		let total = 0;
		for (const tp of localTierParticipants) {
			const tier = service.pricing_tiers?.find((t) => t.id === tp.tierId);
			if (tier) {
				total += tier.price * tp.count;
			} else if (tp.tierId === "default") {
				total += service.base_price * tp.count;
			}
		}
		return total;
	};

	if (!service) {
		return null;
	}

	const totalParticipants = getTotalParticipants();
	const maxParticipants = service.max_participants;
	const canAddMore = totalParticipants < maxParticipants;

	return (
		<div className="space-y-4">
			<label className="block text-sm font-medium text-gray-700">
				Participants <span className="text-red-500">*</span>
			</label>

			{/* Summary */}
			<div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center space-x-2">
						<Users className="h-5 w-5 text-gray-600" />
						<span className="font-medium text-gray-900">
							{totalParticipants} participant{totalParticipants > 1 ? "s" : ""}
						</span>
						<span className="text-sm text-gray-500">(max {maxParticipants})</span>
					</div>
					<div className="text-right">
						<div className="font-medium text-emerald-600">{calculatePrice().toFixed(2)}€</div>
						{service.fixed_price && totalParticipants > 1 && (
							<div className="text-xs text-gray-500">Prix fixe</div>
						)}
					</div>
				</div>
			</div>

			{/* Pricing Tiers */}
			{service.pricing_tiers && service.pricing_tiers.length > 0 ? (
				<div className="space-y-3">
					{service.pricing_tiers.map((tier) => {
						const count = getTierCount(tier.id);
						return (
							<div key={tier.id} className="border border-gray-200 rounded-lg p-4">
								<div className="flex items-center justify-between">
									<div className="flex-1">
										<h4 className="font-medium text-gray-900">{tier.tier_name}</h4>
										<div className="flex items-center space-x-4 text-sm text-gray-600">
											<span>{formatAgeRange(tier.min_age, tier.max_age)}</span>
											<span className="font-medium text-emerald-600">
												{tier.price.toFixed(2)}€{!service.fixed_price && " / pers."}
											</span>
										</div>
									</div>
									<div className="flex items-center space-x-3">
										<button
											onClick={() => updateTierCount(tier.id, count - 1)}
											disabled={disabled || count === 0}
											className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-full hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
										>
											<Minus className="h-4 w-4" />
										</button>
										<span className="w-8 text-center font-medium">{count}</span>
										<button
											onClick={() => updateTierCount(tier.id, count + 1)}
											disabled={disabled || !canAddMore}
											className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-full hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
										>
											<Plus className="h-4 w-4" />
										</button>
									</div>
								</div>
							</div>
						);
					})}
				</div>
			) : (
				/* Simple participant count for services without tiers */
				<div className="border border-gray-200 rounded-lg p-4">
					<div className="flex items-center justify-between">
						<div className="flex-1">
							<h4 className="font-medium text-gray-900">Participants</h4>
							<div className="text-sm text-gray-600">
								{formatAgeRange(service.min_age, service.max_age)}
							</div>
						</div>
						<div className="flex items-center space-x-3">
							<button
								onClick={() => updateTierCount("default", getTierCount("default") - 1)}
								disabled={disabled || getTierCount("default") === 0}
								className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-full hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
							>
								<Minus className="h-4 w-4" />
							</button>
							<span className="w-8 text-center font-medium">{getTierCount("default")}</span>
							<button
								onClick={() => updateTierCount("default", getTierCount("default") + 1)}
								disabled={disabled || !canAddMore}
								className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-full hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
							>
								<Plus className="h-4 w-4" />
							</button>
						</div>
					</div>
				</div>
			)}

			{/* Validation Messages */}
			{totalParticipants === 0 && <div className="text-sm text-red-600">Au moins un participant est requis</div>}

			{totalParticipants > maxParticipants && (
				<div className="text-sm text-red-600">Le nombre maximum de participants est {maxParticipants}</div>
			)}
		</div>
	);
}

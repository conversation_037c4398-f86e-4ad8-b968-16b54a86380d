import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export const dynamic = 'force-dynamic';

// GET /api/settings - Get public business settings (no auth required)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')

    let query = supabase.from('business_settings').select('*').eq('is_public', true)

    // Filter by category if specified
    if (category) {
      query = query.eq('category', category)
    }

    const { data: settings, error } = await query.order('category').order('key')

    if (error) {
      console.error('Public settings query error:', error)
      return NextResponse.json({ error: 'Failed to fetch settings' }, { status: 500 })
    }

    // Transform settings into the expected format
    const settingsMap: Record<string, any> = {}
    const categorizedSettings: Record<string, Record<string, any>> = {}

    settings?.forEach((setting: any) => {
      let value = setting.value

      // Parse value based on type
      switch (setting.value_type) {
        case 'number':
          value = parseFloat(setting.value)
          break
        case 'boolean':
          value = setting.value === 'true'
          break
        case 'json':
          try {
            value = JSON.parse(setting.value)
          } catch {
            value = setting.value
          }
          break
        default:
          value = setting.value
      }

      settingsMap[setting.key] = value

      if (!categorizedSettings[setting.category]) {
        categorizedSettings[setting.category] = {}
      }
      categorizedSettings[setting.category][setting.key] = {
        value,
        description: setting.description,
        type: setting.value_type,
        isPublic: setting.is_public
      }
    })

    return NextResponse.json({
      settings: settingsMap,
      categorized: categorizedSettings,
      count: settings?.length || 0
    })

  } catch (error) {
    console.error('Public settings GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

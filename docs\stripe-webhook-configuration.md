# Configuration des Webhooks Stripe pour le Système de Caution

Ce document détaille la configuration complète des webhooks Stripe nécessaires pour le système de caution (security deposit) de l'application.

## Vue d'ensemble

Le système de caution utilise les webhooks Stripe pour synchroniser automatiquement les statuts des autorisations de paiement entre Stripe et notre base de données. Les webhooks sont essentiels pour :

- Confirmer les autorisations de caution réussies
- Gérer les échecs d'autorisation
- Synchroniser les statuts en temps réel
- Assurer la cohérence des données

## Événements Webhook Requis

### Événements Principaux

1. **`payment_intent.succeeded`**
   - Déclenché quand un paiement est complété avec succès
   - Utilisé pour les paiements de réservation et les captures de caution

2. **`payment_intent.payment_failed`**
   - Déclenché quand un paiement échoue
   - Permet de mettre à jour le statut en cas d'échec

3. **`payment_intent.requires_action`**
   - Déclench<PERSON> quand une action supplémentaire est requise (3D Secure, etc.)
   - Gère les cas d'authentification forte

4. **`payment_intent.canceled`**
   - Déclenché quand un payment intent est annulé
   - Utilisé pour libérer les autorisations de caution

5. **`payment_intent.amount_capturable_updated`** ⭐ **CRITIQUE POUR LES CAUTIONS**
   - Déclenché quand une autorisation est confirmée et prête à être capturée
   - **C'est l'événement principal pour confirmer les autorisations de caution**

## Configuration pour le Développement Local

### 1. Installation de Stripe CLI

```bash
# Télécharger et installer Stripe CLI depuis https://stripe.com/docs/stripe-cli
# Ou via package manager :

# Windows (Chocolatey)
choco install stripe-cli

# macOS (Homebrew)
brew install stripe/stripe-cli/stripe

# Linux (voir documentation Stripe)
```

### 2. Authentification Stripe CLI

```bash
# Se connecter à votre compte Stripe
stripe login

# Vérifier la connexion
stripe config --list
```

### 3. Configuration du Forwarding Local

```bash
# Démarrer le forwarding vers votre serveur local
stripe listen --forward-to localhost:3000/api/webhooks/stripe

# Alternative avec un port spécifique
stripe listen --forward-to http://localhost:3000/api/webhooks/stripe

# Pour écouter seulement les événements spécifiques
stripe listen --events payment_intent.succeeded,payment_intent.payment_failed,payment_intent.requires_action,payment_intent.canceled,payment_intent.amount_capturable_updated --forward-to localhost:3000/api/webhooks/stripe
```

### 4. Récupération du Webhook Secret

Quand vous démarrez `stripe listen`, la CLI affiche un webhook secret temporaire :

```
> Ready! Your webhook signing secret is whsec_1234567890abcdef...
```

**Ajoutez ce secret à votre fichier `.env.local` :**

```env
STRIPE_WEBHOOK_SECRET=whsec_1234567890abcdef...
```

### 5. Test des Webhooks en Local

```bash
# Dans un terminal, démarrer le forwarding
stripe listen --forward-to localhost:3000/api/webhooks/stripe

# Dans un autre terminal, démarrer votre application
npm run dev

# Tester avec des événements simulés
stripe trigger payment_intent.succeeded
stripe trigger payment_intent.amount_capturable_updated
```

## Configuration pour l'Environnement de Déploiement

### 1. Création du Webhook Endpoint

Dans le Dashboard Stripe :

1. Aller dans **Developers > Webhooks**
2. Cliquer sur **Add endpoint**
3. Entrer l'URL : `https://votre-domaine.com/api/webhooks/stripe`
4. Sélectionner les événements requis (voir liste ci-dessus)
5. Cliquer sur **Add endpoint**

### 2. Configuration des Événements

Sélectionner exactement ces événements :

```
payment_intent.succeeded
payment_intent.payment_failed  
payment_intent.requires_action
payment_intent.canceled
payment_intent.amount_capturable_updated
```

### 3. Récupération du Signing Secret

1. Cliquer sur le webhook créé
2. Dans la section **Signing secret**, cliquer sur **Reveal**
3. Copier le secret (commence par `whsec_`)

### 4. Configuration des Variables d'Environnement

**Pour Vercel :**
```bash
vercel env add STRIPE_WEBHOOK_SECRET
# Entrer la valeur du signing secret
```

**Pour d'autres plateformes :**
Ajouter la variable d'environnement `STRIPE_WEBHOOK_SECRET` avec la valeur du signing secret.

## Sécurité et Authentification

### Vérification des Signatures

Le code de vérification est dans `lib/stripe.ts` :

```typescript
export const verifyWebhookSignature = (
  payload: string | Buffer,
  signature: string
): { success: boolean; event?: StripeServer.Event; error?: string } => {
  // Vérification automatique avec le secret configuré
}
```

### Variables d'Environnement Requises

```env
# Clés Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...

# Secret webhook (différent pour dev/prod)
STRIPE_WEBHOOK_SECRET=whsec_...
```

## Test et Débogage

### 1. Logs de Débogage

Les webhooks loggent automatiquement dans la console :

```typescript
console.log("Received Stripe webhook:", event.type);
console.log("=== SECURITY DEPOSIT AUTHORIZATION WEBHOOK ===");
```

### 2. Test avec Stripe CLI

```bash
# Simuler une autorisation de caution réussie
stripe trigger payment_intent.amount_capturable_updated

# Simuler un paiement réussi
stripe trigger payment_intent.succeeded

# Simuler un échec
stripe trigger payment_intent.payment_failed
```

### 3. Vérification dans le Dashboard

1. **Stripe Dashboard > Developers > Webhooks**
2. Cliquer sur votre endpoint
3. Voir les **Recent deliveries** pour vérifier les statuts
4. Examiner les réponses (200 = succès, autres = erreur)

## Dépannage Courant

### Problème : Webhook Secret Invalide
```
Error: Invalid signature
```
**Solution :** Vérifier que `STRIPE_WEBHOOK_SECRET` correspond au secret du webhook.

### Problème : Événements Non Reçus
**Solution :** 
1. Vérifier que l'URL du webhook est correcte
2. Vérifier que les événements sont bien sélectionnés
3. Vérifier les logs du serveur

### Problème : Timeout des Webhooks
**Solution :** 
1. Optimiser le traitement des webhooks
2. Vérifier que l'endpoint répond rapidement (< 10s)
3. Implémenter une logique de retry si nécessaire

## Monitoring et Maintenance

### Surveillance des Webhooks

1. **Dashboard Stripe** : Surveiller les delivery rates
2. **Logs applicatifs** : Vérifier les erreurs de traitement
3. **Base de données** : Vérifier la cohérence des statuts

### Bonnes Pratiques

1. **Idempotence** : Gérer les webhooks dupliqués
2. **Timeout** : Répondre rapidement (< 10 secondes)
3. **Retry Logic** : Implémenter une logique de retry pour les échecs temporaires
4. **Monitoring** : Surveiller les taux de succès des webhooks

## Ressources Supplémentaires

- [Documentation Stripe Webhooks](https://stripe.com/docs/webhooks)
- [Stripe CLI Documentation](https://stripe.com/docs/stripe-cli)
- [Testing Webhooks](https://stripe.com/docs/webhooks/test)

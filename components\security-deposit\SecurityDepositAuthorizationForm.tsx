"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { StripeProvider } from "@/components/payment/StripeProvider";
import { PaymentElement, useStripe, useElements } from "@stripe/react-stripe-js";
import { AlertCircle, CheckCircle, CreditCard, Loader2, Shield } from "lucide-react";
import { useEffect, useState } from "react";

interface SecurityDepositAuthorizationFormProps {
	reservationId: string;
	amount: number;
	onSuccess: (paymentIntentId: string) => void;
	onError: (error: string) => void;
	onCancel?: () => void;
}

export default function SecurityDepositAuthorizationForm({
	reservationId,
	amount,
	onSuccess,
	onError,
	onCancel,
}: SecurityDepositAuthorizationFormProps) {
	const [clientSecret, setClientSecret] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [authorizationCreated, setAuthorizationCreated] = useState(false);

	useEffect(() => {
		const createAuthorization = async () => {
			// Prevent multiple authorization attempts
			if (authorizationCreated) {
				return;
			}

			try {
				setIsLoading(true);
				setError(null);
				setAuthorizationCreated(true);

				const response = await fetch("/api/security-deposits/authorize", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						reservationId,
						amount,
					}),
				});

				const result = await response.json();

				if (!response.ok) {
					throw new Error(result.error || "Failed to create authorization");
				}

				setClientSecret(result.clientSecret);
			} catch (error) {
				console.error("Error creating security deposit authorization:", error);
				setError(error instanceof Error ? error.message : "Unknown error");
				setAuthorizationCreated(false); // Reset flag on error
				onError(error instanceof Error ? error.message : "Unknown error");
			} finally {
				setIsLoading(false);
			}
		};

		createAuthorization();
	}, [reservationId, amount, onError, authorizationCreated]);

	const formatAmount = (amount: number) => {
		return new Intl.NumberFormat("fr-FR", {
			style: "currency",
			currency: "EUR",
		}).format(amount);
	};

	if (isLoading) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Shield className="w-5 h-5" />
						Autorisation de caution
					</CardTitle>
				</CardHeader>
				<CardContent className="text-center py-8">
					<Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-emerald-600" />
					<p className="text-gray-600">Préparation de l'autorisation sécurisée...</p>
				</CardContent>
			</Card>
		);
	}

	if (error || !clientSecret) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Shield className="w-5 h-5" />
						Autorisation de caution
					</CardTitle>
				</CardHeader>
				<CardContent>
					<Alert className="border-red-200 bg-red-50">
						<AlertCircle className="w-4 h-4 text-red-600" />
						<AlertDescription className="text-red-700">
							{error || "Erreur lors de la préparation de l'autorisation"}
						</AlertDescription>
					</Alert>
					{onCancel && (
						<div className="mt-4">
							<Button variant="outline" onClick={onCancel} className="w-full">
								Retour aux options de caution
							</Button>
						</div>
					)}
				</CardContent>
			</Card>
		);
	}

	return (
		<StripeProvider clientSecret={clientSecret}>
			<SecurityDepositPaymentForm
				amount={amount}
				reservationId={reservationId}
				onSuccess={onSuccess}
				onError={onError}
				onCancel={onCancel}
			/>
		</StripeProvider>
	);
}

interface SecurityDepositPaymentFormProps {
	amount: number;
	reservationId: string;
	onSuccess: (paymentIntentId: string) => void;
	onError: (error: string) => void;
	onCancel?: () => void;
}

function SecurityDepositPaymentForm({
	amount,
	reservationId,
	onSuccess,
	onError,
	onCancel,
}: SecurityDepositPaymentFormProps) {
	const stripe = useStripe();
	const elements = useElements();
	const [isProcessing, setIsProcessing] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [isComplete, setIsComplete] = useState(false);

	const formatAmount = (amount: number) => {
		return new Intl.NumberFormat("fr-FR", {
			style: "currency",
			currency: "EUR",
		}).format(amount);
	};

	const handleSubmit = async (event: React.FormEvent) => {
		event.preventDefault();

		if (!stripe || !elements) {
			setError("Stripe n'est pas encore chargé. Veuillez réessayer.");
			return;
		}

		setIsProcessing(true);
		setError(null);

		try {
			// Submit the form to validate
			const { error: submitError } = await elements.submit();
			if (submitError) {
				setError(submitError.message || "Erreur lors de la validation du formulaire");
				setIsProcessing(false);
				return;
			}

			// Confirm the payment intent (authorization)
			const { error: confirmError, paymentIntent } = await stripe.confirmPayment({
				elements,
				confirmParams: {
					return_url: `${typeof window !== "undefined" ? window.location.origin : ""}/reservation/payment-success?booking=${reservationId}`,
				},
				redirect: "if_required",
			});

			if (confirmError) {
				setError(confirmError.message || "Erreur lors de l'autorisation");
				onError(confirmError.message || "Authorization failed");
			} else if (paymentIntent) {
				if (paymentIntent.status === "requires_capture") {
					// Authorization successful
					setIsComplete(true);
					onSuccess(paymentIntent.id);
				} else {
					setError("Statut d'autorisation inattendu");
					onError("Unexpected authorization status");
				}
			}
		} catch (error) {
			console.error("Error during authorization:", error);
			setError("Erreur lors de l'autorisation");
			onError("Authorization failed");
		} finally {
			setIsProcessing(false);
		}
	};

	if (isComplete) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<CheckCircle className="w-5 h-5 text-green-600" />
						Autorisation confirmée
					</CardTitle>
				</CardHeader>
				<CardContent className="text-center py-8">
					<div className="text-green-600 mb-4">
						<CheckCircle className="w-16 h-16 mx-auto" />
					</div>
					<h3 className="text-lg font-semibold text-gray-900 mb-2">
						Pré-autorisation de {formatAmount(amount)} confirmée
					</h3>
					<p className="text-gray-600 mb-4">
						Votre caution d'équipement a été pré-autorisée avec succès. Aucun prélèvement n'a été effectué.
					</p>
					<div className="bg-green-50 rounded-lg p-4 text-sm text-green-700">
						<p className="font-medium mb-1">Libération automatique sous 5-7 jours</p>
						<p>La pré-autorisation sera automatiquement libérée si aucun dommage n'est constaté.</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Shield className="w-5 h-5" />
					Autorisation de caution
				</CardTitle>
				<div className="text-sm text-gray-600">
					Montant à pré-autoriser : <span className="font-semibold">{formatAmount(amount)}</span>
				</div>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					{/* Important Notice */}
					<div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
						<div className="flex items-start gap-2">
							<Shield className="w-5 h-5 text-blue-600 mt-0.5" />
							<div>
								<h4 className="font-medium text-blue-900 mb-1">Pré-autorisation sécurisée</h4>
								<p className="text-sm text-blue-700">
									Cette opération ne prélève aucun montant sur votre carte. Elle réserve simplement le
									montant de la caution qui sera libéré automatiquement sous 5-7 jours si aucun
									dommage n'est constaté.
								</p>
							</div>
						</div>
					</div>

					{/* Payment Element */}
					<div className="p-4 border rounded-lg bg-gray-50">
						<PaymentElement
							options={{
								layout: "tabs",
								paymentMethodOrder: ["card"],
								wallets: {
									applePay: "never",
									googlePay: "never",
								},
							}}
						/>
					</div>

					{/* Error Display */}
					{error && (
						<Alert className="border-red-200 bg-red-50">
							<AlertCircle className="w-4 h-4 text-red-600" />
							<AlertDescription className="text-red-700">{error}</AlertDescription>
						</Alert>
					)}

					{/* Action Buttons */}
					<div className="space-y-2">
						<Button
							type="submit"
							disabled={!stripe || !elements || isProcessing}
							className="w-full bg-emerald-500 hover:bg-emerald-600"
						>
							{isProcessing ? (
								<>
									<Loader2 className="w-4 h-4 mr-2 animate-spin" />
									Autorisation en cours...
								</>
							) : (
								<>
									<Shield className="w-4 h-4 mr-2" />
									Autoriser la caution de {formatAmount(amount)}
								</>
							)}
						</Button>

						{onCancel && (
							<Button type="button" variant="outline" onClick={onCancel} className="w-full">
								Retour aux options de caution
							</Button>
						)}

						<div className="text-xs text-gray-500 text-center">
							Paiement sécurisé par Stripe. Vos informations sont protégées.
						</div>
					</div>
				</form>
			</CardContent>
		</Card>
	);
}

"use client";

import { adminApi } from "@/lib/api-client";
import { useAuth } from "@/lib/auth-context";
import { Service, ServiceInsert } from "@/lib/types";
import { AlertCircle, Clock, Edit, Loader2, Plus, Save, Tag, Trash2, Users, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import ImageUpload from "./ImageUpload";
import Button from "./ui/Button";

interface Employee {
	id: string;
	first_name: string;
	last_name: string;
	email: string;
}

interface Equipment {
	id: string;
	name: string;
	description: string;
	total_capacity: number;
	is_active: boolean;
}

interface ServiceWithPricing extends Service {
	pricing_tiers?: any[];
	id: string;
	name: string;
	description: string | null;
	duration_minutes: number;
	base_price: number;
	max_participants: number;
	category: string | null;
	image_url: string | null;
	features: string[] | null;
	is_active: boolean;
}

const AdminServices = () => {
	const { user, session, loading: authLoading } = useAuth();
	const router = useRouter();
	const [services, setServices] = useState<ServiceWithPricing[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isCreating, setIsCreating] = useState(false);
	const [editForm, setEditForm] = useState<Partial<ServiceInsert>>({});
	const [saving, setSaving] = useState(false);
	const [employees, setEmployees] = useState<Employee[]>([]);
	const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
	const [equipment, setEquipment] = useState<Equipment[]>([]);
	const [selectedEquipment, setSelectedEquipment] = useState<
		{ equipmentId: string; capacityPerParticipant: number }[]
	>([]);

	useEffect(() => {
		// Only fetch when auth is ready and user is authenticated
		if (!authLoading && user && session) {
			fetchServices();
			fetchEmployees();
			fetchEquipment();
		} else if (!authLoading && !user) {
			// Auth is ready but no user - stop loading
			setLoading(false);
		}
	}, [authLoading, user, session]);

	const fetchEmployees = async () => {
		// Don't fetch if user is not authenticated
		if (!user || !session) return;

		try {
			const response = await adminApi.getEmployees({ limit: 100 });
			if (response?.employees) {
				setEmployees(response.employees);
			}
		} catch (err) {
			console.error("Error fetching employees:", err);
		}
	};

	const fetchEquipment = async () => {
		// Don't fetch if user is not authenticated
		if (!user || !session) return;

		try {
			const response = await adminApi.getEquipment({ limit: 100 });
			if (response?.equipment) {
				setEquipment(response.equipment);
			}
		} catch (err) {
			console.error("Error fetching equipment:", err);
		}
	};

	const fetchServices = async () => {
		// Don't fetch if user is not authenticated
		if (!user || !session) {
			setLoading(false);
			return;
		}

		try {
			setLoading(true);
			setError(null);
			const response = await adminApi.getServices();
			if (response?.services) {
				setServices(response.services);
			}
		} catch (err) {
			console.error("Error fetching services:", err);
			setError("Erreur lors du chargement des services");
		} finally {
			setLoading(false);
		}
	};

	const handleCreate = () => {
		setIsCreating(true);
		setEditForm({
			name: "",
			description: "",
			duration_minutes: 120,
			base_price: 0,
			max_participants: 10,
			category: "",
			image_url: "",
			features: [],
			is_active: true,
			requires_employee: true,
			fixed_price: false,
		});
	};

	const handleSave = async () => {
		if (!editForm.name || !editForm.duration_minutes || !editForm.base_price || !editForm.max_participants) {
			setError("Veuillez remplir tous les champs obligatoires");
			return;
		}

		try {
			setSaving(true);
			setError(null);

			if (isCreating) {
				const response = await adminApi.createService(editForm);
				const serviceId = response.service?.id;

				// Create employee qualifications if service was created successfully
				if (serviceId && selectedEmployees.length > 0) {
					for (const employeeId of selectedEmployees) {
						try {
							await adminApi.createEmployeeServiceQualification({
								employee_id: employeeId,
								service_id: serviceId,
								is_active: true,
							});
						} catch (qualError) {
							console.error("Error creating employee qualification:", qualError);
						}
					}
				}

				// Create equipment requirements if service was created successfully
				if (serviceId && selectedEquipment.length > 0) {
					for (const equipment of selectedEquipment) {
						if (equipment.equipmentId) {
							try {
								await adminApi.createServiceEquipmentRequirement({
									service_id: serviceId,
									equipment_id: equipment.equipmentId,
									capacity_per_participant: equipment.capacityPerParticipant,
								});
							} catch (equipError) {
								console.error("Error creating equipment requirement:", equipError);
							}
						}
					}
				}

				setIsCreating(false);
			}

			setEditForm({});
			setSelectedEmployees([]);
			setSelectedEquipment([]);
			setIsCreating(false);
			await fetchServices(); // Refresh the list
		} catch (err) {
			console.error("Error saving service:", err);
			setError("Erreur lors de la sauvegarde du service");
		} finally {
			setSaving(false);
		}
	};

	const handleCancel = () => {
		setIsCreating(false);
		setEditForm({});
		setSelectedEmployees([]);
		setSelectedEquipment([]);
		setError(null);
	};

	const handleDelete = async (serviceId: string) => {
		if (window.confirm("Êtes-vous sûr de vouloir supprimer ce service ?")) {
			try {
				await adminApi.deleteService(serviceId);
				await fetchServices(); // Refresh the list
			} catch (err) {
				console.error("Error deleting service:", err);
				setError("Erreur lors de la suppression du service");
			}
		}
	};

	const handleInputChange = (field: keyof ServiceInsert, value: any) => {
		setEditForm((prev: Partial<ServiceInsert>) => ({ ...prev, [field]: value }));
	};

	const addFeature = () => {
		const features = Array.isArray(editForm.features) ? editForm.features : [];
		setEditForm((prev: Partial<ServiceInsert>) => ({ ...prev, features: [...features, ""] }));
	};

	const updateFeature = (index: number, value: string) => {
		const features = Array.isArray(editForm.features) ? [...editForm.features] : [];
		features[index] = value;
		setEditForm((prev: Partial<ServiceInsert>) => ({ ...prev, features }));
	};

	const removeFeature = (index: number) => {
		const features = Array.isArray(editForm.features) ? [...editForm.features] : [];
		features.splice(index, 1);
		setEditForm((prev: Partial<ServiceInsert>) => ({ ...prev, features }));
	};

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Activités</h1>
					<p className="text-gray-600">Gérez vos excursions et activités</p>
				</div>
				<Button onClick={handleCreate} icon={Plus}>
					Nouvelle Activité
				</Button>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Loading State */}
			{loading && (
				<div className="flex items-center justify-center h-64">
					<div className="flex items-center space-x-2">
						<Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
						<span className="text-gray-600">Chargement des services...</span>
					</div>
				</div>
			)}

			{/* Create Form */}
			{isCreating && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
					<h2 className="text-xl font-bold text-gray-900 mb-6">Créer un nouveau service</h2>

					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<div className="space-y-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Nom du service *</label>
								<input
									type="text"
									value={editForm.name || ""}
									onChange={(e) => handleInputChange("name", e.target.value)}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
								<textarea
									rows={4}
									value={editForm.description || ""}
									onChange={(e) => handleInputChange("description", e.target.value)}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										Prix de base (€) *
									</label>
									<input
										type="number"
										step="0.01"
										value={editForm.base_price || 0}
										onChange={(e) => handleInputChange("base_price", parseFloat(e.target.value))}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										Durée (minutes) *
									</label>
									<input
										type="number"
										value={editForm.duration_minutes || 120}
										onChange={(e) =>
											handleInputChange("duration_minutes", parseInt(e.target.value))
										}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">Catégorie *</label>
									<input
										type="text"
										value={editForm.category || ""}
										onChange={(e) => handleInputChange("category", e.target.value)}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										Participants max *
									</label>
									<input
										type="number"
										value={editForm.max_participants || 10}
										onChange={(e) =>
											handleInputChange("max_participants", parseInt(e.target.value))
										}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
							</div>

							<div className="space-y-3">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										<input
											type="checkbox"
											checked={editForm.is_active ?? true}
											onChange={(e) => handleInputChange("is_active", e.target.checked)}
											className="mr-2"
										/>
										Activité active
									</label>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										<input
											type="checkbox"
											checked={editForm.requires_employee ?? true}
											onChange={(e) => handleInputChange("requires_employee", e.target.checked)}
											className="mr-2"
										/>
										Nécessite un employé
									</label>
									<p className="text-xs text-gray-500 mt-1">
										Décochez si le service ne nécessite qu'un équipement (ex: location de matériel)
									</p>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										<input
											type="checkbox"
											checked={editForm.fixed_price ?? false}
											onChange={(e) => handleInputChange("fixed_price", e.target.checked)}
											className="mr-2"
										/>
										Prix fixe
									</label>
									<p className="text-xs text-gray-500 mt-1">
										Le prix ne varie pas selon le nombre de participants
									</p>
								</div>

								{/* Qualified Employees Selection */}
								{editForm.requires_employee && (
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-2">
											Employés qualifiés
										</label>
										<div className="space-y-2 max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-3">
											{employees.length > 0 ? (
												employees.map((employee) => (
													<label key={employee.id} className="flex items-center text-sm">
														<input
															type="checkbox"
															checked={selectedEmployees.includes(employee.id)}
															onChange={(e) => {
																if (e.target.checked) {
																	setSelectedEmployees([
																		...selectedEmployees,
																		employee.id,
																	]);
																} else {
																	setSelectedEmployees(
																		selectedEmployees.filter(
																			(id) => id !== employee.id
																		)
																	);
																}
															}}
															className="mr-2 rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
														/>
														{employee.first_name} {employee.last_name}
														<span className="text-gray-500 ml-1">({employee.email})</span>
													</label>
												))
											) : (
												<p className="text-sm text-gray-500">Aucun employé disponible</p>
											)}
										</div>
										<p className="text-xs text-gray-500 mt-1">
											Sélectionnez les employés qualifiés pour ce service
										</p>
									</div>
								)}

								{/* Equipment Requirements */}
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										Équipements requis
									</label>
									<div className="space-y-3">
										{selectedEquipment.map((item, index) => (
											<div
												key={index}
												className="flex items-center gap-3 p-3 border border-gray-300 rounded-lg"
											>
												<select
													value={item.equipmentId}
													onChange={(e) => {
														const newSelected = [...selectedEquipment];
														newSelected[index].equipmentId = e.target.value;
														setSelectedEquipment(newSelected);
													}}
													className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
												>
													<option value="">Sélectionner un équipement</option>
													{equipment
														.filter((eq) => eq.is_active)
														.map((eq) => (
															<option key={eq.id} value={eq.id}>
																{eq.name} (Capacité: {eq.total_capacity})
															</option>
														))}
												</select>
												<div className="flex items-center gap-2">
													<label className="text-sm text-gray-600">
														Capacité/participant:
													</label>
													<input
														type="number"
														min="1"
														value={item.capacityPerParticipant}
														onChange={(e) => {
															const newSelected = [...selectedEquipment];
															newSelected[index].capacityPerParticipant =
																parseInt(e.target.value) || 1;
															setSelectedEquipment(newSelected);
														}}
														className="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
													/>
												</div>
												<button
													type="button"
													onClick={() => {
														setSelectedEquipment(
															selectedEquipment.filter((_, i) => i !== index)
														);
													}}
													className="text-red-600 hover:text-red-800"
												>
													<X className="w-4 h-4" />
												</button>
											</div>
										))}
										<button
											type="button"
											onClick={() => {
												setSelectedEquipment([
													...selectedEquipment,
													{ equipmentId: "", capacityPerParticipant: 1 },
												]);
											}}
											className="flex items-center gap-2 px-3 py-2 text-sm text-emerald-600 border border-emerald-300 rounded-lg hover:bg-emerald-50"
										>
											<Plus className="w-4 h-4" />
											Ajouter un équipement
										</button>
									</div>
									<p className="text-xs text-gray-500 mt-1">
										Définissez les équipements nécessaires et leur capacité par participant
									</p>
								</div>
							</div>
						</div>

						<div className="space-y-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Image du service</label>
								<ImageUpload
									value={editForm.image_url || ""}
									onChange={(url) => handleInputChange("image_url", url)}
									disabled={saving}
								/>
							</div>

							<div>
								<div className="flex justify-between items-center mb-2">
									<label className="block text-sm font-medium text-gray-700">
										Caractéristiques incluses
									</label>
									<button
										onClick={addFeature}
										className="text-emerald-600 hover:text-emerald-700 text-sm font-medium"
									>
										+ Ajouter
									</button>
								</div>
								<div className="space-y-2">
									{(Array.isArray(editForm.features) ? editForm.features : []).map(
										(feature: any, index: number) => (
											<div key={index} className="flex gap-2">
												<input
													type="text"
													value={feature}
													onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
														updateFeature(index, e.target.value)
													}
													className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
													placeholder="Ex: Guide expert"
												/>
												<button
													onClick={() => removeFeature(index)}
													className="p-2 text-red-600 hover:text-red-700"
												>
													<X className="h-4 w-4" />
												</button>
											</div>
										)
									)}
								</div>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Règles de disponibilité
								</label>
								<div className="space-y-3 p-4 bg-gray-50 rounded-lg">
									<div className="flex items-center justify-between">
										<span className="text-sm text-gray-700">Temps de préparation</span>
										<select className="px-3 py-1 border border-gray-300 rounded text-sm">
											<option>30 minutes</option>
											<option>1 heure</option>
											<option>2 heures</option>
										</select>
									</div>
									<div className="flex items-center justify-between">
										<span className="text-sm text-gray-700">Réservation minimum</span>
										<select className="px-3 py-1 border border-gray-300 rounded text-sm">
											<option>6 heures</option>
											<option>12 heures</option>
											<option>24 heures</option>
											<option>48 heures</option>
											<option>72 heures</option>
										</select>
									</div>
									<div className="flex items-center justify-between">
										<span className="text-sm text-gray-700">Annulation gratuite</span>
										<select className="px-3 py-1 border border-gray-300 rounded text-sm">
											<option>24 heures</option>
											<option>48 heures</option>
											<option>72 heures</option>
										</select>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div className="flex justify-end gap-4 mt-6 pt-6 border-t">
						<Button variant="outline" onClick={handleCancel} icon={X} disabled={saving}>
							Annuler
						</Button>
						<Button onClick={handleSave} icon={saving ? Loader2 : Save} disabled={saving}>
							{saving ? "Sauvegarde..." : "Créer le service"}
						</Button>
					</div>
				</div>
			)}

			{/* Services List */}
			{!loading && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200">
					<div className="p-6 border-b border-gray-200">
						<h2 className="text-xl font-bold text-gray-900">Services ({services.length})</h2>
					</div>
					<div className="divide-y divide-gray-200">
						{services.length === 0 ? (
							<div className="p-12 text-center">
								<p className="text-gray-500">Aucun service trouvé</p>
							</div>
						) : (
							services.map((service) => (
								<div key={service.id} className="p-6 flex items-center gap-4">
									<img
										src={service.image_url || "/placeholder-service.jpg"}
										alt={service.name}
										className="w-16 h-16 object-cover rounded-lg"
										onError={(e) => {
											e.currentTarget.src = "/placeholder-service.jpg";
										}}
									/>
									<div className="flex-1">
										<div className="flex items-center gap-2">
											<h3 className="text-lg font-semibold text-gray-900">{service.name}</h3>
											{!service.is_active && (
												<span className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded-full">
													Inactif
												</span>
											)}
										</div>
										<p className="text-gray-600 text-sm mt-1 line-clamp-2">
											{service.description || "Aucune description"}
										</p>
										<div className="flex items-center gap-6 mt-3 text-sm text-gray-500">
											<span className="flex items-center gap-1">
												<Clock className="h-4 w-4" />
												{service.duration_minutes} min
											</span>
											<span className="flex items-center gap-1">
												<Users className="h-4 w-4" />
												{service.max_participants} pers.
											</span>
											<span className="flex items-center gap-1">
												<Tag className="h-4 w-4" />
												{service.category || "Non catégorisé"}
											</span>
											<span className="text-2xl font-bold text-emerald-600">
												€{service.base_price?.toFixed(2) || "0.00"}
											</span>
										</div>
									</div>
									<div className="flex gap-2">
										<Button
											variant="outline"
											size="sm"
											icon={Edit}
											onClick={() => router.push(`/admin/services/${service.id}`)}
										>
											Modifier
										</Button>
										<Button
											variant="outline"
											size="sm"
											icon={Trash2}
											onClick={() => handleDelete(service.id)}
											className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
											disabled={saving}
										>
											Supprimer
										</Button>
									</div>
								</div>
							))
						)}
					</div>
				</div>
			)}
		</div>
	);
};

export default AdminServices;

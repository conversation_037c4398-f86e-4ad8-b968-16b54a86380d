"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { adminApi } from "@/lib/api-client";
import { Edit, Plus, Save, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";

interface ServiceSchedulingRule {
	id: string;
	service_id: string;
	days_of_week: number[] | null; // Changed to array of days
	min_advance_booking_hours: number;
	max_advance_booking_days: number;
	operating_start_time: string;
	operating_end_time: string;
	booking_interval_minutes: number | null;
	specific_times: string[] | null;
	max_bookings_per_day: number | null;
	is_active: boolean;
	created_at: string;
	updated_at: string;
}

interface ServiceSchedulingRulesProps {
	serviceId: string;
	serviceName: string;
	serviceDuration?: number; // Duration in minutes for validation
}

const DAYS_OF_WEEK = [
	{ value: null, label: "Tous les jours" },
	{ value: 0, label: "Dimanche" },
	{ value: 1, label: "Lundi" },
	{ value: 2, label: "Mardi" },
	{ value: 3, label: "Mercredi" },
	{ value: 4, label: "Jeudi" },
	{ value: 5, label: "Vendredi" },
	{ value: 6, label: "Samedi" },
];

export default function ServiceSchedulingRules({
	serviceId,
	serviceName,
	serviceDuration = 60,
}: ServiceSchedulingRulesProps) {
	const [rules, setRules] = useState<ServiceSchedulingRule[]>([]);
	const [loading, setLoading] = useState(true);
	const [editing, setEditing] = useState<string | null>(null);
	const [creating, setCreating] = useState(false);
	const [formData, setFormData] = useState<Partial<ServiceSchedulingRule>>({});

	useEffect(() => {
		fetchRules();
	}, [serviceId]);

	const fetchRules = async () => {
		try {
			const data = await adminApi.getServiceSchedulingRules(serviceId);
			setRules(data.rules || []);
		} catch (error) {
			console.error("Error fetching scheduling rules:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleCreate = () => {
		setCreating(true);
		setFormData({
			service_id: serviceId,
			days_of_week: null, // null means all days
			min_advance_booking_hours: 2,
			max_advance_booking_days: 30,
			operating_start_time: "08:00",
			operating_end_time: "18:00",
			booking_interval_minutes: 60, // Default to interval scheduling
			specific_times: null, // Start with interval scheduling
			max_bookings_per_day: null,
			is_active: true,
		});
	};

	const handleEdit = (rule: ServiceSchedulingRule) => {
		setEditing(rule.id);
		setFormData({ ...rule });
	};

	const handleSave = async () => {
		try {
			if (creating) {
				await adminApi.createServiceSchedulingRule(serviceId, formData);
			} else {
				await adminApi.updateServiceSchedulingRule(serviceId, editing!, formData);
			}

			await fetchRules();
			handleCancel();
		} catch (error) {
			console.error("Error saving rule:", error);
		}
	};

	const handleDelete = async (ruleId: string) => {
		if (confirm("Êtes-vous sûr de vouloir supprimer cette règle ?")) {
			try {
				await adminApi.deleteServiceSchedulingRule(serviceId, ruleId);
				await fetchRules();
			} catch (error) {
				console.error("Error deleting rule:", error);
			}
		}
	};

	const handleCancel = () => {
		setCreating(false);
		setEditing(null);
		setFormData({});
	};

	const formatDaysOfWeek = (daysOfWeek: number[] | null) => {
		if (!daysOfWeek || daysOfWeek.length === 0) {
			return "Tous les jours";
		}

		if (daysOfWeek.length === 7) {
			return "Tous les jours";
		}

		const dayLabels = daysOfWeek
			.sort()
			.map((day) => DAYS_OF_WEEK.find((d) => d.value === day)?.label)
			.filter(Boolean);

		return dayLabels.join(", ");
	};

	const formatScheduleType = (rule: ServiceSchedulingRule) => {
		if (rule.specific_times && rule.specific_times.length > 0) {
			return `Créneaux fixes: ${rule.specific_times.join(", ")}`;
		}
		if (rule.booking_interval_minutes) {
			return `Créneaux par intervalles de ${rule.booking_interval_minutes} min`;
		}
		return "Type de planification non configuré";
	};

	if (loading) {
		return <div className="p-4">Chargement...</div>;
	}

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<div>
					<h3 className="text-lg font-semibold">Règles de planification</h3>
					<p className="text-sm text-gray-600">Service: {serviceName}</p>
				</div>
				<Button onClick={handleCreate} className="flex items-center gap-2">
					<Plus className="w-4 h-4" />
					Nouvelle règle
				</Button>
			</div>

			{/* Rules List */}
			<div className="space-y-4">
				{rules.map((rule) => (
					<Card key={rule.id}>
						<CardContent className="p-4">
							{editing === rule.id ? (
								<RuleForm
									formData={formData}
									setFormData={setFormData}
									onSave={handleSave}
									onCancel={handleCancel}
									serviceDuration={serviceDuration}
								/>
							) : (
								<div className="flex justify-between items-start">
									<div className="space-y-2">
										<div className="flex items-center gap-2">
											<Badge variant={rule.is_active ? "default" : "secondary"}>
												{rule.is_active ? "Actif" : "Inactif"}
											</Badge>
											<span className="font-medium">{formatDaysOfWeek(rule.days_of_week)}</span>
										</div>
										<div className="text-sm text-gray-600">
											<div>
												Horaires: {rule.operating_start_time} - {rule.operating_end_time}
											</div>
											<div>{formatScheduleType(rule)}</div>
											<div>
												Réservation: {rule.min_advance_booking_hours}h à{" "}
												{rule.max_advance_booking_days}j à l'avance
											</div>
											{rule.max_bookings_per_day && (
												<div>Max {rule.max_bookings_per_day} réservations/jour</div>
											)}
										</div>
									</div>
									<div className="flex gap-2">
										<Button variant="outline" size="sm" onClick={() => handleEdit(rule)}>
											<Edit className="w-4 h-4" />
										</Button>
										<Button variant="outline" size="sm" onClick={() => handleDelete(rule.id)}>
											<Trash2 className="w-4 h-4" />
										</Button>
									</div>
								</div>
							)}
						</CardContent>
					</Card>
				))}
			</div>

			{/* Create Form */}
			{creating && (
				<Card>
					<CardHeader>
						<CardTitle>Nouvelle règle de planification</CardTitle>
					</CardHeader>
					<CardContent>
						<RuleForm
							formData={formData}
							setFormData={setFormData}
							onSave={handleSave}
							onCancel={handleCancel}
							serviceDuration={serviceDuration}
						/>
					</CardContent>
				</Card>
			)}
		</div>
	);
}

interface RuleFormProps {
	formData: Partial<ServiceSchedulingRule>;
	setFormData: (data: Partial<ServiceSchedulingRule>) => void;
	onSave: () => void;
	onCancel: () => void;
	serviceDuration: number;
}

function RuleForm({ formData, setFormData, onSave, onCancel, serviceDuration }: RuleFormProps) {
	const toggleDaySelection = (dayValue: number) => {
		const currentDays = formData.days_of_week || [];
		const isSelected = currentDays.includes(dayValue);

		if (isSelected) {
			// Remove day from selection
			const newDays = currentDays.filter((d) => d !== dayValue);
			setFormData({
				...formData,
				days_of_week: newDays.length === 0 ? null : newDays,
			});
		} else {
			// Add day to selection
			setFormData({
				...formData,
				days_of_week: [...currentDays, dayValue],
			});
		}
	};

	const toggleAllDays = () => {
		if (formData.days_of_week === null) {
			// Currently "all days", switch to empty selection
			setFormData({ ...formData, days_of_week: [] });
		} else {
			// Switch to "all days"
			setFormData({ ...formData, days_of_week: null });
		}
	};

	// Helper function to check if a time slot overlaps with existing ones
	const isTimeSlotValid = (newTime: string, currentIndex: number = -1) => {
		if (!formData.specific_times || !serviceDuration) return true;

		const newStartMinutes = timeToMinutes(newTime);
		const newEndMinutes = newStartMinutes + serviceDuration;

		return formData.specific_times.every((existingTime, index) => {
			if (index === currentIndex) return true; // Skip the current time being edited

			const existingStartMinutes = timeToMinutes(existingTime);
			const existingEndMinutes = existingStartMinutes + serviceDuration;

			// Check if new slot overlaps with existing slot
			return newEndMinutes <= existingStartMinutes || newStartMinutes >= existingEndMinutes;
		});
	};

	// Convert time string (HH:MM) to minutes since midnight
	const timeToMinutes = (timeStr: string): number => {
		const [hours, minutes] = timeStr.split(":").map(Number);
		return hours * 60 + minutes;
	};

	return (
		<div className="space-y-4">
			<div className="grid grid-cols-2 gap-4">
				<div>
					<Label>Jours de la semaine</Label>
					<div className="space-y-3 mt-2">
						{/* All days option */}
						<div className="flex items-center space-x-2">
							<input
								type="checkbox"
								id="all-days"
								checked={formData.days_of_week === null}
								onChange={toggleAllDays}
								className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
							/>
							<Label htmlFor="all-days" className="font-medium">
								Tous les jours
							</Label>
						</div>

						{/* Individual days */}
						<div className="grid grid-cols-2 gap-2 pl-6">
							{DAYS_OF_WEEK.slice(1).map((day) => (
								<div key={day.value} className="flex items-center space-x-2">
									<input
										type="checkbox"
										id={`day-${day.value}`}
										checked={
											formData.days_of_week === null ||
											(formData.days_of_week && formData.days_of_week.includes(day.value!))
										}
										disabled={formData.days_of_week === null}
										onChange={() => toggleDaySelection(day.value!)}
										className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
									/>
									<Label htmlFor={`day-${day.value}`} className="text-sm">
										{day.label}
									</Label>
								</div>
							))}
						</div>
					</div>
				</div>

				<div className="flex items-center space-x-2">
					<Switch
						checked={formData.is_active || false}
						onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
					/>
					<Label>Règle active</Label>
				</div>
			</div>

			{/* Booking constraints - moved before scheduling type */}
			<div className="grid grid-cols-3 gap-4">
				<div>
					<Label>Réservation min (heures)</Label>
					<Input
						type="number"
						value={formData.min_advance_booking_hours || ""}
						onChange={(e) =>
							setFormData({ ...formData, min_advance_booking_hours: parseInt(e.target.value) })
						}
					/>
				</div>
				<div>
					<Label>Réservation max (jours)</Label>
					<Input
						type="number"
						value={formData.max_advance_booking_days || ""}
						onChange={(e) =>
							setFormData({ ...formData, max_advance_booking_days: parseInt(e.target.value) })
						}
					/>
				</div>
				<div>
					<Label>Max réservations/jour</Label>
					<Input
						type="number"
						value={formData.max_bookings_per_day || ""}
						onChange={(e) =>
							setFormData({
								...formData,
								max_bookings_per_day: e.target.value ? parseInt(e.target.value) : null,
							})
						}
					/>
				</div>
			</div>

			{/* Scheduling Type Selection */}
			<div>
				<Label>Type de planification</Label>
				<div className="space-y-3 mt-2">
					<div className="flex items-center space-x-2">
						<input
							type="radio"
							id="interval-scheduling"
							name="scheduling-type"
							checked={!formData.specific_times || formData.specific_times.length === 0}
							onChange={() =>
								setFormData({
									...formData,
									specific_times: null,
									booking_interval_minutes: formData.booking_interval_minutes || 60,
								})
							}
							className="text-emerald-600 focus:ring-emerald-500"
						/>
						<Label htmlFor="interval-scheduling">Créneaux par intervalles</Label>
					</div>
					<div className="flex items-center space-x-2">
						<input
							type="radio"
							id="fixed-scheduling"
							name="scheduling-type"
							checked={Boolean(formData.specific_times && formData.specific_times.length > 0)}
							onChange={() =>
								setFormData({ ...formData, specific_times: ["09:00"], booking_interval_minutes: null })
							}
							className="text-emerald-600 focus:ring-emerald-500"
						/>
						<Label htmlFor="fixed-scheduling">Créneaux à heures fixes</Label>
					</div>
				</div>
			</div>

			{/* Operating hours - applies to both scheduling types */}
			<div className="grid grid-cols-2 gap-4">
				<div>
					<Label>Heure de début</Label>
					<Input
						type="time"
						value={formData.operating_start_time || ""}
						onChange={(e) => setFormData({ ...formData, operating_start_time: e.target.value })}
					/>
				</div>
				<div>
					<Label>Heure de fin</Label>
					<Input
						type="time"
						value={formData.operating_end_time || ""}
						onChange={(e) => setFormData({ ...formData, operating_end_time: e.target.value })}
					/>
				</div>
			</div>

			{/* Interval Scheduling Fields */}
			{(!formData.specific_times || formData.specific_times.length === 0) && (
				<div>
					<Label>Intervalle de réservation (minutes)</Label>
					<Input
						type="number"
						value={formData.booking_interval_minutes || ""}
						onChange={(e) =>
							setFormData({
								...formData,
								booking_interval_minutes: e.target.value ? parseInt(e.target.value) : null,
							})
						}
						placeholder="Ex: 60 pour des créneaux d'1 heure"
					/>
				</div>
			)}

			{/* Fixed Times Scheduling Fields */}
			{formData.specific_times && formData.specific_times.length > 0 && (
				<div>
					<Label>Heures fixes</Label>
					<p className="text-sm text-gray-600 mb-2">
						Durée du service: {serviceDuration} minutes. Les créneaux ne doivent pas se chevaucher.
					</p>
					<div className="space-y-2">
						{formData.specific_times.map((time, index) => {
							const isValid = isTimeSlotValid(time, index);
							return (
								<div key={index} className="flex items-center gap-2">
									<Input
										type="time"
										value={time}
										onChange={(e) => {
											const newTime = e.target.value;
											if (isTimeSlotValid(newTime, index)) {
												const newTimes = [...formData.specific_times!];
												newTimes[index] = newTime;
												setFormData({ ...formData, specific_times: newTimes });
											}
										}}
										className={!isValid ? "border-red-500" : ""}
									/>
									{!isValid && (
										<span className="text-red-500 text-sm">Conflit avec un autre créneau</span>
									)}
									<Button
										type="button"
										variant="outline"
										size="sm"
										onClick={() => {
											const newTimes = formData.specific_times!.filter((_, i) => i !== index);
											setFormData({
												...formData,
												specific_times: newTimes.length > 0 ? newTimes : null,
											});
										}}
									>
										<X className="w-4 h-4" />
									</Button>
								</div>
							);
						})}
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() => {
								const newTimes = [...(formData.specific_times || []), "09:00"];
								setFormData({ ...formData, specific_times: newTimes });
							}}
							className="flex items-center gap-2"
						>
							<Plus className="w-4 h-4" />
							Ajouter une heure
						</Button>
					</div>
				</div>
			)}

			<div className="flex gap-2">
				<Button onClick={onSave} className="flex items-center gap-2">
					<Save className="w-4 h-4" />
					Sauvegarder
				</Button>
				<Button variant="outline" onClick={onCancel} className="flex items-center gap-2">
					<X className="w-4 h-4" />
					Annuler
				</Button>
			</div>
		</div>
	);
}

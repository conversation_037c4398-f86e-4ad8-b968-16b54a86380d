"use client";

import { useVideoLoading } from "@/hooks/use-video-loading";
import { useEffect, useRef, useState } from "react";

interface VideoBackgroundProps {
	src: string;
	poster: string;
	fallbackImage: string;
	className?: string;
	style?: React.CSSProperties;
	showLoadingIndicator?: boolean;
}

export default function VideoBackground({
	src,
	poster,
	fallbackImage,
	className = "",
	style = {},
	showLoadingIndicator = false,
}: VideoBackgroundProps) {
	const containerRef = useRef<HTMLDivElement>(null);
	const [isInViewport, setIsInViewport] = useState(false);

	// Intersection Observer to detect when video is in viewport
	useEffect(() => {
		const observer = new IntersectionObserver(
			([entry]) => {
				setIsInViewport(entry.isIntersecting);
			},
			{ threshold: 0.1 }
		);

		if (containerRef.current) {
			observer.observe(containerRef.current);
		}

		return () => observer.disconnect();
	}, []);

	const { videoProps, isLoaded, hasError } = useVideoLoading({
		src: isInViewport ? src : "", // Only load video when in viewport
		autoPlay: true,
		muted: true,
		loop: true,
		playsInline: true,
	});

	return (
		<div ref={containerRef} className="absolute top-0 left-0 w-full h-full">
			{/* Background Image (always visible as fallback) */}
			<div
				className="absolute top-0 left-0 w-full h-full bg-cover bg-center bg-no-repeat transition-opacity duration-500"
				style={{
					backgroundImage: `url('${fallbackImage}')`,
					zIndex: 1,
					opacity: isLoaded && !hasError ? 0.3 : 1, // Fade out when video loads
				}}
			/>

			{/* Video Background */}
			{isInViewport && !hasError && (
				<video
					{...videoProps}
					poster={poster}
					className={`absolute top-0 left-0 w-full h-full object-cover ${className}`}
					style={{
						zIndex: 2,
						...videoProps.style,
						...style,
					}}
				>
					<source src={src} type="video/mp4" />
					Your browser does not support the video tag.
				</video>
			)}

			{/* Loading indicator */}
			{showLoadingIndicator && isInViewport && !isLoaded && !hasError && (
				<div
					className="absolute top-0 left-0 w-full h-full flex items-center justify-center"
					style={{ zIndex: 3 }}
				>
					<div className="bg-black/30 backdrop-blur-sm rounded-lg p-3">
						<div className="flex items-center space-x-2 text-white">
							<div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
							<span className="text-xs">Chargement...</span>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}

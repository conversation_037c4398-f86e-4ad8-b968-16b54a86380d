"use client";

import ClientOnly from "@/components/ClientOnly";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export const dynamic = "force-dynamic";

function AdminPageContent() {
	const router = useRouter();

	useEffect(() => {
		// Redirect to admin dashboard
		router.push("/admin/dashboard");
	}, [router]);

	return (
		<div className="flex items-center justify-center min-h-screen">
			<div className="text-center">
				<h1 className="text-2xl font-bold text-gray-900 mb-4">Redirection vers l'administration...</h1>
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
			</div>
		</div>
	);
}

export default function AdminPage() {
	return (
		<ClientOnly
			fallback={
				<div className="flex items-center justify-center min-h-screen">
					<div className="text-center">
						<h1 className="text-2xl font-bold text-gray-900 mb-4">Chargement...</h1>
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
					</div>
				</div>
			}
		>
			<AdminPageContent />
		</ClientOnly>
	);
}

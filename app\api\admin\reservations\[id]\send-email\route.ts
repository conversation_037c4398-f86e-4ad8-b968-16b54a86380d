import { NextRequest, NextResponse } from "next/server";
import { withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import {
	sendBookingConfirmationEmail,
	sendPaymentConfirmationEmail,
	sendBookingReminderEmail,
	sendAdminNewReservationNotification,
	sendAdminPaymentReceivedNotification,
} from "@/lib/email-service";
import { generateBookingConfirmationPDF } from "@/lib/pdf-generator";
import { generateInvoicePDF, createInvoiceData } from "@/lib/invoice-generator";
import { generateBookingQRCode, generateVerificationUrl } from "@/lib/qr-generator";
import { appConfig } from "@/lib/config";

export interface ManualEmailRequest {
	emailType:
		| "booking_confirmation"
		| "payment_confirmation"
		| "booking_reminder"
		| "admin_new_reservation"
		| "admin_payment_received";
	paymentId?: string; // Required for payment_confirmation and admin_payment_received
}

// POST /api/admin/reservations/[id]/send-email - Send manual email for reservation
export const POST = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const reservationId = params.id;
		const { emailType, paymentId }: ManualEmailRequest = await request.json();

		console.log(`=== MANUAL EMAIL TRIGGER ===`);
		console.log(`Admin ${user.email} requesting ${emailType} email for reservation ${reservationId}`);

		if (!emailType) {
			return NextResponse.json({ error: "Email type is required" }, { status: 400 });
		}

		// Get reservation details with customer and service info
		const { data: reservation, error: reservationError } = await supabaseAdmin
			.from("reservations")
			.select(
				`
				*,
				customer:customers(first_name, last_name, email, phone),
				service:services(name, requires_security_deposit, security_deposit_amount),
				payments!payments_reservation_id_fkey(*)
			`
			)
			.eq("id", reservationId)
			.single();

		if (reservationError || !reservation) {
			console.error("Error fetching reservation:", reservationError);
			return NextResponse.json({ error: "Reservation not found" }, { status: 404 });
		}

		if (!reservation.customer) {
			return NextResponse.json({ error: "Customer information not found" }, { status: 400 });
		}

		const customerName = `${reservation.customer.first_name} ${reservation.customer.last_name}`;
		const serviceName = reservation.service?.name || "Service";
		const date = new Date(reservation.start_time).toLocaleDateString("fr-FR");
		const time = new Date(reservation.start_time).toLocaleTimeString("fr-FR", {
			hour: "2-digit",
			minute: "2-digit",
		});

		let emailResult: { success: boolean; messageId?: string; error?: string };

		switch (emailType) {
			case "booking_confirmation":
				try {
					// Generate PDF attachment
					let pdfBuffer: Buffer | undefined;
					try {
						const qrCode = await generateBookingQRCode(reservation.qr_code);
						const verificationUrl = generateVerificationUrl(reservation.qr_code);

						const pdf = await generateBookingConfirmationPDF({
							reservationNumber: reservation.reservation_number,
							customerName,
							customerEmail: reservation.customer.email,
							customerPhone: reservation.customer.phone,
							serviceName,
							date,
							time,
							participants: reservation.participant_count,
							totalAmount: reservation.total_amount,
							specialRequests: reservation.special_requests,
							qrCodeData: {
								reservationId: reservation.id,
								reservationNumber: reservation.reservation_number,
								customerName,
								serviceName,
								date,
								time,
								participants: reservation.participant_count,
								totalAmount: reservation.total_amount,
								verificationUrl,
							},
							qrCode,
							verificationUrl,
							appUrl: appConfig.url,
						});

						pdfBuffer = Buffer.from(await pdf.arrayBuffer());
					} catch (pdfError) {
						console.error("Error generating PDF:", pdfError);
						// Continue without PDF attachment
					}

					// Get selected options from reservation
					let selectedOptions: Array<{ name: string; price: number; perParticipant?: boolean }> = [];
					if (reservation.selected_options && Array.isArray(reservation.selected_options)) {
						selectedOptions = reservation.selected_options.map((option: any) => ({
							name: option.optionName,
							price: option.totalPrice,
							perParticipant: false, // This info is not stored in the simplified format
						}));
					}

					emailResult = await sendBookingConfirmationEmail(
						reservation.customer.email,
						customerName,
						{
							reservationNumber: reservation.reservation_number,
							serviceName,
							date,
							time,
							participants: reservation.participant_count,
							totalAmount: reservation.total_amount,
							specialRequests: reservation.special_requests,
							selectedOptions,
							// Security deposit information
							securityDepositRequired: reservation.service?.requires_security_deposit || false,
							securityDepositAmount: reservation.service?.security_deposit_amount || 0,
							securityDepositStatus: reservation.security_deposit_status || "none",
						},
						pdfBuffer
					);
				} catch (error) {
					console.error("Error sending booking confirmation:", error);
					emailResult = {
						success: false,
						error: error instanceof Error ? error.message : "Failed to send booking confirmation",
					};
				}
				break;

			case "payment_confirmation":
				if (!paymentId) {
					return NextResponse.json(
						{ error: "Payment ID is required for payment confirmation emails" },
						{ status: 400 }
					);
				}

				// Get payment details
				const { data: payment, error: paymentError } = await supabaseAdmin
					.from("payments")
					.select("*")
					.eq("id", paymentId)
					.eq("reservation_id", reservationId)
					.single();

				if (paymentError || !payment) {
					return NextResponse.json({ error: "Payment not found" }, { status: 404 });
				}

				try {
					// Generate invoice PDF
					let invoiceBuffer: Buffer | undefined;
					try {
						const invoiceData = createInvoiceData(
							reservation,
							payment,
							reservation.customer,
							reservation.service
						);
						const invoicePDF = await generateInvoicePDF(invoiceData);
						invoiceBuffer = Buffer.from(await invoicePDF.arrayBuffer());
					} catch (invoiceError) {
						console.error("Error generating invoice:", invoiceError);
						// Continue without invoice attachment
					}

					emailResult = await sendPaymentConfirmationEmail(
						reservation.customer.email,
						customerName,
						{
							reservationNumber: reservation.reservation_number,
							serviceName,
							date,
							time,
							participants: reservation.participant_count,
							amount: payment.amount,
							currency: payment.currency.toUpperCase(),
							paymentDate: new Date(payment.payment_date || payment.created_at).toLocaleDateString(
								"fr-FR"
							),
							isDeposit: payment.is_deposit,
							remainingAmount: payment.is_deposit ? reservation.remaining_amount : undefined,
						},
						invoiceBuffer
					);
				} catch (error) {
					console.error("Error sending payment confirmation:", error);
					emailResult = {
						success: false,
						error: error instanceof Error ? error.message : "Failed to send payment confirmation",
					};
				}
				break;

			case "booking_reminder":
				try {
					emailResult = await sendBookingReminderEmail(reservation.customer.email, customerName, {
						reservationNumber: reservation.reservation_number,
						serviceName,
						date,
						time,
						participants: reservation.participant_count,
						totalAmount: reservation.total_amount,
					});
				} catch (error) {
					console.error("Error sending booking reminder:", error);
					emailResult = {
						success: false,
						error: error instanceof Error ? error.message : "Failed to send booking reminder",
					};
				}
				break;

			case "admin_new_reservation":
				try {
					emailResult = await sendAdminNewReservationNotification({
						reservationNumber: reservation.reservation_number,
						customerName,
						customerEmail: reservation.customer.email,
						serviceName,
						date,
						time,
						participants: reservation.participant_count,
						totalAmount: reservation.total_amount,
						specialRequests: reservation.special_requests,
						// Security deposit information
						securityDepositRequired: reservation.service?.requires_security_deposit || false,
						securityDepositAmount: reservation.service?.security_deposit_amount || 0,
						securityDepositStatus: reservation.security_deposit_status || "none",
					});
				} catch (error) {
					console.error("Error sending admin new reservation notification:", error);
					emailResult = {
						success: false,
						error: error instanceof Error ? error.message : "Failed to send admin notification",
					};
				}
				break;

			case "admin_payment_received":
				if (!paymentId) {
					return NextResponse.json(
						{ error: "Payment ID is required for admin payment notifications" },
						{ status: 400 }
					);
				}

				// Get payment details
				const { data: adminPayment, error: adminPaymentError } = await supabaseAdmin
					.from("payments")
					.select("*")
					.eq("id", paymentId)
					.eq("reservation_id", reservationId)
					.single();

				if (adminPaymentError || !adminPayment) {
					return NextResponse.json({ error: "Payment not found" }, { status: 404 });
				}

				try {
					emailResult = await sendAdminPaymentReceivedNotification({
						reservationNumber: reservation.reservation_number,
						customerName,
						amount: adminPayment.amount,
						currency: adminPayment.currency.toUpperCase(),
						paymentDate: new Date(adminPayment.payment_date || adminPayment.created_at).toLocaleDateString(
							"fr-FR"
						),
						isDeposit: adminPayment.is_deposit,
					});
				} catch (error) {
					console.error("Error sending admin payment notification:", error);
					emailResult = {
						success: false,
						error: error instanceof Error ? error.message : "Failed to send admin payment notification",
					};
				}
				break;

			default:
				return NextResponse.json({ error: "Invalid email type" }, { status: 400 });
		}

		// Log the email sending attempt
		try {
			await supabaseAdmin.from("admin_audit_log").insert({
				admin_user_id: user.id,
				action: `manual_email_${emailType}`,
				table_name: "reservations",
				record_id: reservationId,
				new_values: {
					email_type: emailType,
					recipient: reservation.customer.email,
					success: emailResult.success,
					message_id: emailResult.messageId,
					error: emailResult.error,
					payment_id: paymentId,
				},
				created_at: new Date().toISOString(),
			});
		} catch (logError) {
			console.error("Error logging email action:", logError);
			// Don't fail the request if logging fails
		}

		console.log(`Email ${emailType} result:`, emailResult);

		return NextResponse.json({
			success: emailResult.success,
			messageId: emailResult.messageId,
			error: emailResult.error,
			emailType,
			recipient: reservation.customer.email,
		});
	} catch (error) {
		console.error("Manual email sending error:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}, "reservations:write");

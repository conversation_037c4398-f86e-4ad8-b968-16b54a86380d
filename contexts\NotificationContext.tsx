"use client";

import { adminApi } from "@/lib/api-client";
import { useAuth } from "@/lib/auth-context";
import React, { createContext, useContext, useEffect, useState } from "react";

interface NotificationCounts {
	total: number;
	unread: number;
}

interface NotificationContextType {
	counts: NotificationCounts;
	loading: boolean;
	error: string | null;
	refetchCounts: () => Promise<void>;
	markAsRead: (id: string) => void;
	deleteNotification: (id: string) => void;
	markAllAsRead: (count: number) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function useNotificationContext() {
	const context = useContext(NotificationContext);
	if (context === undefined) {
		throw new Error("useNotificationContext must be used within a NotificationProvider");
	}
	return context;
}

export function NotificationProvider({ children }: { children: React.ReactNode }) {
	const { user, session, loading: authLoading } = useAuth();
	const [counts, setCounts] = useState<NotificationCounts>({ total: 0, unread: 0 });
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const fetchCounts = async () => {
		// Don't fetch if user is not authenticated
		if (!user || !session) {
			setLoading(false);
			return;
		}

		try {
			setLoading(true);
			setError(null);

			// Fetch notifications to count them
			const result = await adminApi.getNotifications({ limit: 100 });

			if (result.success) {
				const total = result.data.length;
				const unread = result.data.filter((notif: any) => !notif.is_read).length;

				setCounts({ total, unread });
			} else {
				throw new Error(result.error || "Failed to fetch notifications");
			}
		} catch (err) {
			console.error("Error fetching notification counts:", err);
			setError(err instanceof Error ? err.message : "Failed to fetch notification counts");
		} finally {
			setLoading(false);
		}
	};

	// Optimistic updates for better UX
	const markAsRead = (id: string) => {
		setCounts((prev) => ({
			...prev,
			unread: Math.max(0, prev.unread - 1),
		}));
	};

	const deleteNotification = (id: string) => {
		setCounts((prev) => ({
			total: Math.max(0, prev.total - 1),
			unread: Math.max(0, prev.unread - 1), // Assume it was unread for worst case
		}));
	};

	const markAllAsRead = (count: number) => {
		setCounts((prev) => ({
			...prev,
			unread: 0,
		}));
	};

	useEffect(() => {
		// Only fetch when auth is ready and user is authenticated
		if (!authLoading && user && session) {
			fetchCounts();
		} else if (!authLoading && !user) {
			// Auth is ready but no user - stop loading
			setLoading(false);
		}
	}, [authLoading, user, session]);

	const value: NotificationContextType = {
		counts,
		loading,
		error,
		refetchCounts: fetchCounts,
		markAsRead,
		deleteNotification,
		markAllAsRead,
	};

	return <NotificationContext.Provider value={value}>{children}</NotificationContext.Provider>;
}

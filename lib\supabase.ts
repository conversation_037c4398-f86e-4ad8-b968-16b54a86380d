import { createClient } from "@supabase/supabase-js";
import { Database } from "./types";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
	throw new Error("Missing Supabase environment variables");
}

// Client for browser/frontend use
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// Admin client for server-side operations (uses service role key)
// Only create on server side where service role key is available
export const supabaseAdmin =
	typeof window === "undefined" && process.env.SUPABASE_SERVICE_ROLE_KEY
		? createClient<Database>(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY, {
				auth: {
					autoRefreshToken: false,
					persistSession: false,
				},
		  })
		: null;

// Helper function to ensure supabaseAdmin is available
export function getSupabaseAdmin() {
	if (!supabaseAdmin) {
		throw new Error("Supabase admin client is not available. Make sure SUPABASE_SERVICE_ROLE_KEY is set.");
	}
	return supabaseAdmin;
}

// Helper function to handle Supabase errors
export function handleSupabaseError(error: any) {
	console.error("Supabase error:", error);

	if (error?.message) {
		return error.message;
	}

	return "An unexpected error occurred";
}

// Helper function for server-side database operations
export async function executeQuery<T>(
	queryFn: () => Promise<{ data: T | null; error: any }>
): Promise<{ data: T | null; error: string | null }> {
	try {
		const { data, error } = await queryFn();

		if (error) {
			return { data: null, error: handleSupabaseError(error) };
		}

		return { data, error: null };
	} catch (err) {
		return { data: null, error: handleSupabaseError(err) };
	}
}

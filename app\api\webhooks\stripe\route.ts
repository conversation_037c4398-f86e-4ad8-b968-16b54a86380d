import { calculateRemainingAmount } from "@/lib/deposit-settings";
import { createNotification, createPaymentReceivedNotification } from "@/lib/notifications";
import { verifyWebhookSignature } from "@/lib/stripe";
import { supabase, supabaseAdmin } from "@/lib/supabase";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		const body = await request.text();
		const headersList = headers();
		const signature = headersList.get("stripe-signature");

		if (!signature) {
			return NextResponse.json({ error: "Missing stripe-signature header" }, { status: 400 });
		}

		// Verify webhook signature
		const verification = verifyWebhookSignature(body, signature);

		if (!verification.success) {
			console.error("Webhook signature verification failed:", verification.error);
			return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
		}

		const event = verification.event!;
		console.log("Received Stripe webhook:", event.type);

		// Handle different event types
		switch (event.type) {
			case "payment_intent.succeeded":
				await handlePaymentSucceeded(event);
				break;

			case "payment_intent.payment_failed":
				await handlePaymentFailed(event);
				break;

			case "payment_intent.requires_action":
				await handlePaymentRequiresAction(event);
				break;

			case "payment_intent.canceled":
				await handlePaymentCanceled(event);
				break;

			case "payment_intent.amount_capturable_updated":
				await handleSecurityDepositAuthorized(event);
				break;

			default:
				console.log(`Unhandled event type: ${event.type}`);
		}

		return NextResponse.json({ received: true });
	} catch (error) {
		console.error("Webhook error:", error);
		return NextResponse.json({ error: "Webhook handler failed" }, { status: 500 });
	}
}

async function handlePaymentSucceeded(event: any) {
	try {
		const paymentIntent = event.data.object;
		const paymentIntentId = paymentIntent.id;

		// Use admin client for server-side operations
		const adminClient = supabaseAdmin || supabase;

		// Update payment record
		const { data: payment, error: paymentError } = await adminClient
			.from("payments")
			.update({
				status: "completed", // Map to our database constraint
				payment_date: new Date().toISOString(),
				updated_at: new Date().toISOString(),
			})
			.eq("payment_intent_id", paymentIntentId)
			.select("*, reservation:reservations!payments_reservation_id_fkey(*)")
			.single();

		if (paymentError || !payment) {
			console.error("Error updating payment record:", paymentError);
			return;
		}

		// Update reservation status and deposit information
		const reservationUpdate: any = {
			status: "confirmed",
			confirmed_at: new Date().toISOString(),
			updated_at: new Date().toISOString(),
		};

		// Handle deposit payments
		if (payment.is_deposit) {
			const remainingAmount = calculateRemainingAmount(payment.reservation.total_amount, payment.amount);
			reservationUpdate.deposit_amount = payment.amount;
			reservationUpdate.remaining_amount = remainingAmount;
			reservationUpdate.deposit_paid = true;
			reservationUpdate.deposit_payment_id = payment.id;
		}

		const { error: reservationError } = await adminClient
			.from("reservations")
			.update(reservationUpdate)
			.eq("id", payment.reservation_id);

		if (reservationError) {
			console.error("Error updating reservation status:", reservationError);
		}

		// Add reservation status history
		await adminClient.from("reservation_status_history").insert({
			reservation_id: payment.reservation_id,
			old_status: "pending",
			new_status: "confirmed",
			changed_by: null, // System change
			change_reason: "Payment confirmed via webhook",
		});

		// Create payment received notification for admins
		const { data: adminProfiles } = await supabase.from("profiles").select("id, email").eq("role", "admin");

		if (adminProfiles) {
			const notificationTemplate = createPaymentReceivedNotification(
				payment.amount,
				payment.currency,
				payment.reservation_id
			);

			for (const admin of adminProfiles) {
				await createNotification(admin.id, notificationTemplate, payment.reservation_id);
			}
		}

		// Send all payment-related emails using batch function with rate limiting
		try {
			const { sendPaymentEmailBatch } = await import("@/lib/email-service");
			const { generateInvoicePDF, createInvoiceData } = await import("@/lib/invoice-generator");
			const { generateBookingConfirmationPDF } = await import("@/lib/pdf-generator");
			const { generateBookingQRCode, generateVerificationUrl } = await import("@/lib/qr-generator");
			const { appConfig } = await import("@/lib/config");

			// Get reservation details with customer and service info
			const { data: reservationDetails } = await adminClient
				.from("reservations")
				.select(
					`
					*,
					customer:customers(first_name, last_name, email, phone),
					service:services(name, requires_security_deposit, security_deposit_amount)
				`
				)
				.eq("id", payment.reservation_id)
				.single();

			if (reservationDetails && reservationDetails.customer) {
				const customerName = `${reservationDetails.customer.first_name} ${reservationDetails.customer.last_name}`;
				const serviceName = reservationDetails.service?.name || "Service";
				const date = new Date(reservationDetails.start_time).toLocaleDateString("fr-FR");
				const time = new Date(reservationDetails.start_time).toLocaleTimeString("fr-FR", {
					hour: "2-digit",
					minute: "2-digit",
				});

				// Generate attachments
				let bookingPdfBuffer: Buffer | undefined;
				let invoiceBuffer: Buffer | undefined;

				try {
					// Generate booking confirmation PDF
					const qrCode = await generateBookingQRCode(reservationDetails.qr_code);
					const verificationUrl = generateVerificationUrl(reservationDetails.qr_code);

					const bookingPdf = await generateBookingConfirmationPDF({
						reservationNumber: reservationDetails.reservation_number,
						customerName,
						customerEmail: reservationDetails.customer.email,
						customerPhone: reservationDetails.customer.phone,
						serviceName,
						date,
						time,
						participants: reservationDetails.participant_count,
						totalAmount: reservationDetails.total_amount,
						specialRequests: reservationDetails.special_requests,
						qrCodeData: {
							reservationId: reservationDetails.id,
							reservationNumber: reservationDetails.reservation_number,
							customerName,
							serviceName,
							date,
							time,
							participants: reservationDetails.participant_count,
							totalAmount: reservationDetails.total_amount,
							verificationUrl,
						},
						qrCode,
						verificationUrl,
						appUrl: appConfig.url,
					});

					bookingPdfBuffer = Buffer.from(await bookingPdf.arrayBuffer());

					// Generate invoice PDF
					const invoiceData = createInvoiceData(
						reservationDetails,
						payment,
						reservationDetails.customer,
						reservationDetails.service
					);

					const invoicePDF = await generateInvoicePDF(invoiceData);
					invoiceBuffer = Buffer.from(await invoicePDF.arrayBuffer());
				} catch (attachmentError) {
					console.error("Error generating attachments:", attachmentError);
					// Continue without attachments
				}

				// Send all emails in batch with rate limiting
				const emailResults = await sendPaymentEmailBatch({
					customerEmail: reservationDetails.customer.email,
					customerName,
					bookingConfirmationData: {
						reservationNumber: reservationDetails.reservation_number,
						serviceName,
						date,
						time,
						participants: reservationDetails.participant_count,
						totalAmount: reservationDetails.total_amount,
						specialRequests: reservationDetails.special_requests,
						// Security deposit information
						securityDepositRequired: reservationDetails.service?.requires_security_deposit || false,
						securityDepositAmount: reservationDetails.service?.security_deposit_amount || 0,
						securityDepositStatus: reservationDetails.security_deposit_status || "none",
					},
					paymentConfirmationData: {
						reservationNumber: reservationDetails.reservation_number,
						serviceName,
						date,
						time,
						participants: reservationDetails.participant_count,
						amount: payment.amount,
						currency: payment.currency.toUpperCase(),
						paymentDate: new Date().toLocaleDateString("fr-FR"),
						isDeposit: payment.is_deposit,
						remainingAmount: payment.is_deposit ? reservationDetails.remaining_amount : undefined,
					},
					adminPaymentReceivedData: {
						reservationNumber: reservationDetails.reservation_number,
						customerName,
						amount: payment.amount,
						currency: payment.currency.toUpperCase(),
						paymentDate: new Date().toLocaleDateString("fr-FR"),
						isDeposit: payment.is_deposit,
					},
					bookingPdfAttachment: bookingPdfBuffer,
					paymentReceiptAttachment: invoiceBuffer,
				});

				console.log("Webhook payment email batch results:", emailResults);
			}
		} catch (emailError) {
			console.error("Error sending payment confirmation emails:", emailError);
			// Don't fail the webhook for email errors
		}

		console.log(`Payment succeeded for reservation ${payment.reservation_id}`);
	} catch (error) {
		console.error("Error handling payment succeeded:", error);
	}
}

async function handlePaymentFailed(event: any) {
	try {
		const paymentIntent = event.data.object;
		const paymentIntentId = paymentIntent.id;
		const failureReason = paymentIntent.last_payment_error?.message || "Payment failed";

		// Use admin client for server-side operations
		const adminClient = supabaseAdmin || supabase;

		// Update payment record
		const { data: payment, error: paymentError } = await adminClient
			.from("payments")
			.update({
				status: "failed",
				failure_reason: failureReason,
				updated_at: new Date().toISOString(),
			})
			.eq("payment_intent_id", paymentIntentId)
			.select("*")
			.single();

		if (paymentError || !payment) {
			console.error("Error updating payment record:", paymentError);
			return;
		}

		// Check if there are other successful payments for this reservation
		const { data: successfulPayments } = await supabase
			.from("payments")
			.select("*")
			.eq("reservation_id", payment.reservation_id)
			.eq("status", "completed");

		// Only update reservation status if no successful payments exist
		if (!successfulPayments || successfulPayments.length === 0) {
			await supabase
				.from("reservations")
				.update({
					status: "payment_failed",
					updated_at: new Date().toISOString(),
				})
				.eq("id", payment.reservation_id);
		}

		console.log(`Payment failed for reservation ${payment.reservation_id}: ${failureReason}`);
	} catch (error) {
		console.error("Error handling payment failed:", error);
	}
}

async function handlePaymentRequiresAction(event: any) {
	try {
		const paymentIntent = event.data.object;
		const paymentIntentId = paymentIntent.id;

		// Update payment record
		await supabase
			.from("payments")
			.update({
				status: "requires_action",
				updated_at: new Date().toISOString(),
			})
			.eq("payment_intent_id", paymentIntentId);

		console.log(`Payment requires action for payment intent ${paymentIntentId}`);
	} catch (error) {
		console.error("Error handling payment requires action:", error);
	}
}

async function handlePaymentCanceled(event: any) {
	try {
		const paymentIntent = event.data.object;
		const paymentIntentId = paymentIntent.id;

		// Update payment record
		const { data: payment, error: paymentError } = await supabase
			.from("payments")
			.update({
				status: "canceled",
				updated_at: new Date().toISOString(),
			})
			.eq("payment_intent_id", paymentIntentId)
			.select("*")
			.single();

		if (paymentError || !payment) {
			console.error("Error updating payment record:", paymentError);
			return;
		}

		// Check if there are other successful payments for this reservation
		const { data: successfulPayments } = await supabase
			.from("payments")
			.select("*")
			.eq("reservation_id", payment.reservation_id)
			.eq("status", "completed");

		// Only update reservation status if no successful payments exist
		if (!successfulPayments || successfulPayments.length === 0) {
			await supabase
				.from("reservations")
				.update({
					status: "canceled",
					updated_at: new Date().toISOString(),
				})
				.eq("id", payment.reservation_id);
		}

		console.log(`Payment canceled for reservation ${payment.reservation_id}`);
	} catch (error) {
		console.error("Error handling payment canceled:", error);
	}
}

async function handleSecurityDepositAuthorized(event: any) {
	try {
		const paymentIntent = event.data.object;
		const paymentIntentId = paymentIntent.id;

		console.log("=== SECURITY DEPOSIT AUTHORIZATION WEBHOOK ===");
		console.log("Event type:", event.type);
		console.log("Payment Intent ID:", paymentIntentId);
		console.log("Payment Intent status:", paymentIntent.status);
		console.log("Amount capturable:", paymentIntent.amount_capturable);
		console.log("Metadata:", paymentIntent.metadata);

		// Check if this is a security deposit authorization
		// First check metadata, then check if payment intent ID exists in our security deposits
		const isSecurityDepositByMetadata = paymentIntent.metadata?.type === "security_deposit_authorization";

		// Use admin client for server-side operations
		const adminClient = supabaseAdmin || supabase;

		// Check if this payment intent is associated with a security deposit in our database
		const { data: reservation, error: reservationError } = await adminClient
			.from("reservations")
			.select("id, security_deposit_status")
			.eq("security_deposit_payment_intent_id", paymentIntentId)
			.single();

		const isSecurityDepositByDatabase = !reservationError && reservation;

		if (!isSecurityDepositByMetadata && !isSecurityDepositByDatabase) {
			console.log("Not a security deposit authorization (checked metadata and database), skipping");
			return;
		}

		console.log("Security deposit authorization detected:", {
			byMetadata: isSecurityDepositByMetadata,
			byDatabase: isSecurityDepositByDatabase,
			reservationId: reservation?.id,
		});

		// Verify the payment intent is in the correct state for authorization
		if (paymentIntent.status !== "requires_capture") {
			console.log(`Payment intent status is ${paymentIntent.status}, expected requires_capture`);
			return;
		}

		// Use admin client for server-side operations
		const adminClient = supabaseAdmin || supabase;

		// Update reservation security deposit status to authorized
		const { error: updateError } = await adminClient
			.from("reservations")
			.update({
				security_deposit_status: "authorized",
				updated_at: new Date().toISOString(),
			})
			.eq("security_deposit_payment_intent_id", paymentIntentId);

		if (updateError) {
			console.error("Error updating security deposit status:", updateError);
			return;
		}

		// Update the security deposit transaction status
		const { error: transactionError } = await adminClient
			.from("security_deposit_transactions")
			.update({
				status: "succeeded",
				updated_at: new Date().toISOString(),
			})
			.eq("payment_intent_id", paymentIntentId)
			.eq("transaction_type", "authorization");

		if (transactionError) {
			console.error("Error updating security deposit transaction:", transactionError);
		}

		console.log(`Security deposit authorized successfully for payment intent ${paymentIntentId}`);
	} catch (error) {
		console.error("Error handling security deposit authorization:", error);
	}
}

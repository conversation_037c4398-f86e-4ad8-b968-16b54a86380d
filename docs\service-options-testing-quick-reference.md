# Service Options Testing - Quick Reference Card

## 🚀 30-Minute Critical Path Test

### Admin Test (15 minutes)
```bash
# 1. Create Test Option
Name: "Test Equipment"
Price: 15€
Per-participant: ✓
Quote-based: ✗

# 2. Create Test Group  
Name: "Duration Choice"
Type: single_required
Options: ["Half Day", "Full Day"]

# 3. Assign to Service
Service: [Pick any active service]
Assignments: Both option + group
Required: Group=Yes, Option=No
```

### Customer Test (15 minutes)
```bash
# 1. Navigate to service with new options
# 2. Start booking: Date → Time → Participants
# 3. Options page: Select from required group + optional equipment
# 4. Verify pricing: Base + (15€ × participants)
# 5. Complete booking → Check confirmation shows options
```

## 🔧 Essential Test Scenarios

### Option Types to Test
| Type | Example | Expected Behavior |
|------|---------|-------------------|
| Per-participant | Equipment (15€) | 15€ × participant count |
| Fixed price | Insurance (25€) | 25€ regardless of participants |
| Quote-based | Private transport | Shows "sur devis" |

### Group Types to Test
| Type | Min | Max | Behavior |
|------|-----|-----|----------|
| `single_required` | 1 | 1 | Must select exactly 1 |
| `single_optional` | 0 | 1 | Can select 0 or 1 |
| `multiple_required` | 2 | 4 | Must select 2-4 options |
| `multiple_optional` | 0 | 3 | Can select 0-3 options |

### Pricing Test Cases
```javascript
// Test Case 1: Per-participant option
Service: 50€/person × 3 people = 150€
Option: 15€/person × 3 people = 45€
Total: 195€

// Test Case 2: Fixed price option  
Service: 50€/person × 3 people = 150€
Option: 25€ fixed = 25€
Total: 175€

// Test Case 3: Quote-based option
Service: 50€/person × 3 people = 150€
Option: Quote-based = "sur devis"
Total: 150€ + options sur devis
```

## 🐛 Common Issues & Quick Fixes

### Issue: Options not showing
```bash
✓ Check: Option assignments exist and are active
✓ Check: RLS policies allow access
✓ Check: Service has optionsV2 property populated
```

### Issue: Wrong pricing
```bash
✓ Check: per_participant flag is correct
✓ Check: custom_price overrides base_price
✓ Check: participant count is passed correctly
```

### Issue: Validation not working
```bash
✓ Check: Group selection_type is correct
✓ Check: min_selections/max_selections are set
✓ Check: is_required flag on assignments
```

## 📊 Database Quick Checks

### Verify Migration Success
```sql
-- Check options were created
SELECT COUNT(*) FROM service_options WHERE is_active = true;

-- Check groups were created  
SELECT COUNT(*) FROM service_option_groups WHERE is_active = true;

-- Check assignments were created
SELECT COUNT(*) FROM service_option_assignments WHERE is_active = true;

-- Check RLS is enabled
SELECT tablename, rowsecurity FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename LIKE '%service_option%';
```

### Test Data Integrity
```sql
-- Verify no orphaned assignments
SELECT COUNT(*) FROM service_option_assignments soa
LEFT JOIN services s ON s.id = soa.service_id
WHERE s.id IS NULL;

-- Verify group-option relationships
SELECT COUNT(*) FROM service_option_group_items sogi
LEFT JOIN service_options so ON so.id = sogi.option_id
WHERE so.id IS NULL;
```

## 🎯 Success Criteria Checklist

### Admin Interface ✅
- [ ] Can create/edit/delete options
- [ ] Can create/edit/delete groups  
- [ ] Can assign options/groups to services
- [ ] Custom pricing works
- [ ] Validation prevents invalid configs

### Customer Interface ✅
- [ ] Options display on service pages
- [ ] Selection interface is intuitive
- [ ] Pricing updates in real-time
- [ ] Validation prevents invalid selections
- [ ] Booking completes successfully

### Technical ✅
- [ ] All API endpoints respond correctly
- [ ] Database integrity maintained
- [ ] RLS policies enforced
- [ ] Performance acceptable (<2s load times)
- [ ] Cross-browser compatible

## 🚨 Emergency Rollback

If critical issues are found:

1. **Immediate:** Disable new option system
```sql
-- Temporarily disable RLS to allow emergency access
ALTER TABLE service_option_assignments DISABLE ROW LEVEL SECURITY;
```

2. **Fallback:** Use legacy JSON options
```javascript
// In service API, prioritize legacy options field
const options = service.options || []; // Legacy JSON
const optionsV2 = service.optionsV2 || []; // New system
```

3. **Recovery:** Re-enable after fixes
```sql
-- Re-enable RLS after fixes
ALTER TABLE service_option_assignments ENABLE ROW LEVEL SECURITY;
```

## 📞 Support Contacts

- **Technical Issues:** Check GitHub issues
- **Database Issues:** Check Supabase dashboard
- **Business Logic:** Review service-options-refactor-design.md
- **API Issues:** Check browser network tab + server logs

"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, CheckCircle, Loader2, Plus, Tag, Trash2, X } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { adminApi } from "@/lib/api-client";

interface CouponManagementProps {
	reservationId: string;
	currentDiscount?: {
		code: string | null;
		amount: number;
	};
	originalAmount: number;
	onCouponUpdate?: () => void;
}

interface AppliedCoupon {
	id: string;
	code: string;
	description: string | null;
	discount_type: string;
	discount_value: number;
}

interface NewCouponForm {
	code: string;
	description: string;
	discount_type: "percentage" | "fixed_amount";
	discount_value: number;
	min_purchase_amount?: number;
	max_discount_amount?: number;
	usage_limit?: number;
	valid_from?: string;
	valid_until?: string;
	applicable_services?: string[];
	is_active: boolean;
}

export function CouponManagement({
	reservationId,
	currentDiscount,
	originalAmount,
	onCouponUpdate,
}: CouponManagementProps) {
	const [couponCode, setCouponCode] = useState("");
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [success, setSuccess] = useState<string | null>(null);
	const [showCreateDialog, setShowCreateDialog] = useState(false);
	const [appliedCoupon, setAppliedCoupon] = useState<AppliedCoupon | null>(null);

	// New coupon form state
	const [newCouponForm, setNewCouponForm] = useState<NewCouponForm>({
		code: "",
		description: "",
		discount_type: "percentage",
		discount_value: 0,
		is_active: true,
	});
	const [createLoading, setCreateLoading] = useState(false);

	useEffect(() => {
		// If there's a current discount, we should fetch the coupon details
		if (currentDiscount?.code) {
			fetchCouponDetails(currentDiscount.code);
		}
	}, [currentDiscount]);

	const fetchCouponDetails = async (code: string) => {
		try {
			const { data: coupon } = await supabase
				.from("discount_coupons")
				.select("*")
				.eq("code", code.toUpperCase())
				.eq("is_active", true)
				.single();

			if (coupon) {
				setAppliedCoupon({
					id: coupon.id,
					code: coupon.code,
					description: coupon.description,
					discount_type: coupon.discount_type,
					discount_value: coupon.discount_value,
				});
			}
		} catch (error) {
			console.error("Error fetching coupon details:", error);
		}
	};

	const handleApplyCoupon = async () => {
		if (!couponCode.trim()) {
			setError("Veuillez saisir un code de réduction");
			return;
		}

		setLoading(true);
		setError(null);
		setSuccess(null);

		try {
			const {
				data: { session },
			} = await supabase.auth.getSession();

			if (!session?.access_token) {
				throw new Error("No authentication session found");
			}

			const response = await fetch(`/api/admin/reservations/${reservationId}/apply-coupon`, {
				method: "POST",
				headers: {
					Authorization: `Bearer ${session.access_token}`,
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					couponCode: couponCode.trim(),
				}),
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || "Failed to apply coupon");
			}

			if (result.success) {
				setSuccess(`Code de réduction appliqué avec succès! Réduction: ${result.discountAmount}€`);
				setAppliedCoupon(result.coupon);
				setCouponCode("");
				onCouponUpdate?.();
			} else {
				setError(result.error || "Erreur lors de l'application du code");
			}
		} catch (error) {
			console.error("Error applying coupon:", error);
			setError(error instanceof Error ? error.message : "Erreur lors de l'application du code");
		} finally {
			setLoading(false);
		}
	};

	const handleRemoveCoupon = async () => {
		setLoading(true);
		setError(null);
		setSuccess(null);

		try {
			const {
				data: { session },
			} = await supabase.auth.getSession();

			if (!session?.access_token) {
				throw new Error("No authentication session found");
			}

			const response = await fetch(`/api/admin/reservations/${reservationId}/apply-coupon`, {
				method: "DELETE",
				headers: {
					Authorization: `Bearer ${session.access_token}`,
					"Content-Type": "application/json",
				},
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || "Failed to remove coupon");
			}

			if (result.success) {
				setSuccess("Code de réduction supprimé avec succès");
				setAppliedCoupon(null);
				onCouponUpdate?.();
			} else {
				setError(result.error || "Erreur lors de la suppression du code");
			}
		} catch (error) {
			console.error("Error removing coupon:", error);
			setError(error instanceof Error ? error.message : "Erreur lors de la suppression du code");
		} finally {
			setLoading(false);
		}
	};

	const handleCreateAndApplyCoupon = async () => {
		if (!newCouponForm.code.trim() || !newCouponForm.discount_value) {
			setError("Code et valeur de réduction requis");
			return;
		}

		setCreateLoading(true);
		setError(null);
		setSuccess(null);

		try {
			// First create the coupon
			const couponData = {
				...newCouponForm,
				code: newCouponForm.code.toUpperCase().trim(),
				valid_from: newCouponForm.valid_from ? new Date(newCouponForm.valid_from).toISOString() : null,
				valid_until: newCouponForm.valid_until ? new Date(newCouponForm.valid_until).toISOString() : null,
			};

			await adminApi.createDiscountCoupon(couponData);

			// Then apply it to the reservation
			const {
				data: { session },
			} = await supabase.auth.getSession();

			if (!session?.access_token) {
				throw new Error("No authentication session found");
			}

			const response = await fetch(`/api/admin/reservations/${reservationId}/apply-coupon`, {
				method: "POST",
				headers: {
					Authorization: `Bearer ${session.access_token}`,
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					couponCode: couponData.code,
				}),
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || "Failed to apply new coupon");
			}

			if (result.success) {
				setSuccess(`Nouveau code créé et appliqué avec succès! Réduction: ${result.discountAmount}€`);
				setAppliedCoupon(result.coupon);
				setShowCreateDialog(false);
				setNewCouponForm({
					code: "",
					description: "",
					discount_type: "percentage",
					discount_value: 0,
					is_active: true,
				});
				onCouponUpdate?.();
			} else {
				setError(result.error || "Erreur lors de l'application du nouveau code");
			}
		} catch (error) {
			console.error("Error creating and applying coupon:", error);
			setError(error instanceof Error ? error.message : "Erreur lors de la création du code");
		} finally {
			setCreateLoading(false);
		}
	};

	const formatDiscount = (type: string, value: number) => {
		return type === "percentage" ? `${value}%` : `${value}€`;
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Tag className="w-5 h-5" />
					Codes de réduction
				</CardTitle>
			</CardHeader>
			<CardContent className="space-y-4">
				{/* Display applied coupon */}
				{appliedCoupon && currentDiscount?.code && (
					<div className="flex items-center justify-between p-3 bg-emerald-50 border border-emerald-200 rounded-lg">
						<div className="flex items-center gap-2">
							<CheckCircle className="w-4 h-4 text-emerald-600" />
							<div>
								<div className="font-medium text-emerald-800">{appliedCoupon.code}</div>
								<div className="text-sm text-emerald-600">
									{appliedCoupon.description || "Code de réduction appliqué"} - Réduction:{" "}
									{formatDiscount(appliedCoupon.discount_type, appliedCoupon.discount_value)}
									{currentDiscount.amount > 0 && ` (${currentDiscount.amount}€)`}
								</div>
							</div>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={handleRemoveCoupon}
							disabled={loading}
							className="text-red-600 hover:text-red-700"
						>
							<Trash2 className="w-4 h-4" />
						</Button>
					</div>
				)}

				{/* Error/Success messages */}
				{error && (
					<div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-800">
						<AlertCircle className="w-4 h-4" />
						{error}
					</div>
				)}

				{success && (
					<div className="flex items-center gap-2 p-3 bg-emerald-50 border border-emerald-200 rounded-lg text-emerald-800">
						<CheckCircle className="w-4 h-4" />
						{success}
					</div>
				)}

				{/* Apply existing coupon */}
				{!appliedCoupon && (
					<div className="space-y-3">
						<div>
							<Label htmlFor="coupon-code">Appliquer un code existant</Label>
							<div className="flex gap-2 mt-1">
								<Input
									id="coupon-code"
									placeholder="Saisir le code de réduction"
									value={couponCode}
									onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
									disabled={loading}
								/>
								<Button onClick={handleApplyCoupon} disabled={loading || !couponCode.trim()}>
									{loading ? <Loader2 className="w-4 h-4 animate-spin" /> : "Appliquer"}
								</Button>
							</div>
						</div>

						{/* Create new coupon */}
						<div className="pt-3 border-t">
							<Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
								<DialogTrigger asChild>
									<Button variant="outline" className="w-full">
										<Plus className="w-4 h-4 mr-2" />
										Créer et appliquer un nouveau code
									</Button>
								</DialogTrigger>
								<DialogContent className="max-w-md">
									<DialogHeader>
										<DialogTitle>Créer un nouveau code de réduction</DialogTitle>
									</DialogHeader>
									<div className="space-y-4">
										<div>
											<Label htmlFor="new-code">Code</Label>
											<Input
												id="new-code"
												placeholder="CODE2024"
												value={newCouponForm.code}
												onChange={(e) =>
													setNewCouponForm((prev) => ({
														...prev,
														code: e.target.value.toUpperCase(),
													}))
												}
											/>
										</div>

										<div>
											<Label htmlFor="new-description">Description (optionnel)</Label>
											<Textarea
												id="new-description"
												placeholder="Description du code de réduction"
												value={newCouponForm.description}
												onChange={(e) =>
													setNewCouponForm((prev) => ({
														...prev,
														description: e.target.value,
													}))
												}
											/>
										</div>

										<div>
											<Label htmlFor="new-type">Type de réduction</Label>
											<Select
												value={newCouponForm.discount_type}
												onValueChange={(value: "percentage" | "fixed_amount") =>
													setNewCouponForm((prev) => ({ ...prev, discount_type: value }))
												}
											>
												<SelectTrigger>
													<SelectValue />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="percentage">Pourcentage</SelectItem>
													<SelectItem value="fixed_amount">Montant fixe</SelectItem>
												</SelectContent>
											</Select>
										</div>

										<div>
											<Label htmlFor="new-value">
												Valeur {newCouponForm.discount_type === "percentage" ? "(%)" : "(€)"}
											</Label>
											<Input
												id="new-value"
												type="number"
												min="0"
												max={newCouponForm.discount_type === "percentage" ? "100" : undefined}
												step={newCouponForm.discount_type === "percentage" ? "1" : "0.01"}
												value={newCouponForm.discount_value}
												onChange={(e) =>
													setNewCouponForm((prev) => ({
														...prev,
														discount_value: parseFloat(e.target.value) || 0,
													}))
												}
											/>
										</div>

										<div className="flex gap-2">
											<Button
												variant="outline"
												onClick={() => setShowCreateDialog(false)}
												disabled={createLoading}
											>
												Annuler
											</Button>
											<Button
												onClick={handleCreateAndApplyCoupon}
												disabled={
													createLoading ||
													!newCouponForm.code.trim() ||
													!newCouponForm.discount_value
												}
											>
												{createLoading ? (
													<Loader2 className="w-4 h-4 animate-spin mr-2" />
												) : null}
												Créer et appliquer
											</Button>
										</div>
									</div>
								</DialogContent>
							</Dialog>
						</div>
					</div>
				)}
			</CardContent>
		</Card>
	);
}

"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { adminApi } from "@/lib/api-client";
import { AlertTriangle, Calendar, Edit, Plus, Save, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";

interface ServiceBlackoutDate {
	id: string;
	service_id: string;
	start_date: string;
	end_date: string;
	reason: string;
	is_active: boolean;
	created_at: string;
	updated_at: string;
}

interface ServiceBlackoutDatesProps {
	serviceId: string;
	serviceName: string;
}

export default function ServiceBlackoutDates({ serviceId, serviceName }: ServiceBlackoutDatesProps) {
	const [blackoutDates, setBlackoutDates] = useState<ServiceBlackoutDate[]>([]);
	const [loading, setLoading] = useState(true);
	const [editing, setEditing] = useState<string | null>(null);
	const [creating, setCreating] = useState(false);
	const [formData, setFormData] = useState<Partial<ServiceBlackoutDate>>({});

	useEffect(() => {
		fetchBlackoutDates();
	}, [serviceId]);

	const fetchBlackoutDates = async () => {
		try {
			const data = await adminApi.getServiceBlackoutDates(serviceId);
			setBlackoutDates(data.blackoutDates || []);
		} catch (error) {
			console.error("Error fetching blackout dates:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleCreate = () => {
		setCreating(true);
		const today = new Date().toISOString().split("T")[0];
		setFormData({
			service_id: serviceId,
			start_date: today,
			end_date: today,
			reason: "",
			is_active: true,
		});
	};

	const handleEdit = (blackoutDate: ServiceBlackoutDate) => {
		setEditing(blackoutDate.id);
		setFormData({ ...blackoutDate });
	};

	const handleSave = async () => {
		try {
			if (creating) {
				await adminApi.createServiceBlackoutDate(serviceId, formData);
			} else {
				await adminApi.updateServiceBlackoutDate(serviceId, editing!, formData);
			}

			await fetchBlackoutDates();
			handleCancel();
		} catch (error) {
			console.error("Error saving blackout date:", error);
		}
	};

	const handleDelete = async (blackoutDateId: string) => {
		if (confirm("Êtes-vous sûr de vouloir supprimer cette période d'indisponibilité ?")) {
			try {
				await adminApi.deleteServiceBlackoutDate(serviceId, blackoutDateId);
				await fetchBlackoutDates();
			} catch (error) {
				console.error("Error deleting blackout date:", error);
			}
		}
	};

	const handleCancel = () => {
		setCreating(false);
		setEditing(null);
		setFormData({});
	};

	const formatDateRange = (startDate: string, endDate: string) => {
		// Parse dates manually to avoid timezone issues
		const parseDate = (dateStr: string) => {
			const [year, month, day] = dateStr.split("-").map(Number);
			return new Date(year, month - 1, day);
		};

		const start = parseDate(startDate).toLocaleDateString("fr-FR");
		const end = parseDate(endDate).toLocaleDateString("fr-FR");
		return start === end ? start : `${start} - ${end}`;
	};

	const isCurrentlyActive = (blackoutDate: ServiceBlackoutDate) => {
		if (!blackoutDate.is_active) return false;
		const now = new Date();
		now.setHours(0, 0, 0, 0); // Reset to start of day for fair comparison

		// Parse dates manually to avoid timezone issues
		const parseDate = (dateStr: string) => {
			const [year, month, day] = dateStr.split("-").map(Number);
			const date = new Date(year, month - 1, day);
			date.setHours(0, 0, 0, 0);
			return date;
		};

		const start = parseDate(blackoutDate.start_date);
		const end = parseDate(blackoutDate.end_date);
		end.setHours(23, 59, 59, 999); // Set to end of day

		return now >= start && now <= end;
	};

	if (loading) {
		return <div className="p-4">Chargement...</div>;
	}

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<div>
					<h3 className="text-lg font-semibold">Périodes d'indisponibilité</h3>
					<p className="text-sm text-gray-600">Service: {serviceName}</p>
				</div>
				<Button onClick={handleCreate} className="flex items-center gap-2">
					<Plus className="w-4 h-4" />
					Nouvelle période
				</Button>
			</div>

			{/* Blackout Dates List */}
			<div className="space-y-4">
				{blackoutDates.map((blackoutDate) => (
					<Card key={blackoutDate.id}>
						<CardContent className="p-4">
							{editing === blackoutDate.id ? (
								<BlackoutDateForm
									formData={formData}
									setFormData={setFormData}
									onSave={handleSave}
									onCancel={handleCancel}
								/>
							) : (
								<div className="flex justify-between items-start">
									<div className="space-y-2">
										<div className="flex items-center gap-2">
											<Badge variant={blackoutDate.is_active ? "destructive" : "secondary"}>
												{blackoutDate.is_active ? "Actif" : "Inactif"}
											</Badge>
											{isCurrentlyActive(blackoutDate) && (
												<Badge variant="destructive" className="flex items-center gap-1">
													<AlertTriangle className="w-3 h-3" />
													En cours
												</Badge>
											)}
											<span className="font-medium">
												{formatDateRange(blackoutDate.start_date, blackoutDate.end_date)}
											</span>
										</div>
										<div className="text-sm text-gray-600">
											<div>
												<strong>Raison:</strong> {blackoutDate.reason}
											</div>
										</div>
									</div>
									<div className="flex gap-2">
										<Button variant="outline" size="sm" onClick={() => handleEdit(blackoutDate)}>
											<Edit className="w-4 h-4" />
										</Button>
										<Button
											variant="outline"
											size="sm"
											onClick={() => handleDelete(blackoutDate.id)}
										>
											<Trash2 className="w-4 h-4" />
										</Button>
									</div>
								</div>
							)}
						</CardContent>
					</Card>
				))}

				{blackoutDates.length === 0 && (
					<Card>
						<CardContent className="p-8 text-center text-gray-500">
							<Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
							<p>Aucune période d'indisponibilité configurée</p>
						</CardContent>
					</Card>
				)}
			</div>

			{/* Create Form */}
			{creating && (
				<Card>
					<CardHeader>
						<CardTitle>Nouvelle période d'indisponibilité</CardTitle>
					</CardHeader>
					<CardContent>
						<BlackoutDateForm
							formData={formData}
							setFormData={setFormData}
							onSave={handleSave}
							onCancel={handleCancel}
						/>
					</CardContent>
				</Card>
			)}
		</div>
	);
}

interface BlackoutDateFormProps {
	formData: Partial<ServiceBlackoutDate>;
	setFormData: (data: Partial<ServiceBlackoutDate>) => void;
	onSave: () => void;
	onCancel: () => void;
}

function BlackoutDateForm({ formData, setFormData, onSave, onCancel }: BlackoutDateFormProps) {
	return (
		<div className="space-y-4">
			<div className="grid grid-cols-2 gap-4">
				<div>
					<Label>Date de début</Label>
					<Input
						type="date"
						value={formData.start_date || ""}
						onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
					/>
				</div>
				<div>
					<Label>Date de fin</Label>
					<Input
						type="date"
						value={formData.end_date || ""}
						onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
					/>
				</div>
			</div>

			<div>
				<Label>Raison de l'indisponibilité</Label>
				<Textarea
					value={formData.reason || ""}
					onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
					placeholder="Ex: Maintenance équipement, congés, météo..."
					rows={3}
				/>
			</div>

			<div className="flex items-center space-x-2">
				<Switch
					checked={formData.is_active || false}
					onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
				/>
				<Label>Période active</Label>
			</div>

			<div className="flex gap-2">
				<Button onClick={onSave} className="flex items-center gap-2">
					<Save className="w-4 h-4" />
					Sauvegarder
				</Button>
				<Button variant="outline" onClick={onCancel} className="flex items-center gap-2">
					<X className="w-4 h-4" />
					Annuler
				</Button>
			</div>
		</div>
	);
}

import { googleReviewsService } from "@/lib/google-reviews-service";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// GET /api/reviews - Fetch Google reviews
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const refresh = searchParams.get("refresh") === "true";
		const minRating = searchParams.get("minRating");
		const maxReviews = searchParams.get("maxReviews");

		// Configure service based on query parameters
		if (minRating) {
			(googleReviewsService as any).config.minRating = parseInt(minRating);
		}
		if (maxReviews) {
			(googleReviewsService as any).config.maxReviews = parseInt(maxReviews);
		}

		let reviews;

		if (refresh) {
			// Force refresh from Google API
			console.log("Forcing refresh of Google reviews...");
			const refreshResult = await googleReviewsService.refreshReviews();

			if (!refreshResult.success) {
				console.warn("Failed to refresh reviews:", refreshResult.error);
				// Fall back to cached reviews
				reviews = await googleReviewsService.getCachedReviews();
			} else {
				reviews = await googleReviewsService.getCachedReviews();
			}
		} else {
			// Use normal flow (API with cache fallback)
			reviews = await googleReviewsService.getReviews();
		}

		// Transform reviews for frontend consumption
		const transformedReviews = reviews.map((review) => ({
			id: review.id,
			name: review.author_name,
			initials: review.author_initials,
			rating: review.rating,
			comment: review.text,
			date: review.date,
			relativeTime: review.relative_time,
			profilePhoto: review.profile_photo_url,
			source: review.source,
		}));

		return NextResponse.json({
			success: true,
			reviews: transformedReviews,
			count: transformedReviews.length,
			source: reviews.length > 0 ? reviews[0].source : "none",
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		console.error("Error fetching reviews:", error);

		return NextResponse.json(
			{
				success: false,
				error: "Failed to fetch reviews",
				details: error instanceof Error ? error.message : "Unknown error",
				reviews: [],
				count: 0,
			},
			{ status: 500 }
		);
	}
}

// POST /api/reviews/refresh - Manually refresh reviews from Google API
export async function POST(request: NextRequest) {
	try {
		console.log("Manual refresh of Google reviews requested...");

		const refreshResult = await googleReviewsService.refreshReviews();

		if (refreshResult.success) {
			const reviews = await googleReviewsService.getCachedReviews();

			return NextResponse.json({
				success: true,
				message: `Successfully refreshed ${refreshResult.count} reviews`,
				count: refreshResult.count,
				cachedCount: reviews.length,
				timestamp: new Date().toISOString(),
			});
		} else {
			return NextResponse.json(
				{
					success: false,
					error: "Failed to refresh reviews",
					details: refreshResult.error,
					count: 0,
				},
				{ status: 500 }
			);
		}
	} catch (error) {
		console.error("Error in manual refresh:", error);

		return NextResponse.json(
			{
				success: false,
				error: "Failed to refresh reviews",
				details: error instanceof Error ? error.message : "Unknown error",
				count: 0,
			},
			{ status: 500 }
		);
	}
}

import { NextRequest, NextResponse } from 'next/server';
import { retryPayment, abandonPayment } from '@/lib/payment-recovery';

export async function POST(request: NextRequest) {
  try {
    const { reservationId, action } = await request.json();

    if (!reservationId) {
      return NextResponse.json(
        { error: 'Reservation ID is required' },
        { status: 400 }
      );
    }

    if (action === 'abandon') {
      const success = await abandonPayment(reservationId, 'User abandoned payment');
      
      if (success) {
        return NextResponse.json({
          success: true,
          message: 'Payment abandoned successfully',
        });
      } else {
        return NextResponse.json(
          { error: 'Failed to abandon payment' },
          { status: 500 }
        );
      }
    }

    // Default action is retry
    const result = await retryPayment({
      reservationId,
      maxRetries: 5, // Allow up to 5 payment attempts
    });

    if (result.success) {
      return NextResponse.json({
        success: true,
        clientSecret: result.clientSecret,
        paymentIntentId: result.paymentIntentId,
        retryCount: result.retryCount,
        canRetry: result.canRetry,
      });
    } else {
      return NextResponse.json(
        {
          error: result.error,
          canRetry: result.canRetry,
          retryCount: result.retryCount,
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error in payment retry:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

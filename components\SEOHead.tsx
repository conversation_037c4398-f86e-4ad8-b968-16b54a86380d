"use client";

import Head from "next/head";
import { usePathname } from "next/navigation";

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  noIndex?: boolean;
  canonical?: string;
  type?: "website" | "article" | "service";
  publishedTime?: string;
  modifiedTime?: string;
}

export default function SEOHead({
  title,
  description,
  keywords,
  image = "/images/home_hero.png",
  noIndex = false,
  canonical,
  type = "website",
  publishedTime,
  modifiedTime,
}: SEOHeadProps) {
  const pathname = usePathname();
  const baseUrl = "https://www.soleiletdecouverte.com";
  
  // Generate default values based on pathname
  const getDefaultTitle = () => {
    if (pathname === "/") return "Soleil et Découverte - Excursions éco-responsables en Guadeloupe";
    if (pathname === "/services") return "Nos Services - Excursions en Guadeloupe";
    if (pathname === "/contact") return "Contact - Soleil et Découverte";
    if (pathname === "/reservation") return "Réservation - Soleil et Découverte";
    if (pathname.startsWith("/services/")) return "Service - Soleil et Découverte";
    return "Soleil et Découverte";
  };

  const getDefaultDescription = () => {
    if (pathname === "/") return "Découvrez la Guadeloupe authentique avec nos excursions éco-responsables. WaterBikes, visites culturelles et rencontres avec les pélicans.";
    if (pathname === "/services") return "Découvrez toutes nos excursions éco-responsables en Guadeloupe : WaterBikes, visites culturelles, rencontres avec les pélicans et bien plus.";
    if (pathname === "/contact") return "Contactez Soleil et Découverte pour réserver votre excursion éco-responsable en Guadeloupe. Nous sommes à votre disposition.";
    if (pathname === "/reservation") return "Réservez votre excursion éco-responsable en Guadeloupe avec Soleil et Découverte. Réservation en ligne simple et sécurisée.";
    return "Excursions éco-responsables en Guadeloupe avec Soleil et Découverte.";
  };

  const getDefaultKeywords = () => {
    const baseKeywords = "Guadeloupe, excursions, écotourisme, Petit-Canal";
    if (pathname === "/services") return `${baseKeywords}, WaterBikes, visites culturelles, pélicans, mangrove`;
    if (pathname === "/contact") return `${baseKeywords}, contact, réservation, information`;
    if (pathname === "/reservation") return `${baseKeywords}, réservation, booking, réserver`;
    if (pathname.startsWith("/services/")) return `${baseKeywords}, service, activité, excursion`;
    return baseKeywords;
  };

  const finalTitle = title || getDefaultTitle();
  const finalDescription = description || getDefaultDescription();
  const finalKeywords = keywords || getDefaultKeywords();
  const finalCanonical = canonical || `${baseUrl}${pathname}`;
  const finalImage = image.startsWith("http") ? image : `${baseUrl}${image}`;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={finalKeywords} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={finalCanonical} />
      
      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Open Graph */}
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={finalImage} />
      <meta property="og:url" content={finalCanonical} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content="Soleil et Découverte" />
      <meta property="og:locale" content="fr_FR" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={finalImage} />
      
      {/* Article specific */}
      {type === "article" && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {type === "article" && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      
      {/* Additional SEO */}
      <meta name="author" content="Soleil et Découverte" />
      <meta name="publisher" content="Soleil et Découverte" />
      <meta name="geo.region" content="GP" />
      <meta name="geo.placename" content="Petit-Canal, Guadeloupe" />
      <meta name="geo.position" content="16.1833;-61.5333" />
      <meta name="ICBM" content="16.1833, -61.5333" />
      
      {/* Language */}
      <meta httpEquiv="content-language" content="fr" />
      
      {/* Mobile */}
      <meta name="format-detection" content="telephone=yes" />
      <meta name="format-detection" content="address=yes" />
    </Head>
  );
}

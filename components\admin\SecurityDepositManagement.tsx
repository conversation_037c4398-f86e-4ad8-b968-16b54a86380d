"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>ard, Shield, AlertTriangle, CheckCircle, Clock } from "lucide-react";
import { supabase } from "@/lib/supabase";

interface SecurityDepositManagementProps {
	reservationId: string;
	onUpdate?: () => void;
}

interface SecurityDepositStatus {
	status: string;
	method?: string;
	amount: number;
	payment_intent_id?: string;
	authorized_at?: string;
	charged_at?: string;
	released_at?: string;
	transactions: Array<{
		id: string;
		transaction_type: string;
		amount: number;
		status: string;
		created_at: string;
		payment_intent_id?: string;
	}>;
}

export function SecurityDepositManagement({ reservationId, onUpdate }: SecurityDepositManagementProps) {
	const [depositStatus, setDepositStatus] = useState<SecurityDepositStatus | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [actionLoading, setActionLoading] = useState<string | null>(null);
	const [stripeStatus, setStripeStatus] = useState<any>(null);
	const [showStripeStatus, setShowStripeStatus] = useState(false);

	useEffect(() => {
		loadDepositStatus();
	}, [reservationId]);

	const loadDepositStatus = async () => {
		try {
			setLoading(true);
			setError(null);

			// Get current session for auth
			const {
				data: { session },
			} = await supabase.auth.getSession();

			if (!session?.access_token) {
				throw new Error("No authentication session found");
			}

			const response = await fetch(`/api/admin/reservations/${reservationId}/security-deposit`, {
				headers: {
					Authorization: `Bearer ${session.access_token}`,
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				throw new Error("Failed to load security deposit status");
			}

			const data = await response.json();
			setDepositStatus(data);
		} catch (err) {
			console.error("Error loading security deposit status:", err);
			setError(err instanceof Error ? err.message : "Unknown error");
		} finally {
			setLoading(false);
		}
	};

	const handleChargeDeposit = async () => {
		if (!depositStatus?.payment_intent_id) return;

		// Show confirmation dialog
		const confirmed = window.confirm(
			`Êtes-vous sûr de vouloir prélever la caution de ${formatAmount(depositStatus.amount)} ?\n\n` +
				"Cette action est irréversible et débitera immédiatement le compte du client."
		);

		if (!confirmed) return;

		try {
			setActionLoading("charge");
			setError(null);

			// Get current session for auth
			const {
				data: { session },
			} = await supabase.auth.getSession();

			if (!session?.access_token) {
				throw new Error("No authentication session found");
			}

			const response = await fetch("/api/security-deposits/charge", {
				method: "POST",
				headers: {
					Authorization: `Bearer ${session.access_token}`,
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ reservationId }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to charge security deposit");
			}

			await loadDepositStatus();
			onUpdate?.();
		} catch (err) {
			console.error("Error charging security deposit:", err);
			setError(err instanceof Error ? err.message : "Unknown error");
		} finally {
			setActionLoading(null);
		}
	};

	const handleReleaseDeposit = async () => {
		if (!depositStatus?.payment_intent_id) return;

		// Show confirmation dialog
		const confirmed = window.confirm(
			`Êtes-vous sûr de vouloir libérer la caution de ${formatAmount(depositStatus.amount)} ?\n\n` +
				"Cette action annulera l'autorisation et le client ne sera pas débité."
		);

		if (!confirmed) return;

		try {
			setActionLoading("release");
			setError(null);

			// Get current session for auth
			const {
				data: { session },
			} = await supabase.auth.getSession();

			if (!session?.access_token) {
				throw new Error("No authentication session found");
			}

			const response = await fetch("/api/security-deposits/release", {
				method: "POST",
				headers: {
					Authorization: `Bearer ${session.access_token}`,
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ reservationId }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to release security deposit");
			}

			await loadDepositStatus();
			onUpdate?.();
		} catch (err) {
			console.error("Error releasing security deposit:", err);
			setError(err instanceof Error ? err.message : "Unknown error");
		} finally {
			setActionLoading(null);
		}
	};

	const getStatusBadge = (status: string) => {
		switch (status) {
			case "authorized":
				return (
					<Badge variant="outline" className="text-blue-600 border-blue-600">
						<Clock className="w-3 h-3 mr-1" />
						Autorisée
					</Badge>
				);
			case "pending":
				// Only show as authorized if there's a payment intent AND it's actually authorized
				// If payment intent is in requires_payment_method state, it's not actually authorized yet
				if (depositStatus?.payment_intent_id) {
					return (
						<Badge variant="outline" className="text-orange-600 border-orange-600">
							<Clock className="w-3 h-3 mr-1" />
							En attente d'autorisation
						</Badge>
					);
				}
				return (
					<Badge variant="outline" className="text-orange-600 border-orange-600">
						<Clock className="w-3 h-3 mr-1" />
						En attente
					</Badge>
				);
			case "charged":
				return (
					<Badge variant="destructive">
						<AlertTriangle className="w-3 h-3 mr-1" />
						Prélevée
					</Badge>
				);
			case "released":
				return (
					<Badge variant="default" className="bg-green-600">
						<CheckCircle className="w-3 h-3 mr-1" />
						Libérée
					</Badge>
				);
			case "none":
				return <Badge variant="secondary">Non configurée</Badge>;
			default:
				return <Badge variant="secondary">{status}</Badge>;
		}
	};

	const checkStripeStatus = async () => {
		if (!depositStatus?.payment_intent_id) return;

		try {
			setActionLoading("stripe-check");
			setError(null);

			// Get current session for auth
			const {
				data: { session },
			} = await supabase.auth.getSession();

			if (!session?.access_token) {
				throw new Error("No authentication session found");
			}

			const response = await fetch(`/api/admin/reservations/${reservationId}/security-deposit-stripe-status`, {
				headers: {
					Authorization: `Bearer ${session.access_token}`,
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to get Stripe status");
			}

			const data = await response.json();
			setStripeStatus(data.data);
			setShowStripeStatus(true);
		} catch (err) {
			console.error("Error checking Stripe status:", err);
			setError(err instanceof Error ? err.message : "Unknown error");
		} finally {
			setActionLoading(null);
		}
	};

	const syncWithStripe = async () => {
		try {
			setActionLoading("sync");
			setError(null);

			// Get current session for auth
			const {
				data: { session },
			} = await supabase.auth.getSession();

			if (!session?.access_token) {
				throw new Error("No authentication session found");
			}

			const response = await fetch(`/api/admin/reservations/${reservationId}/sync-security-deposit`, {
				method: "POST",
				headers: {
					Authorization: `Bearer ${session.access_token}`,
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to sync with Stripe");
			}

			const data = await response.json();
			console.log("Sync result:", data);

			// Reload the deposit status to reflect changes
			await loadDepositStatus();
			onUpdate?.();

			// Show success message
			if (data.data.sync_performed) {
				alert(`Synchronisation réussie: ${data.data.message}`);
			} else {
				alert("Aucune synchronisation nécessaire - les statuts sont déjà cohérents");
			}
		} catch (err) {
			console.error("Error syncing with Stripe:", err);
			setError(err instanceof Error ? err.message : "Unknown error");
		} finally {
			setActionLoading(null);
		}
	};

	const formatAmount = (amount: number) => {
		return new Intl.NumberFormat("fr-FR", {
			style: "currency",
			currency: "EUR",
		}).format(amount);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("fr-FR", {
			day: "2-digit",
			month: "2-digit",
			year: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	if (loading) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Shield className="w-5 h-5" />
						Caution d'équipement
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-center py-4">Chargement...</div>
				</CardContent>
			</Card>
		);
	}

	if (error) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Shield className="w-5 h-5" />
						Caution d'équipement
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-center py-4 text-red-600">Erreur: {error}</div>
				</CardContent>
			</Card>
		);
	}

	if (!depositStatus) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Shield className="w-5 h-5" />
						Caution d'équipement
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-center py-4 text-gray-500">Aucune caution requise pour cette réservation</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<Shield className="w-5 h-5" />
						Caution d'équipement
					</div>
					{getStatusBadge(depositStatus.status)}
				</CardTitle>
			</CardHeader>
			<CardContent className="space-y-4">
				{/* Status Summary */}
				<div className="bg-gray-50 rounded-lg p-4 space-y-2">
					<div className="flex justify-between">
						<span className="text-gray-600">Montant:</span>
						<span className="font-medium">{formatAmount(depositStatus.amount)}</span>
					</div>
					{depositStatus.method && (
						<div className="flex justify-between">
							<span className="text-gray-600">Méthode:</span>
							<span className="font-medium">
								{depositStatus.method === "authorization" ? "Autorisation en ligne" : "Manuel"}
							</span>
						</div>
					)}
					{depositStatus.payment_intent_id && (
						<div className="flex justify-between">
							<span className="text-gray-600">ID Stripe:</span>
							<span className="font-mono text-sm text-gray-500">{depositStatus.payment_intent_id}</span>
						</div>
					)}
				</div>

				{/* Status Information */}
				{depositStatus.status === "pending" && depositStatus.payment_intent_id && (
					<div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2 text-orange-700">
								<Clock className="w-4 h-4" />
								<span className="font-medium">En attente d'autorisation</span>
							</div>
							<div className="flex gap-2">
								<Button
									onClick={checkStripeStatus}
									disabled={actionLoading === "stripe-check"}
									variant="outline"
									size="sm"
									className="text-xs"
								>
									{actionLoading === "stripe-check" ? "Vérification..." : "Vérifier statut"}
								</Button>
							</div>
						</div>
						<p className="text-sm text-orange-600 mt-1">
							Le client doit compléter l'autorisation de caution en ligne. Les actions de prélèvement
							seront disponibles une fois l'autorisation confirmée.
						</p>
					</div>
				)}

				{/* Stripe Status Details */}
				{showStripeStatus && stripeStatus && (
					<div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
						<div className="flex items-center justify-between mb-2">
							<h4 className="font-medium text-gray-900">Statut Stripe détaillé</h4>
							<Button
								onClick={() => setShowStripeStatus(false)}
								variant="ghost"
								size="sm"
								className="text-xs"
							>
								Masquer
							</Button>
						</div>
						<div className="space-y-2 text-sm">
							<div className="flex justify-between">
								<span className="text-gray-600">Statut base de données:</span>
								<span className="font-medium">{stripeStatus.database_status}</span>
							</div>
							<div className="flex justify-between">
								<span className="text-gray-600">Statut Stripe:</span>
								<span className="font-medium">{stripeStatus.stripe_status}</span>
							</div>
							<div className="flex justify-between">
								<span className="text-gray-600">Peut être prélevé:</span>
								<span
									className={`font-medium ${stripeStatus.can_capture ? "text-green-600" : "text-red-600"}`}
								>
									{stripeStatus.can_capture ? "Oui" : "Non"}
								</span>
							</div>
							<div className="flex justify-between">
								<span className="text-gray-600">Peut être annulé:</span>
								<span
									className={`font-medium ${stripeStatus.can_cancel ? "text-green-600" : "text-red-600"}`}
								>
									{stripeStatus.can_cancel ? "Oui" : "Non"}
								</span>
							</div>
						</div>
					</div>
				)}

				{/* Actions */}
				{depositStatus.status === "authorized" && (
					<div className="flex gap-2">
						<Button
							onClick={handleChargeDeposit}
							disabled={actionLoading === "charge"}
							variant="destructive"
							className="flex items-center gap-2"
						>
							<CreditCard className="w-4 h-4" />
							{actionLoading === "charge" ? "Prélèvement..." : "Prélever la caution"}
						</Button>
						<Button
							onClick={handleReleaseDeposit}
							disabled={actionLoading === "release"}
							variant="outline"
							className="flex items-center gap-2"
						>
							<CheckCircle className="w-4 h-4" />
							{actionLoading === "release" ? "Libération..." : "Libérer la caution"}
						</Button>
					</div>
				)}

				{/* Transaction History */}
				{depositStatus.transactions.length > 0 && (
					<div>
						<h4 className="font-semibold mb-2">Historique des transactions</h4>
						<div className="space-y-2">
							{depositStatus.transactions.map((transaction) => (
								<div key={transaction.id} className="border rounded-lg p-3">
									<div className="flex justify-between items-start">
										<div className="space-y-1">
											<div className="flex items-center gap-2">
												<span className="font-medium">
													{transaction.transaction_type === "authorization"
														? "Autorisation"
														: transaction.transaction_type === "charge"
															? "Prélèvement"
															: transaction.transaction_type === "release"
																? "Libération"
																: transaction.transaction_type}
												</span>
												<Badge variant="outline" className="text-xs">
													{transaction.status}
												</Badge>
											</div>
											<div className="text-sm text-gray-600">
												{formatAmount(transaction.amount)} •{" "}
												{formatDate(transaction.created_at)}
											</div>
											{transaction.payment_intent_id && (
												<div className="text-xs text-gray-500 font-mono">
													{transaction.payment_intent_id}
												</div>
											)}
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				)}

				{error && <div className="text-red-600 text-sm bg-red-50 p-3 rounded-lg">{error}</div>}
			</CardContent>
		</Card>
	);
}

import { NextRequest, NextResponse } from "next/server";
import { withAdminAuth } from "@/lib/admin-auth";
import { getAdminNotificationEmail } from "@/lib/admin-notifications";

// GET /api/admin/settings/admin-email - Get admin notification email
export const GET = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const adminEmail = await getAdminNotificationEmail();

		return NextResponse.json({
			success: true,
			adminEmail,
		});
	} catch (error) {
		console.error("Error fetching admin notification email:", error);
		return NextResponse.json(
			{
				error: "Failed to fetch admin notification email",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}, "settings:read");

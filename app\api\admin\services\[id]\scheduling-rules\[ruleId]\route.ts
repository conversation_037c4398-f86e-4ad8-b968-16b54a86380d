import { withAdminAuth } from '@/lib/admin-auth'
import { supabaseAdmin } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'

// PUT /api/admin/services/[id]/scheduling-rules/[ruleId] - Update scheduling rule
export const PUT = withAdminAuth(async (
  request: NextRequest, 
  user, 
  { params }: { params: { id: string; ruleId: string } }
) => {
  try {
    const { id: serviceId, ruleId } = params
    const ruleData = await request.json()

    // Ensure service_id matches the URL parameter
    ruleData.service_id = serviceId

    const { data: rule, error } = await supabaseAdmin
      .from('service_scheduling_rules')
      .update(ruleData)
      .eq('id', ruleId)
      .eq('service_id', serviceId)
      .select()
      .single()

    if (error) {
      console.error('Error updating scheduling rule:', error)
      return NextResponse.json({ error: 'Failed to update scheduling rule' }, { status: 500 })
    }

    if (!rule) {
      return NextResponse.json({ error: 'Scheduling rule not found' }, { status: 404 })
    }

    return NextResponse.json({ rule })
  } catch (error) {
    console.error('Scheduling rule PUT error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:write')

// DELETE /api/admin/services/[id]/scheduling-rules/[ruleId] - Delete scheduling rule
export const DELETE = withAdminAuth(async (
  request: NextRequest, 
  user, 
  { params }: { params: { id: string; ruleId: string } }
) => {
  try {
    const { id: serviceId, ruleId } = params

    const { error } = await supabaseAdmin
      .from('service_scheduling_rules')
      .delete()
      .eq('id', ruleId)
      .eq('service_id', serviceId)

    if (error) {
      console.error('Error deleting scheduling rule:', error)
      return NextResponse.json({ error: 'Failed to delete scheduling rule' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Scheduling rule DELETE error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:write')

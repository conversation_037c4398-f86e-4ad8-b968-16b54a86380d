import QRCode from "qrcode";

export interface BookingQRData {
	reservationId: string;
	reservationNumber: string;
	customerName: string;
	serviceName: string;
	date: string;
	time: string;
	participants: number;
	totalAmount: number;
	verificationUrl: string;
}

/**
 * Generate QR code data URL for booking information
 */
export async function generateBookingQRCode(data: BookingQRData): Promise<string> {
	try {
		// Create a structured data object for the QR code
		const qrData = {
			type: "booking",
			reservationId: data.reservationId,
			reservationNumber: data.reservationNumber,
			customerName: data.customerName,
			serviceName: data.serviceName,
			date: data.date,
			time: data.time,
			participants: data.participants,
			totalAmount: data.totalAmount,
			verificationUrl: data.verificationUrl,
			timestamp: new Date().toISOString(),
		};

		// Convert to JSON string
		const qrContent = JSON.stringify(qrData);

		// Generate QR code as data URL
		const qrCodeDataURL = await QRCode.toDataURL(qrContent, {
			errorCorrectionLevel: "M",
			margin: 1,
			color: {
				dark: "#000000",
				light: "#FFFFFF",
			},
			width: 256,
		});

		return qrCodeDataURL;
	} catch (error) {
		console.error("Error generating QR code:", error);
		throw new Error("Failed to generate QR code");
	}
}

/**
 * Generate simple QR code for text content
 */
export async function generateSimpleQRCode(content: string): Promise<string> {
	try {
		const qrCodeDataURL = await QRCode.toDataURL(content, {
			errorCorrectionLevel: "M",
			margin: 1,
			color: {
				dark: "#000000",
				light: "#FFFFFF",
			},
			width: 256,
		});

		return qrCodeDataURL;
	} catch (error) {
		console.error("Error generating simple QR code:", error);
		throw new Error("Failed to generate QR code");
	}
}

/**
 * Parse QR code data back to booking information
 */
export function parseBookingQRData(qrContent: string): BookingQRData | null {
	try {
		const data = JSON.parse(qrContent);

		if (data.type !== "booking") {
			return null;
		}

		return {
			reservationId: data.reservationId,
			reservationNumber: data.reservationNumber,
			customerName: data.customerName,
			serviceName: data.serviceName,
			date: data.date,
			time: data.time,
			participants: data.participants,
			totalAmount: data.totalAmount,
			verificationUrl: data.verificationUrl,
		};
	} catch (error) {
		console.error("Error parsing QR code data:", error);
		return null;
	}
}

/**
 * Generate a verification URL for the booking
 */
export function generateVerificationUrl(reservationId: string, baseUrl: string): string {
	return `${baseUrl}/verify-booking/${reservationId}`;
}

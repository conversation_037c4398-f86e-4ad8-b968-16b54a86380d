/**
 * Generate a URL-friendly slug from a service name
 */
export function generateSlug(name: string): string {
	return name
		.toLowerCase()
		.normalize("NFD") // Decompose accented characters
		.replace(/[\u0300-\u036f]/g, "") // Remove diacritics
		.replace(/[^a-z0-9\s-]/g, "") // Remove special characters except spaces and hyphens
		.trim()
		.replace(/\s+/g, "-") // Replace spaces with hyphens
		.replace(/-+/g, "-"); // Replace multiple hyphens with single hyphen
}

/**
 * Extract service ID from a slug that contains both slug and ID
 * Format: "service-name-{id}"
 */
export function extractIdFromSlug(slug: string): string | null {
	// Look for UUID pattern at the end of the slug
	const uuidRegex = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
	const match = slug.match(uuidRegex);
	return match ? match[0] : null;
}

/**
 * Create a slug with just the service name
 * Format: "service-name"
 */
export function createServiceSlug(name: string, id: string): string {
	return generateSlug(name);
}

/**
 * Parse a service slug to get both name and ID
 */
export function parseServiceSlug(slug: string): { name: string; id: string } | null {
	const id = extractIdFromSlug(slug);
	if (!id) return null;

	const nameSlug = slug.replace(`-${id}`, "");
	return {
		name: nameSlug.replace(/-/g, " "),
		id,
	};
}

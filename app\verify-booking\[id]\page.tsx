"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, Calendar, Clock, Users, MapPin, Phone, Mail, Loader2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface BookingVerificationData {
  reservationNumber: string;
  customerName: string;
  email: string;
  phone: string;
  serviceName: string;
  date: string;
  time: string;
  participants: number;
  totalAmount: number;
  status: string;
  isValid: boolean;
}

export default function VerifyBookingPage() {
  const params = useParams();
  const [bookingData, setBookingData] = useState<BookingVerificationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBookingData = async () => {
      try {
        const response = await fetch(`/api/bookings/${params.id}`);
        const result = await response.json();

        if (result.success) {
          const data = result.data;
          setBookingData({
            reservationNumber: data.bookingNumber,
            customerName: data.customerName,
            email: data.email,
            phone: data.phone,
            serviceName: data.items[0]?.service || 'Service',
            date: data.items[0]?.date || '',
            time: data.items[0]?.time || '',
            participants: data.items[0]?.participants || 1,
            totalAmount: data.totalAmount,
            status: 'confirmed', // You might want to get this from the API
            isValid: true,
          });
        } else {
          setError("Réservation non trouvée");
        }
      } catch (err) {
        console.error("Error fetching booking:", err);
        setError("Erreur lors de la vérification de la réservation");
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchBookingData();
    }
  }, [params.id]);

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const [year, month, day] = dateString.split("-").map(Number);
    const date = new Date(year, month - 1, day);
    return date.toLocaleDateString("fr-FR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-emerald-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-emerald-600" />
            <p className="text-gray-600">Vérification de la réservation...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !bookingData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-emerald-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Réservation non trouvée</h1>
            <p className="text-gray-600 mb-6">{error || "Cette réservation n'existe pas ou a été supprimée."}</p>
            <Link href="/" className="text-emerald-600 hover:text-emerald-700 font-medium">
              Retour à l'accueil
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-emerald-50">
      {/* Header */}
      <header className="bg-white/90 backdrop-blur-md shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/images/logo-hd.png"
                alt="Soleil & Découverte"
                width={60}
                height={60}
                className="object-contain"
              />
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-yellow-500 bg-clip-text text-transparent">
                  Soleil & Découverte
                </h1>
                <p className="text-sm text-emerald-600">Vérification de réservation</p>
              </div>
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Verification Status */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-8"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className={`inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 ${
                bookingData.isValid 
                  ? 'bg-emerald-500 text-white' 
                  : 'bg-red-500 text-white'
              }`}
            >
              {bookingData.isValid ? (
                <CheckCircle className="w-10 h-10" />
              ) : (
                <XCircle className="w-10 h-10" />
              )}
            </motion.div>

            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {bookingData.isValid ? 'Réservation Valide' : 'Réservation Invalide'}
            </h1>
            <p className="text-xl text-gray-600 mb-2">
              {bookingData.isValid 
                ? `Réservation confirmée pour ${bookingData.customerName}`
                : 'Cette réservation ne peut pas être vérifiée'
              }
            </p>
            <p className="text-lg text-emerald-600 font-semibold">
              Numéro de réservation : {bookingData.reservationNumber}
            </p>
          </motion.div>

          {/* Booking Details */}
          {bookingData.isValid && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Détails de la réservation
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="font-semibold text-lg mb-2">{bookingData.serviceName}</h3>
                        <div className="space-y-2 text-sm text-gray-600">
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            {formatDate(bookingData.date)}
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="w-4 h-4" />
                            {bookingData.time}
                          </div>
                          <div className="flex items-center gap-2">
                            <Users className="w-4 h-4" />
                            {bookingData.participants} participant{bookingData.participants > 1 ? 's' : ''}
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-2">Informations client</h4>
                        <div className="space-y-2 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Nom :</span>
                            <span className="ml-2">{bookingData.customerName}</span>
                          </div>
                          <div>
                            <span className="font-medium">Email :</span>
                            <span className="ml-2">{bookingData.email}</span>
                          </div>
                          <div>
                            <span className="font-medium">Téléphone :</span>
                            <span className="ml-2">{bookingData.phone}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-semibold">Montant total</span>
                        <span className="text-2xl font-bold text-emerald-600">
                          {bookingData.totalAmount}€
                        </span>
                      </div>
                    </div>

                    <div className="flex justify-center">
                      <Badge 
                        variant={bookingData.status === 'confirmed' ? 'default' : 'secondary'}
                        className="px-4 py-2"
                      >
                        {bookingData.status === 'confirmed' ? 'Confirmée' : bookingData.status}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Back to Home */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-center mt-8"
          >
            <Link 
              href="/" 
              className="inline-flex items-center gap-2 text-emerald-600 hover:text-emerald-700 font-medium"
            >
              Retour à l'accueil
            </Link>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

import { googleReviewsService } from "@/lib/google-reviews-service";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// POST /api/cron/weekly-reviews - Cron job for weekly Google reviews refresh
export async function POST(request: NextRequest) {
	try {
		// Verify cron secret for security
		const cronSecret = request.headers.get("authorization");
		if (cronSecret !== `Bearer ${process.env.CRON_SECRET}`) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		console.log("🔄 Weekly Google Reviews refresh started via cron job");

		// Check if refresh is actually needed
		const needsRefresh = await googleReviewsService.needsWeeklyRefresh();
		
		if (!needsRefresh) {
			console.log("✅ Reviews are still fresh, skipping refresh");
			return NextResponse.json({
				success: true,
				message: "Reviews are still fresh, no refresh needed",
				refreshed: false,
				timestamp: new Date().toISOString(),
			});
		}

		// Perform the refresh
		const refreshResult = await googleReviewsService.refreshReviews();

		if (refreshResult.success) {
			const reviews = await googleReviewsService.getCachedReviews();
			console.log(`✅ Successfully refreshed ${refreshResult.count} Google reviews`);

			return NextResponse.json({
				success: true,
				message: `Weekly refresh completed: ${refreshResult.count} reviews updated`,
				refreshed: true,
				count: refreshResult.count,
				cachedCount: reviews.length,
				timestamp: new Date().toISOString(),
			});
		} else {
			console.error("❌ Weekly refresh failed:", refreshResult.error);
			return NextResponse.json(
				{
					success: false,
					error: "Weekly refresh failed",
					details: refreshResult.error,
					refreshed: false,
					count: 0,
				},
				{ status: 500 }
			);
		}
	} catch (error) {
		console.error("❌ Error in weekly cron job:", error);
		return NextResponse.json(
			{
				success: false,
				error: "Internal server error during weekly refresh",
				refreshed: false,
				count: 0,
			},
			{ status: 500 }
		);
	}
}

// GET /api/cron/weekly-reviews - Health check for cron job
export async function GET(request: NextRequest) {
	try {
		const needsRefresh = await googleReviewsService.needsWeeklyRefresh();
		const reviews = await googleReviewsService.getCachedReviews();
		
		return NextResponse.json({
			status: "healthy",
			needsRefresh,
			totalCachedReviews: reviews.length,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		console.error("Error in cron health check:", error);
		return NextResponse.json(
			{ status: "error", error: "Health check failed" },
			{ status: 500 }
		);
	}
}

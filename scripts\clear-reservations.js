#!/usr/bin/env node

/**
 * Debug Script: Clear All Reservation Data
 *
 * This script clears all reservation-related data from the database.
 * It handles foreign key constraints by deleting in the correct order.
 *
 * WARNING: This will permanently delete all reservation data!
 * Only use this for development/testing purposes.
 *
 * Usage:
 *   node scripts/clear-reservations-debug.js                    # Interactive mode with confirmation
 *   node scripts/clear-reservations-debug.js --force           # Skip confirmation
 *   node scripts/clear-reservations-debug.js --dry-run         # Show what would be deleted without deleting
 *   node scripts/clear-reservations-debug.js --table payments  # Clear specific table only
 */

const { createClient } = require("@supabase/supabase-js");
const readline = require("readline");

// Load environment variables from .env.local
const fs = require("fs");
const path = require("path");

function loadEnvFile() {
	const envPath = path.join(__dirname, "..", ".env.local");
	if (!fs.existsSync(envPath)) {
		console.error("❌ .env.local file not found");
		console.error("Please create .env.local with NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY");
		process.exit(1);
	}

	const envContent = fs.readFileSync(envPath, "utf8");
	const envVars = {};

	envContent.split("\n").forEach((line) => {
		const [key, ...valueParts] = line.split("=");
		if (key && valueParts.length > 0) {
			envVars[key.trim()] = valueParts.join("=").trim();
		}
	});

	return envVars;
}

const env = loadEnvFile();
const SUPABASE_URL = env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
	console.error("❌ Missing required environment variables in .env.local:");
	console.error("   NEXT_PUBLIC_SUPABASE_URL");
	console.error("   SUPABASE_SERVICE_ROLE_KEY");
	process.exit(1);
}

// Tables to clear in order (respecting foreign key constraints)
const TABLES_TO_CLEAR = [
	// Tables that reference reservations (delete first)
	{ name: "refunds", description: "Payment refunds" },
	{ name: "payments", description: "Payment records" },
	{ name: "equipment_reservations", description: "Equipment reservations" },
	{ name: "customer_feedback", description: "Customer feedback/reviews" },
	{ name: "reservation_status_history", description: "Reservation status changes" },
	{ name: "notifications", description: "Reservation notifications" },
	{ name: "customer_journey_events", description: "Customer journey tracking" },

	// Main reservations table
	{ name: "reservations", description: "Main reservations" },

	// Analytics tables that may reference reservations
	{ name: "customer_analytics", description: "Customer analytics data" },
	{ name: "service_analytics", description: "Service analytics data" },
	{ name: "employee_analytics", description: "Employee analytics data" },
	{ name: "daily_business_metrics", description: "Daily business metrics" },
	{ name: "equipment_utilization_history", description: "Equipment utilization history" },
];

// Parse command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes("--dry-run");
const isForced = args.includes("--force");
const showHelp = args.includes("--help") || args.includes("-h");
const specificTable = args.includes("--table") ? args[args.indexOf("--table") + 1] : null;

// Help function
function showHelpMessage() {
	console.log("🚨 RESERVATION DATA CLEANUP SCRIPT");
	console.log("=====================================");
	console.log("");
	console.log("This script clears all reservation-related data from the database.");
	console.log("⚠️  WARNING: This permanently deletes data! Use with caution!");
	console.log("");
	console.log("Usage:");
	console.log("  node scripts/clear-reservations-debug.js [options]");
	console.log("");
	console.log("Options:");
	console.log("  --help, -h          Show this help message");
	console.log("  --dry-run           Show what would be deleted without deleting");
	console.log("  --force             Skip confirmation prompt");
	console.log("  --table <n>      Clear specific table only");
	console.log("");
	console.log("Examples:");
	console.log("  node scripts/clear-reservations-debug.js --dry-run");
	console.log("  node scripts/clear-reservations-debug.js --force");
	console.log("  node scripts/clear-reservations-debug.js --table payments");
	console.log("");
	console.log("Available tables:");
	TABLES_TO_CLEAR.forEach((table) => {
		console.log(`  - ${table.name} (${table.description})`);
	});
}

// Helper function to ask for user confirmation
function askConfirmation(question) {
	const rl = readline.createInterface({
		input: process.stdin,
		output: process.stdout,
	});

	return new Promise((resolve) => {
		rl.question(question, (answer) => {
			rl.close();
			resolve(answer.toLowerCase().trim());
		});
	});
}

async function clearReservationData() {
	console.log("🚨 RESERVATION DATA CLEANUP SCRIPT");
	console.log("=====================================");

	if (isDryRun) {
		console.log("🔍 DRY RUN MODE - No data will be deleted");
	} else {
		console.log("⚠️  WARNING: This will delete ALL reservation-related data!");
		console.log("⚠️  This action cannot be undone!");
	}

	if (specificTable) {
		console.log(`🎯 Target: ${specificTable} table only`);
	}
	console.log("");

	// Ask for confirmation unless forced or dry run
	if (!isForced && !isDryRun) {
		const confirmation = await askConfirmation(
			"Are you sure you want to delete ALL reservation data? Type 'yes' to continue: "
		);

		if (confirmation !== "yes") {
			console.log("❌ Operation cancelled by user");
			return;
		}
		console.log("");
	}

	// Create Supabase client
	const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
		auth: {
			autoRefreshToken: false,
			persistSession: false,
		},
	});

	console.log("🔌 Connected to Supabase");
	console.log("");

	// Filter tables if specific table is requested
	const tablesToProcess = specificTable
		? TABLES_TO_CLEAR.filter((table) => table.name === specificTable)
		: TABLES_TO_CLEAR;

	if (specificTable && tablesToProcess.length === 0) {
		console.log(`❌ Table '${specificTable}' not found in the list of reservation-related tables`);
		console.log("Available tables:", TABLES_TO_CLEAR.map((t) => t.name).join(", "));
		return;
	}

	// Get initial counts
	console.log("📊 Getting initial data counts...");
	const initialCounts = {};

	for (const table of tablesToProcess) {
		try {
			const { count, error } = await supabase.from(table.name).select("*", { count: "exact", head: true });

			if (error) {
				console.log(`   ❌ Error counting ${table.name}: ${error.message}`);
				initialCounts[table.name] = "Error";
			} else {
				initialCounts[table.name] = count || 0;
				console.log(`   📋 ${table.name}: ${count || 0} records`);
			}
		} catch (err) {
			console.log(`   ❌ Error counting ${table.name}: ${err.message}`);
			initialCounts[table.name] = "Error";
		}
	}

	console.log("");
	console.log("🗑️  Starting deletion process...");
	console.log("");

	let totalDeleted = 0;
	const deletionResults = {};

	// Delete data in order
	for (const table of tablesToProcess) {
		try {
			const initialCount = initialCounts[table.name];
			const recordCount = typeof initialCount === "number" ? initialCount : 0;

			if (isDryRun) {
				console.log(`🔍 Would clear ${table.name} (${table.description}): ${recordCount} records`);
				deletionResults[table.name] = { success: true, deleted: recordCount, dryRun: true };
				totalDeleted += recordCount;
			} else {
				console.log(`🔄 Clearing ${table.name} (${table.description})...`);

				const { error } = await supabase
					.from(table.name)
					.delete()
					.neq("id", "00000000-0000-0000-0000-000000000000"); // Delete all records

				if (error) {
					console.log(`   ❌ Error deleting from ${table.name}: ${error.message}`);
					deletionResults[table.name] = { success: false, error: error.message };
				} else {
					console.log(`   ✅ Cleared ${table.name}: ${recordCount} records deleted`);
					deletionResults[table.name] = { success: true, deleted: recordCount };
					totalDeleted += recordCount;
				}
			}
		} catch (err) {
			console.log(`   ❌ Error clearing ${table.name}: ${err.message}`);
			deletionResults[table.name] = { success: false, error: err.message };
		}
	}

	console.log("");
	console.log("📊 CLEANUP SUMMARY");
	console.log("==================");
	if (isDryRun) {
		console.log(`Total records that would be deleted: ${totalDeleted}`);
	} else {
		console.log(`Total records deleted: ${totalDeleted}`);
	}
	console.log("");

	// Show detailed results
	for (const table of tablesToProcess) {
		const result = deletionResults[table.name];

		if (result?.success) {
			if (isDryRun) {
				console.log(`🔍 ${table.name}: ${result.deleted} would be deleted`);
			} else {
				console.log(`✅ ${table.name}: ${result.deleted} deleted`);
			}
		} else {
			console.log(`❌ ${table.name}: ${result?.error || "Unknown error"}`);
		}
	}

	if (!isDryRun) {
		console.log("");
		console.log("🔍 Verifying cleanup...");

		// Verify deletion
		let verificationPassed = true;
		for (const table of tablesToProcess) {
			try {
				const { count, error } = await supabase.from(table.name).select("*", { count: "exact", head: true });

				if (error) {
					console.log(`   ❌ Error verifying ${table.name}: ${error.message}`);
					verificationPassed = false;
				} else {
					const remaining = count || 0;
					if (remaining > 0) {
						console.log(`   ⚠️  ${table.name}: ${remaining} records remaining`);
						verificationPassed = false;
					} else {
						console.log(`   ✅ ${table.name}: Clean`);
					}
				}
			} catch (err) {
				console.log(`   ❌ Error verifying ${table.name}: ${err.message}`);
				verificationPassed = false;
			}
		}

		console.log("");
		if (verificationPassed) {
			console.log("🎉 All reservation data successfully cleared!");
		} else {
			console.log("⚠️  Some data may remain. Check the verification results above.");
		}
	}

	console.log("");
	if (isDryRun) {
		console.log("🔍 Dry run complete! Use --force to actually delete the data.");
	} else {
		console.log("✨ Cleanup complete!");
	}
}

// Run the script
if (require.main === module) {
	if (showHelp) {
		showHelpMessage();
		process.exit(0);
	}

	clearReservationData()
		.then(() => {
			console.log("Script completed successfully");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Script failed:", error);
			process.exit(1);
		});
}

module.exports = { clearReservationData };

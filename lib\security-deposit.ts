import { getStripeServer } from "./stripe";
import { supabaseAdmin } from "./supabase";

export interface SecurityDepositAuthorizationParams {
	amount: number; // in cents
	reservationId: string;
	customerEmail?: string;
	customerName?: string;
}

export interface SecurityDepositTransaction {
	id: string;
	reservation_id: string;
	transaction_type: "authorization" | "capture" | "release";
	amount: number;
	payment_intent_id?: string;
	status: "pending" | "succeeded" | "failed" | "canceled";
	processor_response?: any;
	created_at: string;
	updated_at: string;
}

/**
 * Create a Stripe authorization-hold for security deposit
 */
export const createSecurityDepositAuthorization = async (params: SecurityDepositAuthorizationParams) => {
	try {
		const stripe = getStripeServer();

		const paymentIntent = await stripe.paymentIntents.create({
			amount: params.amount,
			currency: "eur",
			capture_method: "manual", // Key for authorization-hold
			metadata: {
				type: "security_deposit_authorization",
				reservationId: params.reservationId,
				customerEmail: params.customerEmail || "",
				customerName: params.customerName || "",
			},
			description: `Caution d'équipement - Réservation ${params.reservationId}`,
			automatic_payment_methods: { enabled: true },
		});

		// Record transaction in database
		let transaction = null;
		if (supabaseAdmin) {
			const { data: transactionData, error } = await supabaseAdmin
				.from("security_deposit_transactions")
				.insert({
					reservation_id: params.reservationId,
					transaction_type: "authorization",
					amount: params.amount / 100, // Convert cents to euros for database
					payment_intent_id: paymentIntent.id,
					status: "pending",
					processor_response: {
						stripe_payment_intent_id: paymentIntent.id,
						stripe_status: paymentIntent.status,
					},
				})
				.select()
				.single();

			if (error) {
				console.error("Error recording security deposit transaction:", error);
				// Don't fail the authorization for database errors
			} else {
				transaction = transactionData;
			}
		}

		return {
			success: true,
			paymentIntent,
			clientSecret: paymentIntent.client_secret,
			transaction,
		};
	} catch (error) {
		console.error("Error creating security deposit authorization:", error);
		return {
			success: false,
			error: error instanceof Error ? error.message : "Unknown error",
		};
	}
};

/**
 * Capture an authorized security deposit (for damages)
 */
export const captureSecurityDeposit = async (paymentIntentId: string, amount?: number) => {
	try {
		const stripe = getStripeServer();

		const captureParams: any = {};
		if (amount) {
			captureParams.amount_to_capture = amount;
		}

		const paymentIntent = await stripe.paymentIntents.capture(paymentIntentId, captureParams);

		// Record capture transaction
		let transaction = null;
		if (supabaseAdmin) {
			const { data: transactionData, error } = await supabaseAdmin
				.from("security_deposit_transactions")
				.insert({
					reservation_id: paymentIntent.metadata.reservationId,
					transaction_type: "capture",
					amount: (amount || paymentIntent.amount) / 100,
					payment_intent_id: paymentIntentId,
					status: paymentIntent.status === "succeeded" ? "succeeded" : "failed",
					processor_response: {
						stripe_payment_intent_id: paymentIntentId,
						stripe_status: paymentIntent.status,
						captured_amount: paymentIntent.amount_received,
					},
				})
				.select()
				.single();

			if (error) {
				console.error("Error recording security deposit capture:", error);
			} else {
				transaction = transactionData;
			}

			// Update reservation status
			await supabaseAdmin
				.from("reservations")
				.update({
					security_deposit_status: "captured",
					updated_at: new Date().toISOString(),
				})
				.eq("security_deposit_payment_intent_id", paymentIntentId);
		}

		return {
			success: true,
			paymentIntent,
			transaction,
		};
	} catch (error) {
		console.error("Error capturing security deposit:", error);
		return {
			success: false,
			error: error instanceof Error ? error.message : "Unknown error",
		};
	}
};

/**
 * Release (cancel) an authorized security deposit
 */
export const releaseSecurityDeposit = async (paymentIntentId: string) => {
	try {
		const stripe = getStripeServer();

		const paymentIntent = await stripe.paymentIntents.cancel(paymentIntentId);

		// Record release transaction
		let transaction = null;
		if (supabaseAdmin) {
			const { data: transactionData, error } = await supabaseAdmin
				.from("security_deposit_transactions")
				.insert({
					reservation_id: paymentIntent.metadata.reservationId,
					transaction_type: "release",
					amount: paymentIntent.amount / 100,
					payment_intent_id: paymentIntentId,
					status: paymentIntent.status === "canceled" ? "succeeded" : "failed",
					processor_response: {
						stripe_payment_intent_id: paymentIntentId,
						stripe_status: paymentIntent.status,
					},
				})
				.select()
				.single();

			if (error) {
				console.error("Error recording security deposit release:", error);
			} else {
				transaction = transactionData;
			}

			// Update reservation status
			await supabaseAdmin
				.from("reservations")
				.update({
					security_deposit_status: "released",
					security_deposit_released_at: new Date().toISOString(),
					updated_at: new Date().toISOString(),
				})
				.eq("security_deposit_payment_intent_id", paymentIntentId);
		}

		return {
			success: true,
			paymentIntent,
			transaction,
		};
	} catch (error) {
		console.error("Error releasing security deposit:", error);
		return {
			success: false,
			error: error instanceof Error ? error.message : "Unknown error",
		};
	}
};

/**
 * Get security deposit transactions for a reservation
 */
export const getSecurityDepositTransactions = async (reservationId: string) => {
	try {
		if (!supabaseAdmin) {
			return { success: false, error: "Database connection not available" };
		}

		const { data: transactions, error } = await supabaseAdmin
			.from("security_deposit_transactions")
			.select("*")
			.eq("reservation_id", reservationId)
			.order("created_at", { ascending: false });

		if (error) {
			console.error("Error fetching security deposit transactions:", error);
			return { success: false, error: error.message };
		}

		return { success: true, transactions };
	} catch (error) {
		console.error("Error fetching security deposit transactions:", error);
		return {
			success: false,
			error: error instanceof Error ? error.message : "Unknown error",
		};
	}
};

/**
 * Convert euros to cents for Stripe
 */
export const eurosToCents = (euros: number): number => {
	return Math.round(euros * 100);
};

/**
 * Convert cents to euros for display
 */
export const centsToEuros = (cents: number): number => {
	return cents / 100;
};

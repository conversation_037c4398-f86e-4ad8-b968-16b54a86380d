import { supabase } from "./supabase";

// Types for Google Places API Reviews
export interface GooglePlacesReview {
	name: string;
	relativePublishTimeDescription: string;
	rating: number;
	text: {
		text: string;
		languageCode: string;
	};
	originalText: {
		text: string;
		languageCode: string;
	};
	authorAttribution: {
		displayName: string;
		uri: string;
		photoUri?: string;
	};
	publishTime: string;
}

export interface GooglePlacesResponse {
	reviews: GooglePlacesReview[];
}

export interface CachedReview {
	id: string;
	author_name: string;
	author_initials: string;
	rating: number;
	text: string;
	date: string;
	relative_time: string;
	profile_photo_url?: string;
	source: "google_places" | "google_business" | "manual";
	is_active: boolean;
	created_at: string;
	updated_at: string;
}

export interface ReviewsServiceConfig {
	googlePlacesApiKey?: string;
	googlePlaceId?: string;
	fallbackToCache: boolean;
	maxCacheAge: number; // in hours
	minRating?: number;
	maxReviews?: number;
}

class GoogleReviewsService {
	private config: ReviewsServiceConfig;

	constructor(config: ReviewsServiceConfig) {
		this.config = {
			maxReviews: 10,
			...config,
			fallbackToCache: config.fallbackToCache ?? true,
			maxCacheAge: config.maxCacheAge ?? 24, // 24 hours default
		};
	}

	/**
	 * Fetch reviews from Google Places API
	 */
	async fetchFromGooglePlaces(): Promise<GooglePlacesReview[]> {
		if (!this.config.googlePlacesApiKey || !this.config.googlePlaceId) {
			throw new Error("Google Places API key and Place ID are required");
		}

		const url = `https://places.googleapis.com/v1/places/${this.config.googlePlaceId}`;

		try {
			const response = await fetch(url, {
				method: "GET",
				headers: {
					"Content-Type": "application/json",
					"X-Goog-Api-Key": this.config.googlePlacesApiKey,
					"X-Goog-FieldMask": "reviews",
				},
			});

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(`Google Places API error: ${response.status} ${response.statusText} - ${errorText}`);
			}

			const data: GooglePlacesResponse = await response.json();
			return data.reviews || [];
		} catch (error) {
			console.error("Error fetching from Google Places API:", error);
			throw error;
		}
	}

	/**
	 * Transform Google Places review to our format
	 */
	private transformGooglePlacesReview(
		review: GooglePlacesReview
	): Omit<CachedReview, "id" | "created_at" | "updated_at"> {
		// Generate initials from display name
		const nameParts = review.authorAttribution.displayName.split(" ");
		const initials = nameParts
			.map((part) => part.charAt(0).toUpperCase())
			.join("")
			.substring(0, 2);

		return {
			author_name: review.authorAttribution.displayName,
			author_initials: initials,
			rating: review.rating,
			text: review.text?.text || review.originalText?.text || "",
			date: review.publishTime,
			relative_time: review.relativePublishTimeDescription,
			profile_photo_url: review.authorAttribution.photoUri || undefined,
			source: "google_places" as const,
			is_active: true,
		};
	}

	/**
	 * Cache reviews in database
	 */
	async cacheReviews(reviews: GooglePlacesReview[]): Promise<void> {
		if (!reviews.length) return;

		const transformedReviews = reviews.map((review) => ({
			...this.transformGooglePlacesReview(review),
			external_id: `google_places_${review.name}`,
		}));

		try {
			// Use upsert to avoid duplicates
			const { error } = await supabase.from("cached_reviews").upsert(transformedReviews, {
				onConflict: "external_id",
				ignoreDuplicates: false,
			});

			if (error) {
				console.error("Error caching reviews:", error);
				throw error;
			}

			console.log(`Successfully cached ${transformedReviews.length} reviews`);
		} catch (error) {
			console.error("Error caching reviews:", error);
			throw error;
		}
	}

	/**
	 * Get cached reviews from database
	 */
	async getCachedReviews(): Promise<CachedReview[]> {
		try {
			let query = supabase
				.from("cached_reviews")
				.select("*")
				.eq("is_active", true)
				.order("date", { ascending: false });

			// Apply rating filter if specified
			if (this.config.minRating) {
				query = query.gte("rating", this.config.minRating);
			}

			// Apply limit if specified
			if (this.config.maxReviews) {
				query = query.limit(this.config.maxReviews);
			}

			const { data, error } = await query;

			if (error) {
				console.error("Error fetching cached reviews:", error);
				throw error;
			}

			return data || [];
		} catch (error) {
			console.error("Error getting cached reviews:", error);
			throw error;
		}
	}

	/**
	 * Check if cached reviews are still fresh
	 */
	async isCacheFresh(): Promise<boolean> {
		try {
			const { data, error } = await supabase
				.from("cached_reviews")
				.select("updated_at")
				.order("updated_at", { ascending: false })
				.limit(1);

			if (error || !data || data.length === 0) {
				return false;
			}

			const lastUpdate = new Date(data[0].updated_at);
			const now = new Date();
			const hoursSinceUpdate = (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60);

			return hoursSinceUpdate < this.config.maxCacheAge;
		} catch (error) {
			console.error("Error checking cache freshness:", error);
			return false;
		}
	}

	/**
	 * Get reviews - always from cache only
	 * Reviews are refreshed weekly via cron job
	 */
	async getReviews(): Promise<CachedReview[]> {
		try {
			// Always load from cache only
			const cachedReviews = await this.getCachedReviews();

			if (cachedReviews.length > 0) {
				console.log(`Loaded ${cachedReviews.length} reviews from cache`);
				return cachedReviews;
			}

			// If no cached reviews, return empty array
			console.warn("No cached reviews available");
			return [];
		} catch (error) {
			console.error("Error loading cached reviews:", error);
			return [];
		}
	}

	/**
	 * Check if reviews need weekly refresh
	 */
	async needsWeeklyRefresh(): Promise<boolean> {
		try {
			const { data: latestReview } = await supabase
				.from("cached_reviews")
				.select("updated_at")
				.eq("source", "google_places")
				.order("updated_at", { ascending: false })
				.limit(1)
				.single();

			if (!latestReview) {
				// No Google Places reviews found, needs refresh
				return true;
			}

			const lastUpdate = new Date(latestReview.updated_at);
			const now = new Date();
			const daysSinceUpdate = (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24);

			// Refresh if more than 7 days old
			return daysSinceUpdate >= 7;
		} catch (error) {
			console.error("Error checking refresh status:", error);
			return true; // Default to refresh if check fails
		}
	}

	/**
	 * Refresh reviews manually
	 */
	async refreshReviews(): Promise<{ success: boolean; count: number; error?: string }> {
		try {
			if (!this.config.googlePlacesApiKey || !this.config.googlePlaceId) {
				throw new Error("Google Places API configuration missing");
			}

			const googleReviews = await this.fetchFromGooglePlaces();
			await this.cacheReviews(googleReviews);

			return {
				success: true,
				count: googleReviews.length,
			};
		} catch (error) {
			console.error("Error refreshing reviews:", error);
			return {
				success: false,
				count: 0,
				error: error instanceof Error ? error.message : "Unknown error",
			};
		}
	}
}

// Export singleton instance with safe configuration
export const googleReviewsService = new GoogleReviewsService({
	googlePlacesApiKey: process.env.GOOGLE_PLACES_API_KEY || undefined,
	googlePlaceId: process.env.GOOGLE_PLACE_ID || undefined,
	fallbackToCache: true,
	maxCacheAge: 168, // 7 days in hours (7 * 24)
	minRating: 4, // Only show 4+ star reviews
	maxReviews: 10,
});

export default GoogleReviewsService;

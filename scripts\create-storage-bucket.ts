import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createServiceImagesBucket() {
	try {
		// Create the bucket
		const { data: bucket, error: bucketError } = await supabase.storage.createBucket("service-images", {
			public: true,
			allowedMimeTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
			fileSizeLimit: 5242880, // 5MB
		});

		if (bucketError) {
			if (bucketError.message.includes("already exists")) {
				console.log("✅ Bucket 'service-images' already exists");
			} else {
				console.error("❌ Error creating bucket:", bucketError);
				return;
			}
		} else {
			console.log("✅ Created bucket 'service-images':", bucket);
		}

		// Set up RLS policies for the bucket
		const policies = [
			{
				name: "Allow public read access",
				definition: "true",
				command: "SELECT",
			},
			{
				name: "Allow authenticated users to upload",
				definition: "auth.role() = 'authenticated'",
				command: "INSERT",
			},
			{
				name: "Allow authenticated users to update their uploads",
				definition: "auth.role() = 'authenticated'",
				command: "UPDATE",
			},
			{
				name: "Allow authenticated users to delete their uploads",
				definition: "auth.role() = 'authenticated'",
				command: "DELETE",
			},
		];

		for (const policy of policies) {
			const { error: policyError } = await supabase.rpc("create_storage_policy", {
				bucket_name: "service-images",
				policy_name: policy.name,
				definition: policy.definition,
				command: policy.command,
			});

			if (policyError) {
				console.log(`⚠️  Policy '${policy.name}' might already exist or error:`, policyError.message);
			} else {
				console.log(`✅ Created policy: ${policy.name}`);
			}
		}

		console.log("🎉 Service images bucket setup complete!");
	} catch (error) {
		console.error("❌ Error setting up storage bucket:", error);
	}
}

// Run the script
createServiceImagesBucket();

"use client";

import { useEffect, useRef, useState } from "react";

interface UseVideoLoadingProps {
	src: string;
	autoPlay?: boolean;
	muted?: boolean;
	loop?: boolean;
	playsInline?: boolean;
}

export function useVideoLoading({
	src,
	autoPlay = true,
	muted = true,
	loop = true,
	playsInline = true,
}: UseVideoLoadingProps) {
	const videoRef = useRef<HTMLVideoElement>(null);
	const [isLoaded, setIsLoaded] = useState(false);
	const [isPlaying, setIsPlaying] = useState(false);
	const [hasError, setHasError] = useState(false);

	useEffect(() => {
		const video = videoRef.current;
		if (!video || !src) return;

		// Reset states when src changes
		setIsLoaded(false);
		setIsPlaying(false);
		setHasError(false);

		const handleLoadedData = () => {
			setIsLoaded(true);
			setHasError(false);
		};

		const handleCanPlay = () => {
			setIsLoaded(true);
			if (autoPlay) {
				video.play().catch(() => {
					setHasError(true);
				});
			}
		};

		const handlePlay = () => {
			setIsPlaying(true);
		};

		const handlePause = () => {
			setIsPlaying(false);
		};

		const handleError = () => {
			setHasError(true);
			setIsLoaded(false);
		};

		video.addEventListener("loadeddata", handleLoadedData);
		video.addEventListener("canplay", handleCanPlay);
		video.addEventListener("play", handlePlay);
		video.addEventListener("pause", handlePause);
		video.addEventListener("error", handleError);

		return () => {
			video.removeEventListener("loadeddata", handleLoadedData);
			video.removeEventListener("canplay", handleCanPlay);
			video.removeEventListener("play", handlePlay);
			video.removeEventListener("pause", handlePause);
			video.removeEventListener("error", handleError);
		};
	}, [autoPlay, src]);

	const videoProps = {
		ref: videoRef,
		autoPlay,
		muted,
		loop,
		playsInline,
		preload: "metadata" as const,
		style: {
			opacity: isLoaded && !hasError ? 1 : 0,
			transition: "opacity 1s ease-in-out",
		},
	};

	return {
		videoRef,
		videoProps,
		isLoaded,
		isPlaying,
		hasError,
	};
}

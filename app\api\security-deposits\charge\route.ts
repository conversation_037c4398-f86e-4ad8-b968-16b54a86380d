import { NextRequest, NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { getStripeServer } from "@/lib/stripe";
import { withAdminAuth } from "@/lib/admin-auth";

export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { reservationId } = await request.json();

		console.log("=== SECURITY DEPOSIT CHARGE DEBUG ===");
		console.log("Reservation ID:", reservationId);
		console.log("User:", user.email);

		if (!reservationId) {
			console.log("ERROR: No reservation ID provided");
			return NextResponse.json({ error: "Reservation ID is required" }, { status: 400 });
		}

		const supabaseAdmin = getSupabaseAdmin();

		// Get reservation with security deposit info
		const { data: reservation, error: reservationError } = await supabaseAdmin
			.from("reservations")
			.select(
				`
				id,
				security_deposit_status,
				security_deposit_payment_intent_id,
				service:services (
					security_deposit_amount
				)
			`
			)
			.eq("id", reservationId)
			.single();

		if (reservationError || !reservation) {
			console.log("ERROR: Reservation not found:", reservationError);
			return NextResponse.json({ error: "Reservation not found" }, { status: 404 });
		}

		console.log("Reservation found:", {
			id: reservation.id,
			security_deposit_status: reservation.security_deposit_status,
			security_deposit_payment_intent_id: reservation.security_deposit_payment_intent_id,
			security_deposit_amount: reservation.service?.security_deposit_amount,
		});

		// Check if deposit is authorized or pending with payment intent (meaning authorization was created)
		if (
			reservation.security_deposit_status !== "authorized" &&
			!(reservation.security_deposit_status === "pending" && reservation.security_deposit_payment_intent_id)
		) {
			console.log("ERROR: Security deposit not in chargeable state");
			return NextResponse.json({ error: "Security deposit must be authorized before charging" }, { status: 400 });
		}

		// If status is pending but we have a payment intent, check if Stripe status is actually requires_capture
		// This handles cases where the webhook didn't fire or failed to update our database
		if (reservation.security_deposit_status === "pending" && reservation.security_deposit_payment_intent_id) {
			console.log("Status is pending, checking if Stripe status is actually requires_capture...");

			const stripe = getStripeServer();
			const currentPaymentIntent = await stripe.paymentIntents.retrieve(
				reservation.security_deposit_payment_intent_id
			);

			console.log("Current Stripe status:", currentPaymentIntent.status);

			if (currentPaymentIntent.status === "requires_capture") {
				console.log("Stripe status is requires_capture, updating our database to authorized");

				// Update our database to match Stripe's status
				const { error: syncError } = await supabaseAdmin
					.from("reservations")
					.update({
						security_deposit_status: "authorized",
						updated_at: new Date().toISOString(),
					})
					.eq("id", reservationId);

				if (syncError) {
					console.error("Error syncing status:", syncError);
				} else {
					console.log("Successfully synced status to authorized");
				}

				// Update the transaction status as well
				await supabaseAdmin
					.from("security_deposit_transactions")
					.update({
						status: "succeeded",
						updated_at: new Date().toISOString(),
					})
					.eq("payment_intent_id", reservation.security_deposit_payment_intent_id)
					.eq("transaction_type", "authorization");
			}
		}

		if (!reservation.security_deposit_payment_intent_id) {
			console.log("ERROR: No payment intent ID found");
			return NextResponse.json({ error: "No payment intent found for security deposit" }, { status: 400 });
		}

		// Charge the security deposit using Stripe
		const stripe = getStripeServer();

		console.log("Attempting to capture payment intent:", reservation.security_deposit_payment_intent_id);

		try {
			const paymentIntent = await stripe.paymentIntents.capture(reservation.security_deposit_payment_intent_id);
			console.log("Capture successful:", {
				id: paymentIntent.id,
				status: paymentIntent.status,
				amount_received: paymentIntent.amount_received,
			});

			// Update reservation status
			const { error: updateError } = await supabaseAdmin
				.from("reservations")
				.update({
					security_deposit_status: "charged",
					updated_at: new Date().toISOString(),
				})
				.eq("id", reservationId);

			if (updateError) {
				console.error("Error updating reservation status:", updateError);
				// Don't fail the request as the charge was successful
			}

			// Record the charge transaction
			const { error: transactionError } = await supabaseAdmin.from("security_deposit_transactions").insert({
				reservation_id: reservationId,
				transaction_type: "charge",
				amount: (reservation.service as any)?.security_deposit_amount || 0,
				payment_intent_id: reservation.security_deposit_payment_intent_id,
				status: "completed",
				processor_response: {
					stripe_payment_intent_id: paymentIntent.id,
					stripe_status: paymentIntent.status,
				},
			});

			if (transactionError) {
				console.error("Error recording charge transaction:", transactionError);
				// Don't fail the request as the charge was successful
			}

			return NextResponse.json({
				success: true,
				message: "Security deposit charged successfully",
				paymentIntent: {
					id: paymentIntent.id,
					status: paymentIntent.status,
					amount: paymentIntent.amount,
				},
			});
		} catch (stripeError: any) {
			console.error("=== STRIPE CHARGE ERROR ===");
			console.error("Error details:", {
				message: stripeError.message,
				code: stripeError.code,
				type: stripeError.type,
				payment_intent: stripeError.payment_intent?.id,
			});
			console.error("Full error:", stripeError);

			// Record the failed charge attempt
			await supabaseAdmin.from("security_deposit_transactions").insert({
				reservation_id: reservationId,
				transaction_type: "charge",
				amount: (reservation.service as any)?.security_deposit_amount || 0,
				payment_intent_id: reservation.security_deposit_payment_intent_id,
				status: "failed",
				processor_response: {
					stripe_error: stripeError.message,
					stripe_code: stripeError.code,
				},
			});

			return NextResponse.json(
				{
					error: "Failed to charge security deposit",
					details: stripeError.message,
				},
				{ status: 400 }
			);
		}
	} catch (error) {
		console.error("Security deposit charge error:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}, "reservations:write");

"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { MapPin, Star } from "lucide-react";

export interface ReviewCardProps {
	name: string;
	initials: string;
	rating: number;
	comment: string;
	relativeTime: string;
	profilePhoto?: string;
	source?: string;
	showSource?: boolean;
	className?: string;
	animationDelay?: number;
}

export function ReviewCard({
	name,
	initials,
	rating,
	comment,
	relativeTime,
	profilePhoto,
	source = "Google",
	showSource = false,
	className = "",
	animationDelay = 0,
}: ReviewCardProps) {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			whileInView={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.6, delay: animationDelay }}
			className={className}
		>
			<Card className="h-full p-6 shadow-lg border-0 bg-white/90 backdrop-blur-sm hover:shadow-xl transition-shadow duration-300">
				<CardContent className="p-0 h-full flex flex-col">
					{/* Star Rating */}
					<div className="flex justify-center mb-4">
						{[...Array(5)].map((_, i) => (
							<Star
								key={i}
								className={`w-5 h-5 ${i < rating ? "text-yellow-400 fill-current" : "text-gray-300"}`}
							/>
						))}
					</div>

					{/* Review Text */}
					<blockquote className="text-gray-700 mb-6 italic leading-relaxed flex-grow text-center">
						"{comment}"
					</blockquote>

					{/* Author Info */}
					<div className="flex items-center justify-center space-x-3 mt-auto">
						{/* Profile Photo or Initials */}
						<div className="flex-shrink-0">
							{profilePhoto ? (
								<img
									src={profilePhoto}
									alt={name}
									className="w-10 h-10 rounded-full object-cover"
									onError={(e) => {
										// Hide the image and show initials fallback
										e.currentTarget.style.display = "none";
										const fallback = e.currentTarget.nextElementSibling as HTMLElement;
										if (fallback) fallback.style.display = "flex";
									}}
								/>
							) : null}
							<div
								className="w-10 h-10 rounded-full bg-emerald-100 flex items-center justify-center"
								style={{ display: profilePhoto ? "none" : "flex" }}
							>
								<span className="text-emerald-700 font-semibold text-sm">{initials}</span>
							</div>
						</div>

						<div className="text-center">
							<p className="font-semibold text-gray-900">{name}</p>
							<div className="flex items-center justify-center text-gray-500 text-sm">
								<MapPin className="w-3 h-3 mr-1" />
								{relativeTime}
							</div>
							{showSource && (
								<Badge variant="outline" className="mt-1 text-xs text-emerald-600 border-emerald-200">
									{source}
								</Badge>
							)}
						</div>
					</div>
				</CardContent>
			</Card>
		</motion.div>
	);
}

export default ReviewCard;

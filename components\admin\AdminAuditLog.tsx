"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from "@/lib/auth-context";
import { supabase } from "@/lib/supabase";
import { AlertCircle, ChevronLeft, ChevronRight, FileText, Loader2, Search, User } from "lucide-react";
import { useEffect, useState } from "react";

interface AuditLogEntry {
	id: string;
	admin_user_id: string | null;
	action: string;
	table_name: string | null;
	record_id: string | null;
	old_values: any;
	new_values: any;
	ip_address: string | null;
	user_agent: string | null;
	session_id: string | null;
	created_at: string | null;
	admin_user?: {
		id: string;
		first_name: string;
		last_name: string;
		email: string;
	};
}

interface AuditLogResponse {
	auditLogs: AuditLogEntry[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
	filters: {
		actions: string[];
		tableNames: string[];
	};
}

export default function AdminAuditLog() {
	const { session } = useAuth();
	const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [pagination, setPagination] = useState({
		page: 1,
		limit: 50,
		total: 0,
		totalPages: 0,
	});
	const [filters, setFilters] = useState({
		actions: [] as string[],
		tableNames: [] as string[],
	});

	// Filter states
	const [search, setSearch] = useState("");
	const [selectedAction, setSelectedAction] = useState("__all__");
	const [selectedTable, setSelectedTable] = useState("__all__");
	const [startDate, setStartDate] = useState("");
	const [endDate, setEndDate] = useState("");

	const fetchAuditLogs = async (page = 1) => {
		try {
			setLoading(true);
			setError(null);

			const params = new URLSearchParams({
				page: page.toString(),
				limit: pagination.limit.toString(),
			});

			if (search) params.append("search", search);
			if (selectedAction && selectedAction !== "__all__") params.append("action", selectedAction);
			if (selectedTable && selectedTable !== "__all__") params.append("table_name", selectedTable);
			if (startDate) params.append("start_date", startDate);
			if (endDate) params.append("end_date", endDate);

			// Get the current session token
			const {
				data: { session: currentSession },
			} = await supabase.auth.getSession();

			if (!currentSession?.access_token) {
				throw new Error("No authentication token available");
			}

			const response = await fetch(`/api/admin/audit-log?${params}`, {
				headers: {
					Authorization: `Bearer ${currentSession.access_token}`,
					"Content-Type": "application/json",
				},
			});
			if (!response.ok) {
				throw new Error("Failed to fetch audit logs");
			}
			const data: AuditLogResponse = await response.json();

			setAuditLogs(data.auditLogs);
			setPagination(data.pagination);
			setFilters(data.filters);
		} catch (err) {
			console.error("Error fetching audit logs:", err);
			setError("Erreur lors du chargement du journal d'audit");
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		// Only fetch when user is authenticated
		if (session?.access_token) {
			fetchAuditLogs();
		}
	}, [session]);

	const handleSearch = () => {
		fetchAuditLogs(1);
	};

	const handleClearFilters = () => {
		setSearch("");
		setSelectedAction("__all__");
		setSelectedTable("__all__");
		setStartDate("");
		setEndDate("");
		fetchAuditLogs(1);
	};

	const formatDate = (dateString: string | null) => {
		if (!dateString) return "N/A";
		return new Date(dateString).toLocaleString("fr-FR");
	};

	const getActionColor = (action: string) => {
		switch (action.toUpperCase()) {
			case "CREATE":
			case "INSERT":
				return "bg-green-100 text-green-800";
			case "UPDATE":
			case "PATCH":
				return "bg-blue-100 text-blue-800";
			case "DELETE":
				return "bg-red-100 text-red-800";
			case "EXPORT":
				return "bg-purple-100 text-purple-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const formatJsonData = (data: any) => {
		if (!data) return "N/A";
		try {
			return JSON.stringify(data, null, 2);
		} catch {
			return String(data);
		}
	};

	if (loading && auditLogs.length === 0) {
		return (
			<div className="flex items-center justify-center h-64">
				<Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Journal d'audit</h1>
					<p className="text-gray-600">Historique des actions administratives</p>
				</div>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Filters */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Search className="h-5 w-5" />
						Filtres
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
							<Input
								placeholder="Action ou table..."
								value={search}
								onChange={(e) => setSearch(e.target.value)}
								onKeyPress={(e) => e.key === "Enter" && handleSearch()}
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">Action</label>
							<Select value={selectedAction} onValueChange={setSelectedAction}>
								<SelectTrigger>
									<SelectValue placeholder="Toutes les actions" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="__all__">Toutes les actions</SelectItem>
									{filters.actions.map((action) => (
										<SelectItem key={action} value={action}>
											{action}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">Table</label>
							<Select value={selectedTable} onValueChange={setSelectedTable}>
								<SelectTrigger>
									<SelectValue placeholder="Toutes les tables" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="__all__">Toutes les tables</SelectItem>
									{filters.tableNames.map((tableName) => (
										<SelectItem key={tableName} value={tableName}>
											{tableName}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">Date début</label>
							<Input
								type="datetime-local"
								value={startDate}
								onChange={(e) => setStartDate(e.target.value)}
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">Date fin</label>
							<Input type="datetime-local" value={endDate} onChange={(e) => setEndDate(e.target.value)} />
						</div>

						<div className="flex items-end gap-2">
							<Button onClick={handleSearch} className="flex-1">
								<Search className="h-4 w-4 mr-2" />
								Rechercher
							</Button>
							<Button variant="outline" onClick={handleClearFilters}>
								Effacer
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Audit Log Table */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<FileText className="h-5 w-5" />
						Entrées du journal ({pagination.total})
					</CardTitle>
				</CardHeader>
				<CardContent>
					{auditLogs.length === 0 ? (
						<div className="text-center py-8">
							<FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
							<p className="text-gray-500">Aucune entrée trouvée</p>
						</div>
					) : (
						<div className="space-y-4">
							{auditLogs.map((entry) => (
								<div key={entry.id} className="border rounded-lg p-4 hover:bg-gray-50">
									<div className="flex items-start justify-between mb-3">
										<div className="flex items-center gap-3">
											<span
												className={`px-2 py-1 rounded-full text-xs font-medium ${getActionColor(
													entry.action
												)}`}
											>
												{entry.action}
											</span>
											{entry.table_name && (
												<span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
													{entry.table_name}
												</span>
											)}
										</div>
										<div className="text-sm text-gray-500">{formatDate(entry.created_at)}</div>
									</div>

									<div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
										<div>
											<div className="flex items-center gap-2 mb-2">
												<User className="h-4 w-4 text-gray-400" />
												<span className="font-medium">Utilisateur:</span>
												{entry.admin_user ? (
													<span>
														{entry.admin_user.first_name} {entry.admin_user.last_name} (
														{entry.admin_user.email})
													</span>
												) : (
													<span className="text-gray-500">Inconnu</span>
												)}
											</div>
											{entry.record_id && (
												<div className="text-gray-600">
													<span className="font-medium">ID enregistrement:</span>{" "}
													{entry.record_id}
												</div>
											)}
											{entry.ip_address && (
												<div className="text-gray-600">
													<span className="font-medium">IP:</span> {entry.ip_address}
												</div>
											)}
										</div>

										{(entry.old_values || entry.new_values) && (
											<div>
												{entry.old_values && (
													<details className="mb-2">
														<summary className="cursor-pointer font-medium text-gray-700">
															Anciennes valeurs
														</summary>
														<pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
															{formatJsonData(entry.old_values)}
														</pre>
													</details>
												)}
												{entry.new_values && (
													<details>
														<summary className="cursor-pointer font-medium text-gray-700">
															Nouvelles valeurs
														</summary>
														<pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
															{formatJsonData(entry.new_values)}
														</pre>
													</details>
												)}
											</div>
										)}
									</div>
								</div>
							))}
						</div>
					)}

					{/* Pagination */}
					{pagination.totalPages > 1 && (
						<div className="flex items-center justify-between mt-6 pt-4 border-t">
							<div className="text-sm text-gray-700">
								Page {pagination.page} sur {pagination.totalPages} ({pagination.total} entrées)
							</div>
							<div className="flex gap-2">
								<Button
									variant="outline"
									size="sm"
									onClick={() => fetchAuditLogs(pagination.page - 1)}
									disabled={pagination.page <= 1 || loading}
								>
									<ChevronLeft className="h-4 w-4" />
									Précédent
								</Button>
								<Button
									variant="outline"
									size="sm"
									onClick={() => fetchAuditLogs(pagination.page + 1)}
									disabled={pagination.page >= pagination.totalPages || loading}
								>
									Suivant
									<ChevronRight className="h-4 w-4" />
								</Button>
							</div>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}

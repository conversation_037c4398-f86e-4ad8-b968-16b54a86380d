import { supabase } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
	try {
		// Get query parameters for filtering
		const { searchParams } = new URL(request.url);
		const category = searchParams.get("category");
		const isActive = searchParams.get("active") !== "false"; // Default to true

		// Build the query
		let query = supabase
			.from("services")
			.select(
				`
        *,
        pricing_tiers (
          id,
          tier_name,
          price,
          min_age,
          max_age
        )
      `
			)
			.eq("is_active", isActive)
			.order("name");

		// Add category filter if provided
		if (category && category !== "all") {
			// Note: We'll need to add a category column to the services table
			// For now, we'll skip this filter
		}

		const { data: services, error } = await query;

		if (error) {
			console.error("Error fetching services:", error);
			return NextResponse.json({ error: "Failed to fetch services" }, { status: 500 });
		}

		// Transform the data to match the expected format
		console.log("=== SERVICES API DEBUG ===");
		console.log("Raw services data sample:", services?.[0]);

		const transformedServices =
			services?.map((service) => {
				console.log(`Service ${service.name} security deposit:`, {
					requires_security_deposit: service.requires_security_deposit,
					security_deposit_amount: service.security_deposit_amount,
				});

				return {
					id: service.id,
					name: service.name,
					description: service.description,
					duration_minutes: service.duration_minutes,
					base_price: service.base_price,
					max_participants: service.max_participants,
					min_age: service.min_age,
					max_age: service.max_age,
					is_family_friendly: service.is_family_friendly,
					is_active: service.is_active,
					image_url: service.image_url,
					fixed_price: service.fixed_price || false,
					pricing_tiers: service.pricing_tiers || [],
					// Security deposit fields
					requires_security_deposit: service.requires_security_deposit || false,
					security_deposit_amount: service.security_deposit_amount || 0,
					// Computed fields for UI compatibility
					duration: `${Math.floor(service.duration_minutes / 60)}h${
						service.duration_minutes % 60 > 0 ? ` ${service.duration_minutes % 60}min` : ""
					}`,
					capacity: `${service.max_participants} personnes max`,
					ageLimit: service.max_age
						? `${service.min_age}-${service.max_age} ans`
						: service.min_age === 0 || service.min_age === null
							? "Pour tous les âges"
							: `À partir de ${service.min_age} ans`,
					// Database fields
					category: service.category || "Service",
					location: service.location || "Petit-Canal",
					features: service.features || [],
					schedule: service.schedule || [],
					gallery: service.gallery || (service.image_url ? [service.image_url] : []),
					options: service.options || [],
				};
			}) || [];

		return NextResponse.json({
			services: transformedServices,
			total: transformedServices.length,
		});
	} catch (error) {
		console.error("Unexpected error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}

import { NextRequest, NextResponse } from 'next/server';
import { generatePaymentReceipt, generateReceiptHTML, generateReceiptText } from '@/lib/payment-receipt';

export async function GET(
  request: NextRequest,
  { params }: { params: { paymentIntentId: string } }
) {
  try {
    const { paymentIntentId } = params;
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'html';

    if (!paymentIntentId) {
      return NextResponse.json(
        { error: 'Payment intent ID is required' },
        { status: 400 }
      );
    }

    const receiptData = await generatePaymentReceipt(paymentIntentId);

    if (!receiptData) {
      return NextResponse.json(
        { error: 'Payment receipt not found or payment not successful' },
        { status: 404 }
      );
    }

    if (format === 'json') {
      return NextResponse.json({
        success: true,
        receipt: receiptData,
      });
    }

    if (format === 'text') {
      const textReceipt = generateReceiptText(receiptData);
      return new NextResponse(textReceipt, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Content-Disposition': `attachment; filename="receipt-${receiptData.reservationNumber}.txt"`,
        },
      });
    }

    // Default to HTML format
    const htmlReceipt = generateReceiptHTML(receiptData);
    return new NextResponse(htmlReceipt, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('Error generating payment receipt:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

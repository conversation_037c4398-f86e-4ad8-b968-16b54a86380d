import { calculateRemainingAmount } from "@/lib/deposit-settings";
import { createNotification, createPaymentReceivedNotification } from "@/lib/notifications";
import { confirmPaymentIntent, isPaymentFailed, isPaymentSuccessful } from "@/lib/stripe";
import { supabase, supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		const { paymentIntentId } = await request.json();

		console.log("=== PAYMENT CONFIRMATION API ===");
		console.log("Payment Intent ID:", paymentIntentId);

		if (!paymentIntentId) {
			console.log("ERROR: No payment intent ID provided");
			return NextResponse.json({ error: "Payment intent ID is required" }, { status: 400 });
		}

		// Confirm payment with Stripe
		console.log("Confirming payment with Stripe...");
		const result = await confirmPaymentIntent(paymentIntentId);

		console.log("Stripe confirmation result:", result);

		if (!result.success) {
			console.log("ERROR: Stripe confirmation failed:", result.error);
			return NextResponse.json({ error: result.error || "Failed to confirm payment" }, { status: 500 });
		}

		const { paymentIntent } = result;
		const paymentStatus = paymentIntent!.status;

		// Map Stripe status to our database status values
		const mapStripeStatusToDbStatus = (stripeStatus: string): string => {
			switch (stripeStatus) {
				case "succeeded":
					return "completed";
				case "processing":
					return "processing";
				case "requires_payment_method":
				case "requires_confirmation":
				case "requires_action":
				case "requires_capture":
					return "pending";
				case "canceled":
				case "payment_failed":
					return "failed";
				default:
					return "pending";
			}
		};

		// Use admin client for server-side operations
		const adminClient = supabaseAdmin || supabase;

		// Update payment record in database
		const updateData: any = {
			status: mapStripeStatusToDbStatus(paymentStatus),
			updated_at: new Date().toISOString(),
		};

		if (isPaymentSuccessful(paymentStatus)) {
			updateData.payment_date = new Date().toISOString();
		} else if (isPaymentFailed(paymentStatus)) {
			updateData.failure_reason = paymentIntent!.last_payment_error?.message || "Payment failed";
		}

		const { data: payment, error: paymentUpdateError } = await adminClient
			.from("payments")
			.update(updateData)
			.eq("payment_intent_id", paymentIntentId)
			.select("*, reservation:reservations!payments_reservation_id_fkey(*)")
			.single();

		if (paymentUpdateError) {
			console.error("Error updating payment record:", paymentUpdateError);
			return NextResponse.json({ error: "Failed to update payment record" }, { status: 500 });
		}

		// If payment successful, update reservation status and deposit information
		if (isPaymentSuccessful(paymentStatus)) {
			const reservationUpdate: any = {
				status: "confirmed",
				confirmed_at: new Date().toISOString(),
				updated_at: new Date().toISOString(),
			};

			// Handle deposit payments
			if (payment.is_deposit) {
				const remainingAmount = calculateRemainingAmount(payment.reservation.total_amount, payment.amount);
				reservationUpdate.deposit_amount = payment.amount;
				reservationUpdate.remaining_amount = remainingAmount;
				reservationUpdate.deposit_paid = true;
				reservationUpdate.deposit_payment_id = payment.id;
			}

			const { error: reservationUpdateError } = await adminClient
				.from("reservations")
				.update(reservationUpdate)
				.eq("id", payment.reservation_id);

			if (reservationUpdateError) {
				console.error("Error updating reservation status:", reservationUpdateError);
				// Don't fail the request as payment was successful
			}

			// Create payment received notification for admins
			try {
				const { data: adminProfiles } = await supabase.from("profiles").select("id").eq("role", "admin");

				if (adminProfiles) {
					const notificationTemplate = createPaymentReceivedNotification(
						payment.amount,
						payment.currency,
						payment.reservation_id
					);

					for (const admin of adminProfiles) {
						await createNotification(admin.id, notificationTemplate, payment.reservation_id);
					}
				}
			} catch (notificationError) {
				console.error("Error creating payment notification:", notificationError);
				// Don't fail the request
			}

			// Send all payment-related emails using batch function with rate limiting
			try {
				const { sendPaymentEmailBatch } = await import("@/lib/email-service");
				const { generateBookingConfirmationPDF } = await import("@/lib/pdf-generator");
				const { generateBookingQRCode, generateVerificationUrl } = await import("@/lib/qr-generator");
				const { appConfig } = await import("@/lib/config");

				// Get full reservation details for emails
				const { data: reservationDetails } = await adminClient
					.from("reservations")
					.select(
						`
						*,
						customer:customers(first_name, last_name, email, phone),
						service:services(name, requires_security_deposit, security_deposit_amount)
					`
					)
					.eq("id", payment.reservation_id)
					.single();

				if (reservationDetails && reservationDetails.customer) {
					const customerName = `${reservationDetails.customer.first_name} ${reservationDetails.customer.last_name}`;
					const serviceName = reservationDetails.service?.name || "Service";
					const date = new Date(reservationDetails.start_time).toLocaleDateString("fr-FR");
					const time = new Date(reservationDetails.start_time).toLocaleTimeString("fr-FR", {
						hour: "2-digit",
						minute: "2-digit",
					});

					// Generate PDF attachment for booking confirmation
					let pdfBuffer: Buffer | undefined;
					try {
						const qrCode = await generateBookingQRCode(reservationDetails.qr_code);
						const verificationUrl = generateVerificationUrl(reservationDetails.qr_code);

						const pdf = await generateBookingConfirmationPDF({
							reservationNumber: reservationDetails.reservation_number,
							customerName,
							customerEmail: reservationDetails.customer.email,
							customerPhone: reservationDetails.customer.phone,
							serviceName,
							date,
							time,
							participants: reservationDetails.participant_count,
							totalAmount: reservationDetails.total_amount,
							specialRequests: reservationDetails.special_requests,
							qrCodeData: {
								reservationId: reservationDetails.id,
								reservationNumber: reservationDetails.reservation_number,
								customerName,
								serviceName,
								date,
								time,
								participants: reservationDetails.participant_count,
								totalAmount: reservationDetails.total_amount,
								verificationUrl,
							},
							qrCode,
							verificationUrl,
							appUrl: appConfig.url,
						});

						pdfBuffer = Buffer.from(await pdf.arrayBuffer());
					} catch (pdfError) {
						console.error("Error generating PDF:", pdfError);
						// Continue without PDF attachment
					}

					// Get selected options from reservation
					let selectedOptions: Array<{ name: string; price: number; perParticipant?: boolean }> = [];
					if (reservationDetails.selected_options && Array.isArray(reservationDetails.selected_options)) {
						selectedOptions = reservationDetails.selected_options.map((option: any) => ({
							name: option.optionName,
							price: option.totalPrice,
							perParticipant: false, // This info is not stored in the simplified format
						}));
					}

					// Send all emails in batch with rate limiting
					const emailResults = await sendPaymentEmailBatch({
						customerEmail: reservationDetails.customer.email,
						customerName,
						bookingConfirmationData: {
							reservationNumber: reservationDetails.reservation_number,
							serviceName,
							date,
							time,
							participants: reservationDetails.participant_count,
							totalAmount: reservationDetails.total_amount,
							specialRequests: reservationDetails.special_requests,
							selectedOptions,
							// Security deposit information
							securityDepositRequired: reservationDetails.service?.requires_security_deposit || false,
							securityDepositAmount: reservationDetails.service?.security_deposit_amount || 0,
							securityDepositStatus: reservationDetails.security_deposit_status || "none",
						},
						adminNewReservationData: {
							reservationNumber: reservationDetails.reservation_number,
							customerName,
							customerEmail: reservationDetails.customer.email,
							serviceName,
							date,
							time,
							participants: reservationDetails.participant_count,
							totalAmount: reservationDetails.total_amount,
							specialRequests: reservationDetails.special_requests,
							// Security deposit information
							securityDepositRequired: reservationDetails.service?.requires_security_deposit || false,
							securityDepositAmount: reservationDetails.service?.security_deposit_amount || 0,
							securityDepositStatus: reservationDetails.security_deposit_status || "none",
						},
						adminPaymentReceivedData: {
							reservationNumber: reservationDetails.reservation_number,
							customerName,
							amount: payment.amount,
							currency: payment.currency.toUpperCase(),
							paymentDate: new Date().toLocaleDateString("fr-FR"),
							isDeposit: payment.is_deposit,
						},
						bookingPdfAttachment: pdfBuffer,
					});

					console.log("Payment email batch results:", emailResults);
				}
			} catch (emailError) {
				console.error("Error sending payment emails:", emailError);
				// Don't fail the request for email errors
			}

			// Add reservation status history
			try {
				await adminClient.from("reservation_status_history").insert({
					reservation_id: payment.reservation_id,
					old_status: "pending",
					new_status: "confirmed",
					changed_by: null, // System change
					change_reason: "Payment confirmed",
				});
			} catch (historyError) {
				console.error("Error adding status history:", historyError);
				// Don't fail the request
			}
		}

		return NextResponse.json({
			success: true,
			paymentStatus,
			paymentIntentId,
			reservationId: payment.reservation_id,
			amount: payment.amount,
			currency: payment.currency,
			isSuccessful: isPaymentSuccessful(paymentStatus),
			isFailed: isPaymentFailed(paymentStatus),
		});
	} catch (error) {
		console.error("Error confirming payment:", error);

		// Provide more specific error messages
		let errorMessage = "Internal server error";
		if (error instanceof Error) {
			errorMessage = error.message;
		}

		return NextResponse.json(
			{
				error: errorMessage,
				details: error instanceof Error ? error.message : "Unknown error occurred",
			},
			{ status: 500 }
		);
	}
}

import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { ensureSupabaseAdmin } from "@/lib/supabase-utils";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

type Employee = Database["public"]["Tables"]["employees"]["Row"];
type EmployeeInsert = Database["public"]["Tables"]["employees"]["Insert"];
type EmployeeUpdate = Database["public"]["Tables"]["employees"]["Update"];

// GET /api/admin/employees - List all employees with admin details
export const GET = withAdminAuth(async (request: NextRequest) => {
	try {
		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get("page") || "1");
		const limit = parseInt(searchParams.get("limit") || "20");
		const search = searchParams.get("search");
		const isActive = searchParams.get("active");
		const role = searchParams.get("role");
		const offset = (page - 1) * limit;

		// Build query
		const supabaseAdmin = ensureSupabaseAdmin();
		let query = supabaseAdmin
			.from("employees")
			.select(
				`
        *,
        employee_availability (
          id,
          day_of_week,
          start_time,
          end_time,
          is_available,
          effective_from,
          effective_until
        ),
        employee_service_qualifications (
          id,
          qualification_level,
          certified_date,
          expiry_date,
          notes,
          is_active,
          service:services (
            id,
            name,
            category
          )
        ),
        employee_time_off (
          id,
          start_date,
          end_date,
          start_time,
          end_time,
          reason,
          type,
          status
        )
      `
			)
			.order("created_at", { ascending: false })
			.range(offset, offset + limit - 1);

		// Apply filters
		if (search) {
			query = query.or(`first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%`);
		}
		if (isActive !== null) {
			query = query.eq("is_active", isActive === "true");
		}
		if (role && role !== "all") {
			query = query.eq("role", role);
		}

		const { data: employees, error } = await query;

		if (error) {
			console.error("Error fetching employees:", error);
			return NextResponse.json({ error: "Failed to fetch employees" }, { status: 500 });
		}

		// Get reservation statistics for each employee
		const employeeIds = employees?.map((e) => e.id) || [];
		const { data: reservationStats } = await supabaseAdmin
			.from("reservations")
			.select("assigned_employee_id, status, start_time")
			.in("assigned_employee_id", employeeIds);

		// Enhance employees with stats
		const enhancedEmployees = employees?.map((employee) => {
			const employeeReservations = reservationStats?.filter((r) => r.assigned_employee_id === employee.id) || [];
			const thisMonth = new Date();
			thisMonth.setDate(1);
			thisMonth.setHours(0, 0, 0, 0);

			return {
				...employee,
				stats: {
					totalReservations: employeeReservations.length,
					thisMonthReservations: employeeReservations.filter((r) => new Date(r.start_time) >= thisMonth)
						.length,
					upcomingReservations: employeeReservations.filter(
						(r) => new Date(r.start_time) > new Date() && r.status !== "cancelled"
					).length,
				},
			};
		});

		// Get total count for pagination
		const { count: totalCount } = await supabaseAdmin.from("employees").select("*", { count: "exact", head: true });

		return NextResponse.json({
			employees: enhancedEmployees || [],
			pagination: {
				page,
				limit,
				total: totalCount || 0,
				totalPages: Math.ceil((totalCount || 0) / limit),
			},
		});
	} catch (error) {
		console.error("Employees GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "employees:read");

// POST /api/admin/employees - Create new employee
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const supabaseAdmin = ensureSupabaseAdmin();
		const employeeData: EmployeeInsert = await request.json();

		// Validate required fields
		if (!employeeData.first_name || !employeeData.last_name || !employeeData.email || !employeeData.hire_date) {
			return NextResponse.json(
				{ error: "Missing required fields: first_name, last_name, email, hire_date" },
				{ status: 400 }
			);
		}

		// Check if email already exists
		const { data: existingEmployee } = await supabaseAdmin
			.from("employees")
			.select("id")
			.eq("email", employeeData.email)
			.single();

		if (existingEmployee) {
			return NextResponse.json({ error: "Employee with this email already exists" }, { status: 400 });
		}

		// Generate employee code if not provided
		if (!employeeData.employee_code) {
			const initials = `${employeeData.first_name[0]}${employeeData.last_name[0]}`.toUpperCase();
			const timestamp = Date.now().toString().slice(-4);
			employeeData.employee_code = `EMP-${initials}-${timestamp}`;
		}

		// Create employee
		const { data: employee, error } = await supabaseAdmin.from("employees").insert(employeeData).select().single();

		if (error) {
			console.error("Error creating employee:", error);
			return NextResponse.json({ error: "Failed to create employee" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(user.id, "CREATE", "employees", employee.id, null, employee, request);

		return NextResponse.json({ employee }, { status: 201 });
	} catch (error) {
		console.error("Employee POST error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "employees:write");

// PUT /api/admin/employees - Bulk update employees
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const supabaseAdmin = ensureSupabaseAdmin();
		const { employeeIds, updates }: { employeeIds: string[]; updates: EmployeeUpdate } = await request.json();

		if (!employeeIds || employeeIds.length === 0) {
			return NextResponse.json({ error: "No employee IDs provided" }, { status: 400 });
		}

		// Get current employees for audit log
		const { data: currentEmployees } = await supabaseAdmin.from("employees").select("*").in("id", employeeIds);

		// Update employees
		const { data: updatedEmployees, error } = await supabaseAdmin
			.from("employees")
			.update(updates)
			.in("id", employeeIds)
			.select();

		if (error) {
			console.error("Error updating employees:", error);
			return NextResponse.json({ error: "Failed to update employees" }, { status: 500 });
		}

		// Log admin actions
		for (const employee of updatedEmployees || []) {
			const oldEmployee = currentEmployees?.find((e: any) => e.id === employee.id);
			await logAdminAction(user.id, "UPDATE", "employees", employee.id, oldEmployee, employee, request);
		}

		return NextResponse.json({
			employees: updatedEmployees,
			updated: updatedEmployees?.length || 0,
		});
	} catch (error) {
		console.error("Employees PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "employees:write");

// DELETE /api/admin/employees - Deactivate employees
export const DELETE = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const supabaseAdmin = ensureSupabaseAdmin();
		const { employeeIds }: { employeeIds: string[] } = await request.json();

		if (!employeeIds || employeeIds.length === 0) {
			return NextResponse.json({ error: "No employee IDs provided" }, { status: 400 });
		}

		// Get employees for audit log before deactivation
		const { data: employeesToDeactivate } = await supabaseAdmin.from("employees").select("*").in("id", employeeIds);

		// Check for future reservations assigned to these employees
		const { data: futureReservations } = await supabaseAdmin
			.from("reservations")
			.select("id, assigned_employee_id")
			.in("assigned_employee_id", employeeIds)
			.gte("start_time", new Date().toISOString());

		if (futureReservations && futureReservations.length > 0) {
			return NextResponse.json(
				{
					error: "Cannot deactivate employees with future reservations",
					futureReservations: futureReservations.length,
				},
				{ status: 400 }
			);
		}

		// Soft delete by setting is_active to false
		const { error } = await supabaseAdmin
			.from("employees")
			.update({ is_active: false, is_available_for_scheduling: false })
			.in("id", employeeIds);

		if (error) {
			console.error("Error deactivating employees:", error);
			return NextResponse.json({ error: "Failed to deactivate employees" }, { status: 500 });
		}

		// Log admin actions
		for (const employee of employeesToDeactivate || []) {
			await logAdminAction(
				user.id,
				"DEACTIVATE",
				"employees",
				employee.id,
				employee,
				{ ...employee, is_active: false },
				request
			);
		}

		return NextResponse.json({
			deactivated: employeeIds.length,
			message: "Employees deactivated successfully",
		});
	} catch (error) {
		console.error("Employees DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "employees:write");

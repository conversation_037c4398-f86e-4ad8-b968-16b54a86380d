"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Edit, Plus, Save, Trash2, X } from "lucide-react";
import { ServiceOption } from "@/lib/types/service-options";
import { adminApi } from "@/lib/api-client";

interface OptionFormData {
	name: string;
	description: string;
	basePrice: number;
	quoteBased: boolean;
	perParticipant: boolean;
}

export default function ServiceOptionsManagement() {
	const [options, setOptions] = useState<ServiceOption[]>([]);
	const [loading, setLoading] = useState(true);
	const [isCreating, setIsCreating] = useState(false);
	const [editingOption, setEditingOption] = useState<ServiceOption | null>(null);

	const [newOption, setNewOption] = useState<OptionFormData>({
		name: "",
		description: "",
		basePrice: 0,
		quoteBased: false,
		perParticipant: false,
	});

	const [editOption, setEditOption] = useState<OptionFormData>({
		name: "",
		description: "",
		basePrice: 0,
		quoteBased: false,
		perParticipant: false,
	});

	useEffect(() => {
		loadOptions();
	}, []);

	const loadOptions = async () => {
		try {
			setLoading(true);
			const data = await adminApi.getServiceOptions();
			setOptions(data.options);
		} catch (error) {
			console.error("Error loading options:", error);
		} finally {
			setLoading(false);
		}
	};

	const createOption = async () => {
		try {
			const data = await adminApi.createServiceOption(newOption);
			setOptions([...options, data.option]);
			setNewOption({
				name: "",
				description: "",
				basePrice: 0,
				quoteBased: false,
				perParticipant: false,
			});
			setIsCreating(false);
		} catch (error) {
			console.error("Error creating option:", error);
		}
	};

	const startEditOption = (option: ServiceOption) => {
		setEditingOption(option);
		setEditOption({
			name: option.name,
			description: option.description || "",
			basePrice: option.basePrice,
			quoteBased: option.quoteBased,
			perParticipant: option.perParticipant,
		});
	};

	const saveEditOption = async () => {
		if (!editingOption) return;

		try {
			const data = await adminApi.updateServiceOption({
				id: editingOption.id,
				...editOption,
			});

			setOptions(options.map((opt) => (opt.id === editingOption.id ? data.option : opt)));

			setEditingOption(null);
		} catch (error) {
			console.error("Error updating option:", error);
		}
	};

	const cancelEditOption = () => {
		setEditingOption(null);
		setEditOption({
			name: "",
			description: "",
			basePrice: 0,
			quoteBased: false,
			perParticipant: false,
		});
	};

	const deleteOption = async (optionId: string) => {
		if (!confirm("Êtes-vous sûr de vouloir supprimer cette option ? Cette action est irréversible.")) {
			return;
		}

		try {
			await adminApi.deleteServiceOption(optionId);
			setOptions(options.filter((opt) => opt.id !== optionId));
		} catch (error) {
			console.error("Error deleting option:", error);
			alert("Erreur lors de la suppression de l'option. Elle est peut-être utilisée dans d'autres services.");
		}
	};

	if (loading) {
		return (
			<div className="container mx-auto py-8">
				<div className="text-center">Chargement...</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto py-8">
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold">Gestion des Options d'Activité</h1>
						<p className="text-gray-600">Créez et gérez les options disponibles pour vos activités</p>
					</div>
					{!isCreating && !editingOption && (
						<Button onClick={() => setIsCreating(true)}>
							<Plus className="h-4 w-4 mr-2" />
							Nouvelle option
						</Button>
					)}
				</div>

				{/* Create New Option */}
				{isCreating && (
					<Card>
						<CardHeader>
							<CardTitle>Créer une nouvelle option</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<Label htmlFor="option-name">Nom de l'option</Label>
									<Input
										id="option-name"
										value={newOption.name}
										onChange={(e) => setNewOption({ ...newOption, name: e.target.value })}
										placeholder="Ex: Masque et tuba"
									/>
								</div>
								<div>
									<Label htmlFor="option-price">Prix de base (€)</Label>
									<Input
										id="option-price"
										type="number"
										step="0.01"
										value={newOption.basePrice}
										onChange={(e) =>
											setNewOption({ ...newOption, basePrice: parseFloat(e.target.value) || 0 })
										}
									/>
								</div>
							</div>
							<div>
								<Label htmlFor="option-description">Description (optionnelle)</Label>
								<Textarea
									id="option-description"
									value={newOption.description}
									onChange={(e) => setNewOption({ ...newOption, description: e.target.value })}
									placeholder="Description de l'option"
								/>
							</div>
							<div className="flex items-center gap-6">
								<div className="flex items-center space-x-2">
									<Switch
										id="option-quote"
										checked={newOption.quoteBased}
										onCheckedChange={(checked) =>
											setNewOption({ ...newOption, quoteBased: checked })
										}
									/>
									<Label htmlFor="option-quote">Sur devis</Label>
								</div>
								<div className="flex items-center space-x-2">
									<Switch
										id="option-per-participant"
										checked={newOption.perParticipant}
										onCheckedChange={(checked) =>
											setNewOption({ ...newOption, perParticipant: checked })
										}
									/>
									<Label htmlFor="option-per-participant">Prix par participant</Label>
								</div>
							</div>
							<div className="flex justify-end gap-2">
								<Button variant="outline" onClick={() => setIsCreating(false)}>
									<X className="h-4 w-4 mr-2" />
									Annuler
								</Button>
								<Button onClick={createOption} disabled={!newOption.name}>
									<Save className="h-4 w-4 mr-2" />
									Créer l'option
								</Button>
							</div>
						</CardContent>
					</Card>
				)}

				{/* Edit Option */}
				{editingOption && (
					<Card>
						<CardHeader>
							<CardTitle>Modifier l'option: {editingOption.name}</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<Label htmlFor="edit-option-name">Nom de l'option</Label>
									<Input
										id="edit-option-name"
										value={editOption.name}
										onChange={(e) => setEditOption({ ...editOption, name: e.target.value })}
										placeholder="Ex: Masque et tuba"
									/>
								</div>
								<div>
									<Label htmlFor="edit-option-price">Prix de base (€)</Label>
									<Input
										id="edit-option-price"
										type="number"
										step="0.01"
										value={editOption.basePrice}
										onChange={(e) =>
											setEditOption({ ...editOption, basePrice: parseFloat(e.target.value) || 0 })
										}
									/>
								</div>
							</div>
							<div>
								<Label htmlFor="edit-option-description">Description (optionnelle)</Label>
								<Textarea
									id="edit-option-description"
									value={editOption.description}
									onChange={(e) => setEditOption({ ...editOption, description: e.target.value })}
									placeholder="Description de l'option"
								/>
							</div>
							<div className="flex items-center gap-6">
								<div className="flex items-center space-x-2">
									<Switch
										id="edit-option-quote"
										checked={editOption.quoteBased}
										onCheckedChange={(checked) =>
											setEditOption({ ...editOption, quoteBased: checked })
										}
									/>
									<Label htmlFor="edit-option-quote">Sur devis</Label>
								</div>
								<div className="flex items-center space-x-2">
									<Switch
										id="edit-option-per-participant"
										checked={editOption.perParticipant}
										onCheckedChange={(checked) =>
											setEditOption({ ...editOption, perParticipant: checked })
										}
									/>
									<Label htmlFor="edit-option-per-participant">Prix par participant</Label>
								</div>
							</div>
							<div className="flex justify-end gap-2">
								<Button variant="outline" onClick={cancelEditOption}>
									<X className="h-4 w-4 mr-2" />
									Annuler
								</Button>
								<Button onClick={saveEditOption} disabled={!editOption.name}>
									<Save className="h-4 w-4 mr-2" />
									Sauvegarder
								</Button>
							</div>
						</CardContent>
					</Card>
				)}

				{/* Options List */}
				<Card>
					<CardHeader>
						<CardTitle>Options existantes ({options.length})</CardTitle>
					</CardHeader>
					<CardContent>
						{options.length === 0 ? (
							<div className="text-center py-8 text-gray-500">
								Aucune option créée. Commencez par créer votre première option.
							</div>
						) : (
							<div className="space-y-3">
								{options.map((option) => (
									<div
										key={option.id}
										className="flex items-center justify-between p-4 border rounded-lg"
									>
										<div className="flex-1">
											<div className="flex items-center gap-3">
												<h3 className="font-medium">{option.name}</h3>
												<div className="flex gap-2">
													{option.quoteBased && <Badge variant="secondary">Sur devis</Badge>}
													{option.perParticipant && (
														<Badge variant="outline">Par participant</Badge>
													)}
												</div>
											</div>
											<div className="text-sm text-gray-600 mt-1">
												Prix:{" "}
												{option.quoteBased && option.basePrice === 0
													? "sur devis"
													: `${option.basePrice}€`}
												{option.perParticipant && " par participant"}
											</div>
											{option.description && (
												<div className="text-sm text-gray-500 mt-1">{option.description}</div>
											)}
										</div>
										<div className="flex items-center gap-2">
											<Button variant="ghost" size="sm" onClick={() => startEditOption(option)}>
												<Edit className="h-4 w-4" />
											</Button>
											<Button
												variant="ghost"
												size="sm"
												onClick={() => deleteOption(option.id)}
												className="text-red-600 hover:text-red-700"
											>
												<Trash2 className="h-4 w-4" />
											</Button>
										</div>
									</div>
								))}
							</div>
						)}
					</CardContent>
				</Card>
			</div>
		</div>
	);
}

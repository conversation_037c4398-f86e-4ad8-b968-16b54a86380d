"use client";

import { adminApi } from "@/lib/api-client";
import {
	AlertCircle,
	Calendar,
	Download,
	Edit,
	Eye,
	Filter,
	Loader2,
	Mail,
	MapPin,
	Phone,
	Plus,
	Search,
	Star,
	Trash2,
	TrendingUp,
	Users,
} from "lucide-react";
import { useEffect, useState } from "react";
import Button from "./ui/Button";

interface CustomerData {
	id: string;
	first_name?: string;
	last_name?: string;
	email?: string;
	phone?: string;
	date_of_birth?: string;
	nationality?: string;
	address?: string;
	city?: string;
	postal_code?: string;
	country?: string;
	emergency_contact_name?: string;
	emergency_contact_phone?: string;
	dietary_restrictions?: string[];
	medical_conditions?: string[];
	preferred_language?: string;
	marketing_consent?: boolean;
	created_at: string;
	updated_at: string;
	total_spent?: number;
	total_bookings?: number;
	last_booking_date?: string;
	status?: string;
}

interface EditClientModalProps {
	client: CustomerData;
	isOpen: boolean;
	onClose: () => void;
	onSave: (data: Partial<CustomerData>) => void;
	isLoading: boolean;
}

const EditClientModal = ({ client, isOpen, onClose, onSave, isLoading }: EditClientModalProps) => {
	const [formData, setFormData] = useState({
		first_name: client.first_name || "",
		last_name: client.last_name || "",
		email: client.email || "",
		phone: client.phone || "",
		date_of_birth: client.date_of_birth || "",
		nationality: client.nationality || "",
		emergency_contact_name: client.emergency_contact_name || "",
		emergency_contact_phone: client.emergency_contact_phone || "",
		dietary_restrictions: Array.isArray(client.dietary_restrictions)
			? client.dietary_restrictions.join(", ")
			: client.dietary_restrictions || "",
		medical_conditions: Array.isArray(client.medical_conditions)
			? client.medical_conditions.join(", ")
			: client.medical_conditions || "",
		marketing_consent: client.marketing_consent || false,
	});

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();

		// Convert dietary_restrictions and medical_conditions back to arrays if they contain commas
		// Convert empty strings to null for date fields to avoid database errors
		const processedData = {
			...formData,
			date_of_birth: formData.date_of_birth?.trim() || undefined,
			dietary_restrictions: formData.dietary_restrictions
				? formData.dietary_restrictions
						.split(",")
						.map((item) => item.trim())
						.filter(Boolean)
				: [],
			medical_conditions: formData.medical_conditions
				? formData.medical_conditions
						.split(",")
						.map((item) => item.trim())
						.filter(Boolean)
				: [],
		};

		onSave(processedData);
	};

	if (!isOpen) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
			<div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
				<div className="p-6 border-b border-gray-200">
					<h2 className="text-xl font-bold text-gray-900">Modifier le client</h2>
				</div>

				<form onSubmit={handleSubmit} className="p-6 space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Prénom <span className="text-red-500">*</span>
							</label>
							<input
								type="text"
								value={formData.first_name}
								onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								required
								disabled={isLoading}
							/>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Nom <span className="text-red-500">*</span>
							</label>
							<input
								type="text"
								value={formData.last_name}
								onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								required
								disabled={isLoading}
							/>
						</div>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Email <span className="text-red-500">*</span>
							</label>
							<input
								type="email"
								value={formData.email}
								onChange={(e) => setFormData({ ...formData, email: e.target.value })}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								required
								disabled={isLoading}
							/>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Téléphone <span className="text-red-500">*</span>
							</label>
							<input
								type="tel"
								value={formData.phone}
								onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								required
								disabled={isLoading}
							/>
						</div>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">Date de naissance</label>
							<input
								type="date"
								value={formData.date_of_birth}
								onChange={(e) => setFormData({ ...formData, date_of_birth: e.target.value })}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								disabled={isLoading}
							/>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">Nationalité</label>
							<input
								type="text"
								value={formData.nationality}
								onChange={(e) => setFormData({ ...formData, nationality: e.target.value })}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								disabled={isLoading}
							/>
						</div>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">Contact d'urgence</label>
							<input
								type="text"
								value={formData.emergency_contact_name}
								onChange={(e) => setFormData({ ...formData, emergency_contact_name: e.target.value })}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								placeholder="Nom du contact"
								disabled={isLoading}
							/>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">Téléphone d'urgence</label>
							<input
								type="tel"
								value={formData.emergency_contact_phone}
								onChange={(e) => setFormData({ ...formData, emergency_contact_phone: e.target.value })}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								placeholder="Téléphone du contact"
								disabled={isLoading}
							/>
						</div>
					</div>

					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Restrictions alimentaires
						</label>
						<input
							type="text"
							value={formData.dietary_restrictions}
							onChange={(e) => setFormData({ ...formData, dietary_restrictions: e.target.value })}
							className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
							placeholder="Séparez par des virgules"
							disabled={isLoading}
						/>
					</div>

					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">Conditions médicales</label>
						<input
							type="text"
							value={formData.medical_conditions}
							onChange={(e) => setFormData({ ...formData, medical_conditions: e.target.value })}
							className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
							placeholder="Séparez par des virgules"
							disabled={isLoading}
						/>
					</div>

					<div className="flex items-center">
						<input
							type="checkbox"
							id="marketing_consent"
							checked={formData.marketing_consent}
							onChange={(e) => setFormData({ ...formData, marketing_consent: e.target.checked })}
							className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
							disabled={isLoading}
						/>
						<label htmlFor="marketing_consent" className="ml-2 block text-sm text-gray-900">
							Consentement marketing
						</label>
					</div>

					<div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
						<button
							type="button"
							onClick={onClose}
							className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
							disabled={isLoading}
						>
							Annuler
						</button>
						<button
							type="submit"
							disabled={isLoading}
							className="px-4 py-2 text-sm font-medium text-white bg-emerald-600 border border-transparent rounded-md hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
						>
							{isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
							<span>Sauvegarder</span>
						</button>
					</div>
				</form>
			</div>
		</div>
	);
};

interface DeleteConfirmModalProps {
	client: CustomerData;
	isOpen: boolean;
	onClose: () => void;
	onConfirm: () => void;
	isLoading: boolean;
}

const DeleteConfirmModal = ({ client, isOpen, onClose, onConfirm, isLoading }: DeleteConfirmModalProps) => {
	if (!isOpen) return null;

	const clientName = `${client.first_name || ""} ${client.last_name || ""}`.trim() || "Client inconnu";

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
			<div className="bg-white rounded-xl shadow-xl max-w-md w-full">
				<div className="p-6">
					<div className="flex items-center mb-4">
						<div className="flex-shrink-0">
							<AlertCircle className="h-6 w-6 text-red-600" />
						</div>
						<div className="ml-3">
							<h3 className="text-lg font-medium text-gray-900">Confirmer la suppression</h3>
						</div>
					</div>

					<div className="mb-6">
						<p className="text-sm text-gray-500">
							Êtes-vous sûr de vouloir supprimer le client <strong>{clientName}</strong> ?
						</p>
						<p className="text-sm text-gray-500 mt-2">
							Cette action anonymisera les données du client de manière permanente. Les réservations
							associées seront conservées mais anonymisées.
						</p>
					</div>

					<div className="flex justify-end space-x-3">
						<button
							type="button"
							onClick={onClose}
							className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
							disabled={isLoading}
						>
							Annuler
						</button>
						<button
							type="button"
							onClick={onConfirm}
							disabled={isLoading}
							className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
						>
							{isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
							<span>Supprimer</span>
						</button>
					</div>
				</div>
			</div>
		</div>
	);
};

const AdminClients = () => {
	const [clients, setClients] = useState<CustomerData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [selectedClient, setSelectedClient] = useState<CustomerData | null>(null);
	const [filterStatus, setFilterStatus] = useState<string>("all");
	const [searchTerm, setSearchTerm] = useState("");
	const [sortBy, setSortBy] = useState<"name" | "total_spent" | "last_booking_date" | "total_bookings">("name");
	const [editingClient, setEditingClient] = useState<CustomerData | null>(null);
	const [deleteConfirmClient, setDeleteConfirmClient] = useState<CustomerData | null>(null);
	const [isUpdating, setIsUpdating] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);
	const [successMessage, setSuccessMessage] = useState<string | null>(null);

	useEffect(() => {
		fetchCustomers();
	}, []);

	const fetchCustomers = async () => {
		try {
			setLoading(true);
			setError(null);
			const response = await adminApi.getCustomers({
				limit: 100,
				page: 1,
			});
			if (response?.customers) {
				setClients(response.customers);
			}
		} catch (err) {
			console.error("Error fetching customers:", err);
			setError("Erreur lors du chargement des clients");
		} finally {
			setLoading(false);
		}
	};

	// Helper functions
	const getClientType = (totalSpent: number) => {
		if (totalSpent >= 1000) return "VIP";
		if (totalSpent >= 500) return "Premium";
		return "Standard";
	};

	const getClientTypeColor = (totalSpent: number) => {
		if (totalSpent >= 1000) return "bg-purple-100 text-purple-800";
		if (totalSpent >= 500) return "bg-blue-100 text-blue-800";
		return "bg-gray-100 text-gray-800";
	};

	const getStatusColor = (status: string) => {
		return status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800";
	};

	const getStatusText = (status: string) => {
		return status === "active" ? "Actif" : "Inactif";
	};

	const handleViewDetails = (client: CustomerData) => {
		setSelectedClient(client);
	};

	const handleEditClient = (client: CustomerData) => {
		setEditingClient(client);
		setError(null);
		setSuccessMessage(null);
	};

	const handleDeleteClient = (client: CustomerData) => {
		setDeleteConfirmClient(client);
		setError(null);
		setSuccessMessage(null);
	};

	const handleUpdateClient = async (updatedData: Partial<CustomerData>) => {
		if (!editingClient) return;

		setIsUpdating(true);
		setError(null);

		try {
			await adminApi.updateCustomer(editingClient.id, updatedData);

			// Update the client in the local state
			setClients((prev) =>
				prev.map((client) => (client.id === editingClient.id ? { ...client, ...updatedData } : client))
			);

			setEditingClient(null);
			setSuccessMessage("Client mis à jour avec succès");

			// Clear success message after 3 seconds
			setTimeout(() => setSuccessMessage(null), 3000);
		} catch (err) {
			console.error("Error updating customer:", err);
			setError(err instanceof Error ? err.message : "Erreur lors de la mise à jour du client");
		} finally {
			setIsUpdating(false);
		}
	};

	const handleConfirmDelete = async () => {
		if (!deleteConfirmClient) return;

		setIsDeleting(true);
		setError(null);

		try {
			await adminApi.deleteCustomer(deleteConfirmClient.id);

			// Remove the client from the local state
			setClients((prev) => prev.filter((client) => client.id !== deleteConfirmClient.id));

			setDeleteConfirmClient(null);
			setSuccessMessage("Client supprimé avec succès");

			// Clear success message after 3 seconds
			setTimeout(() => setSuccessMessage(null), 3000);
		} catch (err) {
			console.error("Error deleting customer:", err);
			setError(err instanceof Error ? err.message : "Erreur lors de la suppression du client");
		} finally {
			setIsDeleting(false);
		}
	};

	const getFullName = (client: CustomerData) => {
		return `${client.first_name || ""} ${client.last_name || ""}`.trim() || "Client inconnu";
	};

	// Filter and sort clients
	const filteredClients = clients
		.filter((client) => {
			const fullName = getFullName(client);
			const matchesSearch =
				fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
				(client.email && client.email.toLowerCase().includes(searchTerm.toLowerCase()));
			const matchesStatus = filterStatus === "all" || client.status === filterStatus;
			return matchesSearch && matchesStatus;
		})
		.sort((a, b) => {
			switch (sortBy) {
				case "name":
					return getFullName(a).localeCompare(getFullName(b));
				case "total_spent":
					return (b.total_spent || 0) - (a.total_spent || 0);
				case "last_booking_date":
					const dateA = a.last_booking_date ? new Date(a.last_booking_date).getTime() : 0;
					const dateB = b.last_booking_date ? new Date(b.last_booking_date).getTime() : 0;
					return dateB - dateA;
				case "total_bookings":
					return (b.total_bookings || 0) - (a.total_bookings || 0);
				default:
					return 0;
			}
		});

	// Calculate statistics
	const totalClients = clients.length;
	const activeClients = clients.filter((c) => c.status === "active").length;
	const vipClients = clients.filter((c) => (c.total_spent || 0) >= 1000).length;
	const averageSpent =
		totalClients > 0 ? clients.reduce((sum, c) => sum + (c.total_spent || 0), 0) / totalClients : 0;
	const totalRevenue = clients.reduce((sum, c) => sum + (c.total_spent || 0), 0);

	if (loading) {
		return (
			<div className="p-6">
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Clients</h1>
					<p className="text-gray-600">Suivez et gérez votre base de clients</p>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="flex items-center space-x-2">
						<Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
						<span className="text-gray-600">Chargement des clients...</span>
					</div>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="p-6">
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Clients</h1>
					<p className="text-gray-600">Suivez et gérez votre base de clients</p>
				</div>
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Clients</h1>
					<p className="text-gray-600">Suivez et gérez votre base de clients</p>
				</div>
				<Button icon={Plus}>Nouveau Client</Button>
			</div>

			{/* Success Message */}
			{successMessage && (
				<div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
								<path
									fillRule="evenodd"
									d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
									clipRule="evenodd"
								/>
							</svg>
						</div>
						<div className="ml-3">
							<p className="text-sm font-medium text-green-800">{successMessage}</p>
						</div>
					</div>
				</div>
			)}

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Statistics Cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm font-medium text-gray-600">Total Clients</p>
							<p className="text-2xl font-bold text-gray-900">{totalClients}</p>
						</div>
						<div className="bg-blue-100 p-3 rounded-lg">
							<Users className="h-6 w-6 text-blue-600" />
						</div>
					</div>
				</div>

				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm font-medium text-gray-600">Clients Actifs</p>
							<p className="text-2xl font-bold text-gray-900">{activeClients}</p>
						</div>
						<div className="bg-green-100 p-3 rounded-lg">
							<TrendingUp className="h-6 w-6 text-green-600" />
						</div>
					</div>
				</div>

				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm font-medium text-gray-600">Clients VIP</p>
							<p className="text-2xl font-bold text-gray-900">{vipClients}</p>
						</div>
						<div className="bg-purple-100 p-3 rounded-lg">
							<Star className="h-6 w-6 text-purple-600" />
						</div>
					</div>
				</div>

				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm font-medium text-gray-600">Dépense Moyenne</p>
							<p className="text-2xl font-bold text-gray-900">€{Math.round(averageSpent)}</p>
						</div>
						<div className="bg-yellow-100 p-3 rounded-lg">
							<TrendingUp className="h-6 w-6 text-yellow-600" />
						</div>
					</div>
				</div>

				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm font-medium text-gray-600">Chiffre d'Affaires</p>
							<p className="text-2xl font-bold text-gray-900">€{totalRevenue}</p>
						</div>
						<div className="bg-emerald-100 p-3 rounded-lg">
							<TrendingUp className="h-6 w-6 text-emerald-600" />
						</div>
					</div>
				</div>
			</div>

			{/* Filters and Controls */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
				<div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
					<div className="flex flex-col sm:flex-row gap-4 flex-1">
						<div className="relative flex-1">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
							<input
								type="text"
								placeholder="Rechercher par nom ou email..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>
						<select
							value={filterStatus}
							onChange={(e) => setFilterStatus(e.target.value)}
							className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						>
							<option value="all">Tous les statuts</option>
							<option value="active">Actif</option>
							<option value="inactive">Inactif</option>
						</select>
						<select
							value={sortBy}
							onChange={(e) => setSortBy(e.target.value as any)}
							className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						>
							<option value="name">Trier par nom</option>
							<option value="total_spent">Trier par dépenses</option>
							<option value="last_booking_date">Trier par dernière visite</option>
							<option value="total_bookings">Trier par réservations</option>
						</select>
					</div>
					<div className="flex gap-2">
						<Button variant="outline" icon={Download}>
							Exporter
						</Button>
						<Button variant="outline" icon={Filter}>
							Filtres avancés
						</Button>
					</div>
				</div>
			</div>

			{/* Clients Table */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
				<div className="overflow-x-auto">
					<table className="w-full">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Client
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Contact
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Type
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Réservations
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Total Dépensé
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Dernière Visite
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Statut
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredClients.length === 0 ? (
								<tr>
									<td colSpan={7} className="px-6 py-12 text-center">
										<p className="text-gray-500">Aucun client trouvé</p>
									</td>
								</tr>
							) : (
								filteredClients.map((client) => (
									<tr key={client.id} className="hover:bg-gray-50">
										<td className="px-6 py-4 whitespace-nowrap">
											<div className="flex items-center">
												<div className="h-10 w-10 bg-emerald-100 rounded-full flex items-center justify-center">
													<span className="text-emerald-600 font-medium">
														{getFullName(client)
															.split(" ")
															.map((n) => n[0])
															.join("")
															.toUpperCase()
															.slice(0, 2)}
													</span>
												</div>
												<div className="ml-4">
													<div className="text-sm font-medium text-gray-900">
														{getFullName(client)}
													</div>
													<div className="text-sm text-gray-500 flex items-center">
														<MapPin className="h-3 w-3 mr-1" />
														{[client.city, client.country].filter(Boolean).join(", ") ||
															"Non renseigné"}
													</div>
												</div>
											</div>
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<div className="text-sm text-gray-900">
												{client.email || "Non renseigné"}
											</div>
											<div className="text-sm text-gray-500">
												{client.phone || "Non renseigné"}
											</div>
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<span
												className={`px-2 py-1 text-xs font-medium rounded-full ${getClientTypeColor(
													client.total_spent || 0
												)}`}
											>
												{getClientType(client.total_spent || 0)}
											</span>
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
											{client.total_bookings || 0}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
											€{(client.total_spent || 0).toFixed(2)}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
											{client.last_booking_date
												? new Date(client.last_booking_date).toLocaleDateString("fr-FR")
												: "Jamais"}
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<span
												className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
													client.status || "inactive"
												)}`}
											>
												{getStatusText(client.status || "inactive")}
											</span>
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
											<div className="flex gap-2">
												<button
													onClick={() => handleViewDetails(client)}
													className="text-emerald-600 hover:text-emerald-900"
													title="Voir détails"
												>
													<Eye className="h-4 w-4" />
												</button>
												<button
													onClick={() => {
														if (typeof window !== "undefined") {
															window.open(`mailto:${client.email}`);
														}
													}}
													className="text-blue-600 hover:text-blue-900"
													title="Envoyer un email"
												>
													<Mail className="h-4 w-4" />
												</button>
												<button
													onClick={() => handleEditClient(client)}
													className="text-gray-600 hover:text-gray-900"
													title="Modifier"
												>
													<Edit className="h-4 w-4" />
												</button>
												<button
													onClick={() => handleDeleteClient(client)}
													className="text-red-600 hover:text-red-900"
													title="Supprimer"
												>
													<Trash2 className="h-4 w-4" />
												</button>
											</div>
										</td>
									</tr>
								))
							)}
						</tbody>
					</table>
				</div>
			</div>

			{/* Empty State */}
			{filteredClients.length === 0 && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
					<Users className="mx-auto h-12 w-12 text-gray-400" />
					<h3 className="mt-2 text-sm font-medium text-gray-900">Aucun client trouvé</h3>
					<p className="mt-1 text-sm text-gray-500">
						Aucun client ne correspond à vos critères de recherche.
					</p>
				</div>
			)}

			{/* Client Details Modal */}
			{selectedClient && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
						<div className="p-6 border-b border-gray-200">
							<div className="flex justify-between items-start">
								<div className="flex items-center gap-4">
									<div className="h-16 w-16 bg-emerald-100 rounded-full flex items-center justify-center">
										<span className="text-emerald-600 font-bold text-xl">
											{(
												(selectedClient.first_name || "") +
												" " +
												(selectedClient.last_name || "")
											)
												.trim()
												.split(" ")
												.map((n: string) => n[0])
												.join("")
												.toUpperCase() || "N/A"}
										</span>
									</div>
									<div>
										<h2 className="text-2xl font-bold text-gray-900">
											{(
												(selectedClient.first_name || "") +
												" " +
												(selectedClient.last_name || "")
											).trim() || "N/A"}
										</h2>
										<span
											className={`px-2 py-1 text-xs font-medium rounded-full ${getClientTypeColor(
												selectedClient.total_spent || 0
											)}`}
										>
											{getClientType(selectedClient.total_spent || 0)}
										</span>
									</div>
								</div>
								<button
									onClick={() => setSelectedClient(null)}
									className="text-gray-400 hover:text-gray-600"
								>
									<svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M6 18L18 6M6 6l12 12"
										/>
									</svg>
								</button>
							</div>
						</div>

						<div className="p-6">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
								<div>
									<h3 className="text-lg font-semibold text-gray-900 mb-4">
										Informations de contact
									</h3>
									<div className="space-y-3">
										<div className="flex items-center gap-3">
											<Mail className="h-5 w-5 text-gray-400" />
											<span className="text-gray-900">{selectedClient.email || "N/A"}</span>
										</div>
										<div className="flex items-center gap-3">
											<Phone className="h-5 w-5 text-gray-400" />
											<span className="text-gray-900">{selectedClient.phone || "N/A"}</span>
										</div>
										<div className="flex items-center gap-3">
											<MapPin className="h-5 w-5 text-gray-400" />
											<span className="text-gray-900">
												{[
													selectedClient.address,
													selectedClient.city,
													selectedClient.postal_code,
													selectedClient.country,
												]
													.filter(Boolean)
													.join(", ") || "N/A"}
											</span>
										</div>
									</div>
								</div>

								<div>
									<h3 className="text-lg font-semibold text-gray-900 mb-4">Statistiques</h3>
									<div className="grid grid-cols-2 gap-4">
										<div className="bg-gray-50 p-4 rounded-lg text-center">
											<div className="text-2xl font-bold text-gray-900">
												{selectedClient.total_bookings || 0}
											</div>
											<div className="text-sm text-gray-600">Réservations</div>
										</div>
										<div className="bg-gray-50 p-4 rounded-lg text-center">
											<div className="text-2xl font-bold text-emerald-600">
												€{selectedClient.total_spent || 0}
											</div>
											<div className="text-sm text-gray-600">Total dépensé</div>
										</div>
									</div>
								</div>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
								<div>
									<h3 className="text-lg font-semibold text-gray-900 mb-4">Historique</h3>
									<div className="space-y-3">
										<div className="flex justify-between">
											<span className="text-gray-600">Première visite:</span>
											<span className="text-gray-900">
												{selectedClient.created_at
													? new Date(selectedClient.created_at).toLocaleDateString("fr-FR")
													: "N/A"}
											</span>
										</div>
										<div className="flex justify-between">
											<span className="text-gray-600">Dernière visite:</span>
											<span className="text-gray-900">
												{selectedClient.last_booking_date
													? new Date(selectedClient.last_booking_date).toLocaleDateString(
															"fr-FR"
														)
													: "N/A"}
											</span>
										</div>
										<div className="flex justify-between">
											<span className="text-gray-600">Statut:</span>
											<span
												className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
													selectedClient.status || "inactive"
												)}`}
											>
												{getStatusText(selectedClient.status || "inactive")}
											</span>
										</div>
									</div>
								</div>

								<div>
									<h3 className="text-lg font-semibold text-gray-900 mb-4">
										Informations supplémentaires
									</h3>
									<div className="space-y-3">
										{selectedClient.dietary_restrictions &&
											selectedClient.dietary_restrictions.length > 0 && (
												<div>
													<span className="text-gray-600">Restrictions alimentaires:</span>
													<div className="mt-1">
														{selectedClient.dietary_restrictions.map(
															(restriction: string, index: number) => (
																<span
																	key={index}
																	className="inline-block bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full mr-1 mb-1"
																>
																	{restriction}
																</span>
															)
														)}
													</div>
												</div>
											)}
										{selectedClient.medical_conditions &&
											selectedClient.medical_conditions.length > 0 && (
												<div>
													<span className="text-gray-600">Conditions médicales:</span>
													<div className="mt-1">
														{selectedClient.medical_conditions.map(
															(condition: string, index: number) => (
																<span
																	key={index}
																	className="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full mr-1 mb-1"
																>
																	{condition}
																</span>
															)
														)}
													</div>
												</div>
											)}
										{selectedClient.emergency_contact_name && (
											<div className="flex justify-between">
												<span className="text-gray-600">Contact d'urgence:</span>
												<span className="text-gray-900">
													{selectedClient.emergency_contact_name}
													{selectedClient.emergency_contact_phone &&
														` (${selectedClient.emergency_contact_phone})`}
												</span>
											</div>
										)}
									</div>
								</div>
							</div>

							<div className="flex gap-3 pt-4 border-t border-gray-200">
								<Button icon={Mail}>Envoyer un email</Button>
								<Button variant="outline" icon={Phone}>
									Appeler
								</Button>
								<Button variant="outline" icon={Calendar}>
									Nouvelle réservation
								</Button>
							</div>
						</div>
					</div>
				</div>
			)}

			{/* Edit Client Modal */}
			{editingClient && (
				<EditClientModal
					client={editingClient}
					isOpen={!!editingClient}
					onClose={() => setEditingClient(null)}
					onSave={handleUpdateClient}
					isLoading={isUpdating}
				/>
			)}

			{/* Delete Confirmation Modal */}
			{deleteConfirmClient && (
				<DeleteConfirmModal
					client={deleteConfirmClient}
					isOpen={!!deleteConfirmClient}
					onClose={() => setDeleteConfirmClient(null)}
					onConfirm={handleConfirmDelete}
					isLoading={isDeleting}
				/>
			)}
		</div>
	);
};

export default AdminClients;

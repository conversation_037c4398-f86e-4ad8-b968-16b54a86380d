import { J<PERSON> } from "./common";

export type AdminAuditLog = {
	Row: {
		id: string;
		admin_user_id: string | null;
		action: string;
		table_name: string | null;
		record_id: string | null;
		old_values: Json | null;
		new_values: Json | null;
		ip_address: string | null;
		user_agent: string | null;
		session_id: string | null;
		created_at: string | null;
	};
	Insert: {
		id?: string;
		admin_user_id?: string | null;
		action: string;
		table_name?: string | null;
		record_id?: string | null;
		old_values?: Json | null;
		new_values?: Json | null;
		ip_address?: string | null;
		user_agent?: string | null;
		session_id?: string | null;
		created_at?: string | null;
	};
	Update: {
		id?: string;
		admin_user_id?: string | null;
		action?: string;
		table_name?: string | null;
		record_id?: string | null;
		old_values?: Json | null;
		new_values?: Json | null;
		ip_address?: string | null;
		user_agent?: string | null;
		session_id?: string | null;
		created_at?: string | null;
	};
};

export type BusinessSettings = {
	Row: {
		id: string;
		key: string;
		value: string;
		value_type: string;
		category: string;
		description: string | null;
		is_public: boolean | null;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: {
		id?: string;
		key: string;
		value: string;
		value_type?: string;
		category?: string;
		description?: string | null;
		is_public?: boolean | null;
		created_at?: string | null;
		updated_at?: string | null;
	};
	Update: {
		id?: string;
		key?: string;
		value?: string;
		value_type?: string;
		category?: string;
		description?: string | null;
		is_public?: boolean | null;
		created_at?: string | null;
		updated_at?: string | null;
	};
};

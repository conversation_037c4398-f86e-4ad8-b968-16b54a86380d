"use client";

import { Plus, Trash2, User } from "lucide-react";
import { useState } from "react";
import Button from "./ui/Button";

// Mock data for employees and services
const employees = [
	{ id: "1", name: "<PERSON>", role: "Guide principal" },
	{ id: "2", name: "<PERSON>", role: "Guide aquatique" },
	{ id: "3", name: "<PERSON><PERSON><PERSON>", role: "Capitaine" },
];

const services = [
	{ id: "mangrove-tour", name: "Découverte de la Mangrove" },
	{ id: "catamaran-sunset", name: "Coucher de Soleil en Catamaran" },
	{ id: "waterbike-adventure", name: "Aventure en Waterbike" },
];

const AdminSchedule = () => {
	const [selectedDate, setSelectedDate] = useState(new Date());
	const [viewMode, setViewMode] = useState<"week" | "month">("week");
	const [assignments, setAssignments] = useState([
		{
			id: "1",
			employeeId: "1",
			serviceId: "mangrove-tour",
			date: "2025-02-01",
			time: "09:00",
			duration: 180,
			status: "confirmed",
		},
		{
			id: "2",
			employeeId: "2",
			serviceId: "catamaran-sunset",
			date: "2025-02-01",
			time: "17:30",
			duration: 240,
			status: "confirmed",
		},
		{
			id: "3",
			employeeId: "3",
			serviceId: "waterbike-adventure",
			date: "2025-02-02",
			time: "10:00",
			duration: 120,
			status: "pending",
		},
	]);

	const [isCreatingAssignment, setIsCreatingAssignment] = useState(false);
	const [newAssignment, setNewAssignment] = useState({
		employeeId: "",
		serviceId: "",
		date: "",
		time: "",
		duration: 120,
	});

	const getWeekDays = (date: Date) => {
		const week = [];
		const startDate = new Date(date);
		const day = startDate.getDay();
		const diff = startDate.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
		startDate.setDate(diff);

		for (let i = 0; i < 7; i++) {
			const currentDate = new Date(startDate);
			currentDate.setDate(startDate.getDate() + i);
			week.push(currentDate);
		}
		return week;
	};

	const weekDays = getWeekDays(selectedDate);
	const timeSlots = Array.from({ length: 12 }, (_, i) => {
		const hour = 8 + i;
		return `${hour.toString().padStart(2, "0")}:00`;
	});

	const getAssignmentsForDateAndTime = (date: Date, time: string) => {
		const dateStr = date.toISOString().split("T")[0];
		return assignments.filter((a) => a.date === dateStr && a.time === time);
	};

	const getEmployeeName = (employeeId: string) => {
		const employee = employees.find((e) => e.id === employeeId);
		return employee ? employee.name : "Employé inconnu";
	};

	const getServiceName = (serviceId: string) => {
		const service = services.find((s) => s.id === serviceId);
		return service ? service.name : "Service inconnu";
	};

	const handleCreateAssignment = () => {
		const assignment = {
			id: Date.now().toString(),
			...newAssignment,
			status: "confirmed" as const,
		};
		setAssignments([...assignments, assignment]);
		setIsCreatingAssignment(false);
		setNewAssignment({
			employeeId: "",
			serviceId: "",
			date: "",
			time: "",
			duration: 120,
		});
		console.log("Assignment created:", assignment);
	};

	const handleDeleteAssignment = (assignmentId: string) => {
		if (window.confirm("Êtes-vous sûr de vouloir supprimer cette affectation ?")) {
			setAssignments(assignments.filter((a) => a.id !== assignmentId));
			console.log("Assignment deleted:", assignmentId);
		}
	};

	const hasConflict = (date: Date, time: string, employeeId: string) => {
		const dateStr = date.toISOString().split("T")[0];
		return assignments.some((a) => a.date === dateStr && a.time === time && a.employeeId === employeeId);
	};

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Planning des Employés</h1>
					<p className="text-gray-600">Gérez les affectations de vos guides et employés</p>
				</div>
				<div className="flex gap-4">
					<div className="flex bg-gray-100 rounded-lg p-1">
						<button
							onClick={() => setViewMode("week")}
							className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
								viewMode === "week"
									? "bg-white text-gray-900 shadow-sm"
									: "text-gray-600 hover:text-gray-900"
							}`}
						>
							Semaine
						</button>
						<button
							onClick={() => setViewMode("month")}
							className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
								viewMode === "month"
									? "bg-white text-gray-900 shadow-sm"
									: "text-gray-600 hover:text-gray-900"
							}`}
						>
							Mois
						</button>
					</div>
					<Button onClick={() => setIsCreatingAssignment(true)} icon={Plus}>
						Nouvelle Affectation
					</Button>
				</div>
			</div>

			{/* Date Navigation */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
				<div className="flex items-center justify-between">
					<button
						onClick={() => {
							const newDate = new Date(selectedDate);
							newDate.setDate(selectedDate.getDate() - 7);
							setSelectedDate(newDate);
						}}
						className="p-2 hover:bg-gray-100 rounded-lg"
					>
						←
					</button>
					<h2 className="text-xl font-bold text-gray-900">
						Semaine du {weekDays[0].toLocaleDateString("fr-FR")} au{" "}
						{weekDays[6].toLocaleDateString("fr-FR")}
					</h2>
					<button
						onClick={() => {
							const newDate = new Date(selectedDate);
							newDate.setDate(selectedDate.getDate() + 7);
							setSelectedDate(newDate);
						}}
						className="p-2 hover:bg-gray-100 rounded-lg"
					>
						→
					</button>
				</div>
			</div>

			{/* Weekly Schedule */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
				<div className="overflow-x-auto">
					<table className="w-full">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
									Heure
								</th>
								{weekDays.map((day, index) => (
									<th
										key={index}
										className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										<div>{day.toLocaleDateString("fr-FR", { weekday: "short" })}</div>
										<div className="text-lg font-bold text-gray-900 mt-1">{day.getDate()}</div>
									</th>
								))}
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{timeSlots.map((time) => (
								<tr key={time}>
									<td className="px-4 py-4 text-sm font-medium text-gray-900 bg-gray-50">{time}</td>
									{weekDays.map((day, dayIndex) => {
										const dayAssignments = getAssignmentsForDateAndTime(day, time);
										return (
											<td
												key={dayIndex}
												className="px-2 py-2 border-l border-gray-200 relative h-20"
											>
												<div className="space-y-1">
													{dayAssignments.map((assignment) => {
														const employee = employees.find(
															(e) => e.id === assignment.employeeId
														);
														const service = services.find(
															(s) => s.id === assignment.serviceId
														);
														return (
															<div
																key={assignment.id}
																className={`p-2 rounded text-xs ${
																	assignment.status === "confirmed"
																		? "bg-emerald-100 text-emerald-800 border border-emerald-200"
																		: "bg-yellow-100 text-yellow-800 border border-yellow-200"
																}`}
															>
																<div className="font-medium truncate">
																	{service?.name}
																</div>
																<div className="flex items-center gap-1 mt-1">
																	<User className="h-3 w-3" />
																	<span className="truncate">{employee?.name}</span>
																</div>
																<div className="flex justify-end mt-1">
																	<button
																		onClick={() =>
																			handleDeleteAssignment(assignment.id)
																		}
																		className="text-red-600 hover:text-red-800"
																	>
																		<Trash2 className="h-3 w-3" />
																	</button>
																</div>
															</div>
														);
													})}
												</div>
											</td>
										);
									})}
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>

			{/* Create Assignment Modal */}
			{isCreatingAssignment && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-xl max-w-md w-full">
						<div className="p-6 border-b border-gray-200">
							<h2 className="text-xl font-bold text-gray-900">Nouvelle Affectation</h2>
						</div>

						<div className="p-6 space-y-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Employé *</label>
								<select
									value={newAssignment.employeeId}
									onChange={(e) =>
										setNewAssignment((prev) => ({ ...prev, employeeId: e.target.value }))
									}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								>
									<option value="">Sélectionner un employé</option>
									{employees.map((employee) => (
										<option key={employee.id} value={employee.id}>
											{employee.name} - {employee.role}
										</option>
									))}
								</select>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Service *</label>
								<select
									value={newAssignment.serviceId}
									onChange={(e) =>
										setNewAssignment((prev) => ({ ...prev, serviceId: e.target.value }))
									}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								>
									<option value="">Sélectionner un service</option>
									{services.map((service) => (
										<option key={service.id} value={service.id}>
											{service.name}
										</option>
									))}
								</select>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">Date *</label>
									<input
										type="date"
										value={newAssignment.date}
										onChange={(e) =>
											setNewAssignment((prev) => ({ ...prev, date: e.target.value }))
										}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">Heure *</label>
									<select
										value={newAssignment.time}
										onChange={(e) =>
											setNewAssignment((prev) => ({ ...prev, time: e.target.value }))
										}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									>
										<option value="">Sélectionner une heure</option>
										{timeSlots.map((time) => (
											<option key={time} value={time}>
												{time}
											</option>
										))}
									</select>
								</div>
							</div>

							{newAssignment.employeeId &&
								newAssignment.date &&
								newAssignment.time &&
								hasConflict(
									new Date(newAssignment.date),
									newAssignment.time,
									newAssignment.employeeId
								) && (
									<div className="bg-red-50 border border-red-200 rounded-lg p-3">
										<p className="text-sm text-red-800">
											⚠️ Conflit détecté : Cet employé a déjà une affectation à cette heure.
										</p>
									</div>
								)}
						</div>

						<div className="p-6 border-t border-gray-200 flex justify-end gap-4">
							<Button variant="outline" onClick={() => setIsCreatingAssignment(false)}>
								Annuler
							</Button>
							<Button
								onClick={handleCreateAssignment}
								disabled={
									!newAssignment.employeeId ||
									!newAssignment.serviceId ||
									!newAssignment.date ||
									!newAssignment.time
								}
							>
								Créer l'affectation
							</Button>
						</div>
					</div>
				</div>
			)}

			{/* Legend */}
			<div className="mt-8 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Légende</h3>
				<div className="flex flex-wrap gap-6">
					<div className="flex items-center gap-2">
						<div className="w-4 h-4 bg-emerald-100 border border-emerald-200 rounded"></div>
						<span className="text-sm text-gray-700">Affectation confirmée</span>
					</div>
					<div className="flex items-center gap-2">
						<div className="w-4 h-4 bg-yellow-100 border border-yellow-200 rounded"></div>
						<span className="text-sm text-gray-700">Affectation en attente</span>
					</div>
					<div className="flex items-center gap-2">
						<div className="w-4 h-4 bg-red-100 border border-red-200 rounded"></div>
						<span className="text-sm text-gray-700">Conflit d'horaire</span>
					</div>
				</div>
			</div>
		</div>
	);
};

export default AdminSchedule;

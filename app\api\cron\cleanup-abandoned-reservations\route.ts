import { NextRequest, NextResponse } from "next/server";
import { supabaseAdmin } from "@/lib/supabase";

/**
 * Cron job to clean up abandoned reservations
 * This endpoint is called by Vercel Cron every 15 minutes
 *
 * For reservations older than 15 minutes:
 * - If pending: cancels them and deletes equipment reservations
 * - If cancelled: deletes equipment reservations
 */
export async function GET(request: NextRequest) {
	try {
		console.log("=== ABANDONED RESERVATIONS CLEANUP CRON JOB STARTED ===");

		if (!supabaseAdmin) {
			console.error("Supabase admin client not available");
			return NextResponse.json({ error: "Database connection error" }, { status: 500 });
		}

		// Verify this is a legitimate cron request
		const authHeader = request.headers.get("authorization");
		const cronSecret = process.env.CRON_SECRET;

		if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
			console.log("Unauthorized cron request");
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Calculate cutoff time (15 minutes ago)
		const cutoffTime = new Date(Date.now() - 15 * 60 * 1000);

		console.log("Looking for reservations older than:", cutoffTime.toISOString());

		// Find pending and cancelled reservations older than 15 minutes
		const { data: abandonedReservations, error: fetchError } = await supabaseAdmin
			.from("reservations")
			.select("id, reservation_number, created_at, customer_id, status")
			.in("status", ["pending", "cancelled"])
			.lt("created_at", cutoffTime.toISOString());

		if (fetchError) {
			console.error("Error fetching abandoned reservations:", fetchError);
			return NextResponse.json({ error: "Database error" }, { status: 500 });
		}

		if (!abandonedReservations || abandonedReservations.length === 0) {
			console.log("No abandoned reservations found");
			return NextResponse.json({
				success: true,
				message: "No abandoned reservations to clean up",
				cleaned: 0,
			});
		}

		console.log(`Found ${abandonedReservations.length} abandoned reservations to clean up`);

		const reservationIds = abandonedReservations.map((r) => r.id);
		const pendingReservations = abandonedReservations.filter((r) => r.status === "pending");
		const cancelledReservations = abandonedReservations.filter((r) => r.status === "cancelled");

		let successCount = 0;
		let errorCount = 0;
		const errors: string[] = [];

		// Cancel pending reservations
		if (pendingReservations.length > 0) {
			const pendingIds = pendingReservations.map((r) => r.id);
			console.log(`Cancelling ${pendingIds.length} pending reservations`);

			const { error: updateError } = await supabaseAdmin
				.from("reservations")
				.update({
					status: "cancelled",
					admin_notes: "Automatically cancelled - payment abandoned after 15 minutes",
					updated_at: new Date().toISOString(),
				})
				.in("id", pendingIds);

			if (updateError) {
				console.error("Error cancelling pending reservations:", updateError);
				return NextResponse.json({ error: "Failed to cancel reservations" }, { status: 500 });
			}
		}

		successCount = reservationIds.length;

		// Also cancel any pending payments for these reservations
		const { error: paymentsError } = await supabaseAdmin
			.from("payments")
			.update({
				status: "failed",
				failure_reason: "Reservation automatically failed after 30 minutes",
				updated_at: new Date().toISOString(),
			})
			.in("reservation_id", reservationIds)
			.in("status", ["pending", "processing"]);

		if (paymentsError) {
			console.error("Error updating payment status:", paymentsError);
			// Don't fail the request, as the reservations were already cancelled
		}

		// Delete equipment reservations for all abandoned reservations (both pending and cancelled)
		console.log(`Deleting equipment reservations for ${reservationIds.length} reservations`);
		const { error: equipmentError } = await supabaseAdmin
			.from("equipment_reservations")
			.delete()
			.in("reservation_id", reservationIds);

		if (equipmentError) {
			console.error("Error deleting equipment reservations:", equipmentError);
			// Don't fail the request, as the main reservations were already processed
		} else {
			console.log(`✅ Updated equipment reservations status to cancelled`);
		}

		console.log("=== ABANDONED RESERVATIONS CLEANUP COMPLETED ===");
		console.log(
			`Processed: ${successCount} reservations (${pendingReservations.length} pending cancelled, ${cancelledReservations.length} already cancelled)`
		);

		return NextResponse.json({
			success: true,
			message: "Abandoned reservations cleanup completed",
			processed: successCount,
			pending_cancelled: pendingReservations.length,
			already_cancelled: cancelledReservations.length,
			equipment_updated: reservationIds.length,
			reservations: abandonedReservations.map((r) => ({
				id: r.id,
				reservation_number: r.reservation_number,
				created_at: r.created_at,
				status: r.status,
			})),
		});
	} catch (error) {
		console.error("Cleanup cron job error:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}

/**
 * Manual trigger for testing (POST request)
 */
export async function POST(_request: NextRequest) {
	try {
		console.log("=== MANUAL ABANDONED RESERVATIONS CLEANUP TRIGGER ===");

		// For manual testing, use a shorter cutoff (5 minutes)
		const cutoffTime = new Date(Date.now() - 5 * 60 * 1000);

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection error" }, { status: 500 });
		}

		const { data: abandonedReservations, error } = await supabaseAdmin
			.from("reservations")
			.select("id, reservation_number, created_at")
			.eq("status", "pending")
			.lt("created_at", cutoffTime.toISOString());

		if (error) {
			return NextResponse.json({ error: "Database error" }, { status: 500 });
		}

		return NextResponse.json({
			success: true,
			message: "Manual cleanup trigger completed",
			found: abandonedReservations?.length || 0,
			reservations: abandonedReservations || [],
		});
	} catch (error) {
		console.error("Manual cleanup trigger error:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}

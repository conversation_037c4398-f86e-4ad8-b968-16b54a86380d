{"name": "soleil-et-decouverte", "version": "1.0.0", "private": true, "description": "Site web officiel de Soleil et Découverte - Excursions éco-responsables en Guadeloupe", "scripts": {"dev": "next dev", "build": "next build && next-sitemap", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "create-admin": "node scripts/create-admin.js", "postbuild": "next-sitemap", "sitemap": "next-sitemap", "generate-sitemap": "node scripts/generate-sitemap.js"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.5.0", "@supabase/supabase-js": "^2.51.0", "@types/qrcode": "^1.5.5", "@vercel/analytics": "^1.5.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "latest", "html2canvas": "^1.4.1", "input-otp": "1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.454.0", "next": "14.2.16", "next-sitemap": "^4.2.3", "next-themes": "^0.4.4", "qrcode": "^1.5.4", "react": "^18", "react-datepicker": "^8.4.0", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "resend": "^4.7.0", "sonner": "^1.7.1", "stripe": "^18.3.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}
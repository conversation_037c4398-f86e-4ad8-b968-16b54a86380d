import { Json } from "./common";

// =====================================================
// Core Option Types
// =====================================================

export type ServiceOptionSelectionType =
	| "optional" // No selections required, multiple allowed
	| "single_required" // Exactly one selection required
	| "single_optional" // Zero or one selection allowed
	| "multiple_required" // At least min_selections required
	| "multiple_optional"; // Zero to max_selections allowed

// =====================================================
// Database Table Types
// =====================================================

export type ServiceOptions = {
	Row: {
		id: string;
		name: string;
		description: string | null;
		base_price: number;
		quote_based: boolean;
		per_participant: boolean;
		is_active: boolean;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<ServiceOptions["Row"]> & {
		name: string;
		base_price: number;
	};
	Update: Partial<ServiceOptions["Row"]>;
};

export type ServiceOptionGroups = {
	Row: {
		id: string;
		name: string;
		description: string | null;
		selection_type: ServiceOptionSelectionType;
		min_selections: number;
		max_selections: number | null;
		is_active: boolean;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<ServiceOptionGroups["Row"]> & {
		name: string;
		selection_type: ServiceOptionSelectionType;
	};
	Update: Partial<ServiceOptionGroups["Row"]>;
};

export type ServiceOptionGroupItems = {
	Row: {
		id: string;
		group_id: string;
		option_id: string;
		sort_order: number;
		is_active: boolean;
		created_at: string | null;
	};
	Insert: Partial<ServiceOptionGroupItems["Row"]> & {
		group_id: string;
		option_id: string;
	};
	Update: Partial<ServiceOptionGroupItems["Row"]>;
};

export type ServiceOptionAssignments = {
	Row: {
		id: string;
		service_id: string;
		group_id: string | null;
		option_id: string | null;
		custom_price: number | null;
		is_required: boolean;
		sort_order: number;
		is_active: boolean;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<ServiceOptionAssignments["Row"]> & {
		service_id: string;
	};
	Update: Partial<ServiceOptionAssignments["Row"]>;
};

export type ReservationOptionSelections = {
	Row: {
		id: string;
		reservation_id: string;
		option_id: string;
		group_id: string | null;
		quantity: number;
		unit_price: number;
		total_price: number;
		created_at: string | null;
	};
	Insert: Partial<ReservationOptionSelections["Row"]> & {
		reservation_id: string;
		option_id: string;
		unit_price: number;
		total_price: number;
	};
	Update: Partial<ReservationOptionSelections["Row"]>;
};

// =====================================================
// Enhanced Frontend Types
// =====================================================

export interface ServiceOption {
	id: string;
	name: string;
	description?: string;
	basePrice: number;
	customPrice?: number; // Service-specific override
	quoteBased: boolean;
	perParticipant: boolean;
	isActive: boolean;
}

export interface ServiceOptionAssignment {
	id: string;
	type: "individual";
	option: ServiceOption;
	isRequired: boolean;
	sortOrder: number;
	requiredGroupId?: string; // For simple required group logic
}

// =====================================================
// Selection and Validation Types
// =====================================================

export interface OptionSelection {
	optionId: string;
	quantity: number;
}

export interface OptionSelectionValidation {
	isValid: boolean;
	errors: string[];
	warnings: string[];
	requiredGroupValidations?: RequiredGroupValidation[];
}

export interface RequiredOptionGroup {
	id: string;
	name: string;
	description?: string;
	minRequired: number; // Minimum options that must be selected from this group
	maxRequired?: number; // Maximum options that can be selected from this group (optional)
	optionIds: string[]; // IDs of options that belong to this group
}

export interface RequiredGroupValidation {
	groupId: string;
	groupName: string;
	isValid: boolean;
	selectedCount: number;
	requiredMin: number;
	errors: string[];
}

// =====================================================
// Pricing Types
// =====================================================

export interface OptionPricing {
	optionId: string;
	optionName: string;
	basePrice: number;
	finalPrice: number; // After service overrides
	quantity: number;
	totalPrice: number;
	perParticipant: boolean;
	quoteBased: boolean;
}

export interface OptionsPricingSummary {
	total: number;
	itemizedPricing: Array<{
		optionId: string;
		optionName: string;
		quantity: number;
		unitPrice: number;
		totalPrice: number;
		perParticipant: boolean;
	}>;
	participantCount: number;
}

// =====================================================
// API Response Types
// =====================================================

export interface ServiceWithOptions {
	id: string;
	name: string;
	// ... other service fields
	optionAssignments: ServiceOptionAssignment[];
}

export interface ReservationWithOptions {
	id: string;
	// ... other reservation fields
	selectedOptions: OptionSelection[];
	optionSelections: {
		id: string;
		option: ServiceOption;
		quantity: number;
		unitPrice: number;
		totalPrice: number;
	}[];
}

// =====================================================
// Admin Interface Types
// =====================================================

export interface OptionFormData {
	name: string;
	description?: string;
	basePrice: number;
	quoteBased: boolean;
	perParticipant: boolean;
}

export interface ServiceOptionConfig {
	options: ServiceOption[];
	assignments: ServiceOptionAssignment[];
}

// =====================================================
// Utility Types
// =====================================================

export type OptionValidationRule = {
	type: "required" | "min_selections" | "max_selections" | "mutually_exclusive";
	groupId?: string;
	value?: number;
	message: string;
};

export type OptionDependencyRule = {
	triggerOptionId: string;
	dependentOptionId: string;
	type: "requires" | "excludes";
};

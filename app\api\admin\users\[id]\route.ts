import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type Profile = Database["public"]["Tables"]["profiles"]["Row"];
type ProfileUpdate = Database["public"]["Tables"]["profiles"]["Update"];

// GET /api/admin/users/[id] - Get single user with full details
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const userId = params.id;

		const { data: userProfile, error } = await supabaseAdmin.from("profiles").select("*").eq("id", userId).single();

		if (error) {
			if (error.code === "PGRST116") {
				return NextResponse.json({ error: "User not found" }, { status: 404 });
			}
			console.error("Error fetching user:", error);
			return NextResponse.json({ error: "Failed to fetch user" }, { status: 500 });
		}

		return NextResponse.json({ user: userProfile });
	} catch (error) {
		console.error("User GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "users:read");

// PUT /api/admin/users/[id] - Update single user
export const PUT = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const userId = params.id;
		const updates: ProfileUpdate = await request.json();

		// Get current user for audit log
		const { data: currentUser } = await supabaseAdmin.from("profiles").select("*").eq("id", userId).single();

		if (!currentUser) {
			return NextResponse.json({ error: "User not found" }, { status: 404 });
		}

		// Update user
		const { data: updatedUser, error } = await supabaseAdmin
			.from("profiles")
			.update(updates)
			.eq("id", userId)
			.select()
			.single();

		if (error) {
			console.error("Error updating user:", error);
			return NextResponse.json({ error: "Failed to update user" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(user.id, "UPDATE", "profiles", userId, currentUser, updatedUser, request);

		return NextResponse.json({ user: updatedUser });
	} catch (error) {
		console.error("User PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "users:write");

// DELETE /api/admin/users/[id] - Delete/deactivate user
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const userId = params.id;

		// Get user for audit log before deletion
		const { data: userToDelete } = await supabaseAdmin.from("profiles").select("*").eq("id", userId).single();

		if (!userToDelete) {
			return NextResponse.json({ error: "User not found" }, { status: 404 });
		}

		// Instead of hard delete, we could deactivate or anonymize
		// For now, let's do a soft delete by updating the record
		const anonymizedData = {
			email: `deleted-${userId}@anonymized.local`,
			first_name: "Deleted",
			last_name: "User",
			phone: null,
			role: "employee", // Set to lowest privilege
		};

		const { error } = await supabaseAdmin.from("profiles").update(anonymizedData).eq("id", userId);

		if (error) {
			console.error("Error deleting user:", error);
			return NextResponse.json({ error: "Failed to delete user" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(user.id, "DELETE", "profiles", userId, userToDelete, anonymizedData, request);

		return NextResponse.json({
			message: "User deleted successfully",
			deletedUserId: userId,
		});
	} catch (error) {
		console.error("User DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "users:write");

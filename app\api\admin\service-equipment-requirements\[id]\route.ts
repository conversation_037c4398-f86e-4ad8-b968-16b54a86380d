import { withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

// PUT /api/admin/service-equipment-requirements/[id] - Update service equipment requirement
export const PUT = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const requirementId = params.id;
		const updates = await request.json();

		// Get current requirement for audit log
		const { data: currentRequirement } = await supabaseAdmin
			.from("service_equipment_requirements")
			.select("*")
			.eq("id", requirementId)
			.single();

		if (!currentRequirement) {
			return NextResponse.json({ error: "Service equipment requirement not found" }, { status: 404 });
		}

		// Update the requirement
		const { data: updatedRequirement, error } = await supabaseAdmin
			.from("service_equipment_requirements")
			.update(updates)
			.eq("id", requirementId)
			.select(
				`
				*,
				service:services(id, name),
				equipment:equipment(id, name, total_capacity)
			`
			)
			.single();

		if (error) {
			console.error("Error updating service equipment requirement:", error);
			return NextResponse.json({ error: "Failed to update requirement" }, { status: 500 });
		}

		return NextResponse.json({
			message: "Service equipment requirement updated successfully",
			requirement: updatedRequirement,
		});
	} catch (error) {
		console.error("Service equipment requirement PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// DELETE /api/admin/service-equipment-requirements/[id] - Delete service equipment requirement
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const requirementId = params.id;

		// Get requirement for audit log
		const { data: requirement } = await supabaseAdmin
			.from("service_equipment_requirements")
			.select(
				`
				*,
				service:services(id, name),
				equipment:equipment(id, name)
			`
			)
			.eq("id", requirementId)
			.single();

		if (!requirement) {
			return NextResponse.json({ error: "Service equipment requirement not found" }, { status: 404 });
		}

		// Delete the requirement
		const { error } = await supabaseAdmin.from("service_equipment_requirements").delete().eq("id", requirementId);

		if (error) {
			console.error("Error deleting service equipment requirement:", error);
			return NextResponse.json({ error: "Failed to delete requirement" }, { status: 500 });
		}

		return NextResponse.json({
			message: "Service equipment requirement deleted successfully",
			requirementId,
		});
	} catch (error) {
		console.error("Service equipment requirement DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

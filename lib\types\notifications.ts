export type Notifications = {
	Row: {
		id: string;
		recipient_id: string;
		reservation_id: string | null;
		notification_type: string;
		subject: string;
		content: string;
		status: string;
		sent_at: string | null;
		error_message: string | null;
		created_at: string | null;
		priority: string | null;
		read_at: string | null;
		is_read: boolean;
	};
	Insert: Partial<Notifications["Row"]> & {
		recipient_id: string;
		notification_type: string;
		subject: string;
		content: string;
	};
	Update: Partial<Notifications["Row"]>;
};

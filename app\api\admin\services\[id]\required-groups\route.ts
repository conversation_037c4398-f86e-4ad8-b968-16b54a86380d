import { NextRequest, NextResponse } from "next/server";
import { withAdminAuth } from "@/lib/admin-auth";
import { getSupabaseAdmin } from "@/lib/supabase";
import { RequiredOptionGroup } from "@/lib/types/service-options";
import { extractIdFromSlug, generateSlug } from "@/lib/utils/slug";

// Helper function to resolve service ID from slug or UUID
async function resolveServiceId(slug: string): Promise<string | null> {
	const supabaseAdmin = getSupabaseAdmin();

	// Try to extract UUID from slug first (backward compatibility)
	let serviceId = extractIdFromSlug(slug);

	if (!serviceId) {
		// If no UUID found, treat as name slug and search by normalized name
		const { data: services, error: servicesError } = await supabaseAdmin
			.from("services")
			.select("id, name")
			.eq("is_active", true);

		if (servicesError) {
			console.error("Error fetching services for slug lookup:", servicesError);
			return null;
		}

		// Find service by comparing normalized names
		const targetSlug = slug;
		const matchingService = services?.find((service: any) => {
			const serviceSlug = generateSlug(service.name);
			return serviceSlug === targetSlug;
		});

		if (!matchingService) {
			return null;
		}

		serviceId = matchingService.id;
	}

	return serviceId;
}

// GET /api/admin/services/[id]/required-groups - Get service required groups
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const supabaseAdmin = getSupabaseAdmin();
		const serviceId = await resolveServiceId(params.id);

		if (!serviceId) {
			return NextResponse.json({ error: "Service not found" }, { status: 404 });
		}

		// Query required groups from service metadata
		// For now, we'll store them in the services table as JSONB
		const { data: service, error } = await supabaseAdmin
			.from("services")
			.select("required_option_groups")
			.eq("id", serviceId)
			.single();

		if (error) {
			console.error("Error fetching service required groups:", error);
			return NextResponse.json({ error: "Failed to fetch required groups" }, { status: 500 });
		}

		const requiredGroups: RequiredOptionGroup[] = service?.required_option_groups || [];

		return NextResponse.json({ requiredGroups });
	} catch (error) {
		console.error("Service required groups GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:read");

// PUT /api/admin/services/[id]/required-groups - Update service required groups
export const PUT = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const serviceId = params.id;
		const body = await request.json();
		const { requiredGroups }: { requiredGroups: RequiredOptionGroup[] } = body;

		const supabaseAdmin = getSupabaseAdmin();

		// Update the service with the required groups
		const { error } = await supabaseAdmin
			.from("services")
			.update({
				required_option_groups: requiredGroups,
				updated_at: new Date().toISOString(),
			})
			.eq("id", serviceId);

		if (error) {
			console.error("Error updating service required groups:", error);
			return NextResponse.json({ error: "Failed to update required groups" }, { status: 500 });
		}

		return NextResponse.json({ message: "Required groups updated successfully" });
	} catch (error) {
		console.error("Service required groups PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// DELETE /api/admin/services/[id]/required-groups - Delete all service required groups
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const serviceId = params.id;

		const supabaseAdmin = getSupabaseAdmin();

		// Clear the required groups
		const { error } = await supabaseAdmin
			.from("services")
			.update({
				required_option_groups: null,
				updated_at: new Date().toISOString(),
			})
			.eq("id", serviceId);

		if (error) {
			console.error("Error deleting service required groups:", error);
			return NextResponse.json({ error: "Failed to delete required groups" }, { status: 500 });
		}

		return NextResponse.json({ message: "Required groups deleted successfully" });
	} catch (error) {
		console.error("Service required groups DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

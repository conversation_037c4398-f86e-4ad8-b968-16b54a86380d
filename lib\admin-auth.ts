import { NextRequest } from "next/server";
import { supabaseAdmin } from "./supabase";

export interface AdminUser {
	id: string;
	email: string;
	role: string;
}

export interface AdminAuthResult {
	success: boolean;
	user?: AdminUser;
	error?: string;
}

/**
 * Verify admin authentication and authorization
 */
export async function verifyAdminAuth(request: NextRequest): Promise<AdminAuthResult> {
	try {
		console.log("=== ADMIN AUTH DEBUG ===");
		console.log("Environment:", process.env.NODE_ENV);
		console.log("Has SUPABASE_SERVICE_ROLE_KEY:", !!process.env.SUPABASE_SERVICE_ROLE_KEY);
		console.log("SupabaseAdmin available:", !!supabaseAdmin);

		// Get authorization header
		const authHeader = request.headers.get("authorization");
		console.log("Auth header present:", !!authHeader);
		console.log("Auth header format:", authHeader ? authHeader.substring(0, 20) + "..." : "none");

		if (!authHeader || !authHeader.startsWith("Bearer ")) {
			console.log("Missing or invalid authorization header");
			return { success: false, error: "Missing or invalid authorization header" };
		}

		const token = authHeader.substring(7);
		console.log("Token length:", token.length);

		if (supabaseAdmin === null) {
			console.log("Database connection not available - supabaseAdmin is null");
			return { success: false, error: "Database connection not available" };
		}

		// Verify JWT token with Supabase
		const {
			data: { user },
			error: authError,
		} = await supabaseAdmin.auth.getUser(token);

		if (authError || !user) {
			return { success: false, error: "Invalid or expired token" };
		}

		// Check if user has admin, manager, or employee role from user metadata or profiles table
		let role = user.user_metadata?.role;

		// If role is not in user metadata, check the profiles table
		if (!role || !["admin", "manager", "employee"].includes(role)) {
			const { data: profile } = await supabaseAdmin.from("profiles").select("role").eq("id", user.id).single();

			role = profile?.role;
		}

		if (!["admin", "manager", "employee"].includes(role)) {
			return { success: false, error: "Insufficient permissions" };
		}

		return {
			success: true,
			user: {
				id: user.id,
				email: user.email || "",
				role: role,
			},
		};
	} catch (error) {
		console.error("Admin auth verification error:", error);
		return { success: false, error: "Authentication verification failed" };
	}
}

/**
 * Middleware wrapper for admin routes
 */
export function withAdminAuth(
	handler: (request: NextRequest, user: AdminUser, context?: any) => Promise<Response>,
	requiredPermission?: string
) {
	return async (request: NextRequest, context?: any) => {
		const authResult = await verifyAdminAuth(request);

		if (!authResult.success || !authResult.user) {
			return new Response(JSON.stringify({ error: authResult.error || "Authentication failed" }), {
				status: 401,
				headers: { "Content-Type": "application/json" },
			});
		}

		// Check specific permission if required
		if (requiredPermission) {
			const { hasPermission } = await import("./permissions");
			if (!hasPermission(authResult.user.role as any, requiredPermission as any)) {
				return new Response(JSON.stringify({ error: "Insufficient permissions for this operation" }), {
					status: 403,
					headers: { "Content-Type": "application/json" },
				});
			}
		}

		return handler(request, authResult.user, context);
	};
}

/**
 * Log admin actions for audit purposes
 */
export async function logAdminAction(
	adminUserId: string,
	action: string,
	tableName: string,
	recordId: string | null,
	oldValues: any | null,
	newValues: any | null,
	request: NextRequest
) {
	try {
		const { ip, headers } = request;
		const userAgent = headers.get("user-agent") || "unknown";
		const sessionId = headers.get("x-session-id") || "unknown";

		if (supabaseAdmin === null) {
			return;
		}

		const { error } = await supabaseAdmin.from("admin_audit_log").insert({
			admin_user_id: adminUserId,
			action,
			table_name: tableName,
			record_id: recordId,
			old_values: oldValues,
			new_values: newValues,
			ip_address: ip,
			user_agent: userAgent,
			session_id: sessionId,
			created_at: new Date().toISOString(),
		});

		if (error) {
			console.error("Error logging admin action:", error);
		}
	} catch (error) {
		console.error("Error in logAdminAction:", error);
	}
}

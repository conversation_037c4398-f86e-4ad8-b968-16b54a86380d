export interface PricingTiers {
	Row: {
		id: string;
		service_id: string;
		tier_name: string;
		min_age: number | null;
		max_age: number | null;
		price: number;
		is_active: boolean;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: {
		id?: string;
		service_id: string;
		tier_name: string;
		min_age?: number | null;
		max_age?: number | null;
		price: number;
		is_active?: boolean;
		created_at?: string | null;
		updated_at?: string | null;
	};
	Update: {
		id?: string;
		service_id?: string;
		tier_name?: string;
		min_age?: number | null;
		max_age?: number | null;
		price?: number;
		is_active?: boolean;
		created_at?: string | null;
		updated_at?: string | null;
	};
}

import { NextRequest, NextResponse } from "next/server";
import { withAdminAuth } from "@/lib/admin-auth";
import { getSupabaseAdmin } from "@/lib/supabase";
import { ServiceOptionAssignment } from "@/lib/types/service-options";
import { extractIdFromSlug, generateSlug } from "@/lib/utils/slug";

// Helper function to resolve service ID from slug or UUID
async function resolveServiceId(slug: string): Promise<string | null> {
	const supabaseAdmin = getSupabaseAdmin();

	// Try to extract UUID from slug first (backward compatibility)
	let serviceId = extractIdFromSlug(slug);

	if (!serviceId) {
		// If no UUID found, treat as name slug and search by normalized name
		const { data: services, error: servicesError } = await supabaseAdmin
			.from("services")
			.select("id, name")
			.eq("is_active", true);

		if (servicesError) {
			console.error("Error fetching services for slug lookup:", servicesError);
			return null;
		}

		// Find service by comparing normalized names
		const targetSlug = slug;
		const matchingService = services?.find((service: any) => {
			const serviceSlug = generateSlug(service.name);
			return serviceSlug === targetSlug;
		});

		if (!matchingService) {
			return null;
		}

		serviceId = matchingService.id;
	}

	return serviceId;
}

// GET /api/admin/services/[id]/option-assignments - Get service option assignments
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const supabaseAdmin = getSupabaseAdmin();
		const serviceId = await resolveServiceId(params.id);

		if (!serviceId) {
			return NextResponse.json({ error: "Service not found" }, { status: 404 });
		}

		// Query service option assignments from database
		const { data: assignments, error } = await supabaseAdmin
			.from("service_option_assignments")
			.select(
				`
        *,
        service_options (
          id,
          name,
          description,
          base_price,
          quote_based,
          per_participant,
          is_active
        )
      `
			)
			.eq("service_id", serviceId)
			.eq("is_active", true)
			.order("sort_order");

		if (error) {
			console.error("Error fetching service option assignments:", error);
			console.error("Service ID:", serviceId);
			return NextResponse.json({ error: "Failed to fetch assignments" }, { status: 500 });
		}

		console.log(`Found ${assignments?.length || 0} assignments for service ${serviceId}`);

		const formattedAssignments: ServiceOptionAssignment[] = assignments.map((assignment) => {
			const option = assignment.service_options;
			return {
				id: assignment.id,
				type: "individual" as const,
				option: {
					id: option.id,
					name: option.name,
					description: option.description || undefined,
					basePrice: option.base_price,
					customPrice: assignment.custom_price || undefined,
					quoteBased: option.quote_based,
					perParticipant: option.per_participant,
					isActive: option.is_active,
				},
				isRequired: assignment.is_required,
				sortOrder: assignment.sort_order,
			};
		});

		return NextResponse.json({ assignments: formattedAssignments });
	} catch (error) {
		console.error("Service option assignments GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:read");

// PUT /api/admin/services/[id]/option-assignments - Update service option assignments
export const PUT = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const serviceId = params.id;
		const body = await request.json();
		const { assignments }: { assignments: ServiceOptionAssignment[] } = body;

		// Start a transaction by deleting existing assignments and creating new ones
		// First, deactivate existing assignments
		const supabaseAdmin = getSupabaseAdmin();
		await supabaseAdmin
			.from("service_option_assignments")
			.update({ is_active: false, updated_at: new Date().toISOString() })
			.eq("service_id", serviceId);

		// Create new assignments (individual options only)
		const insertData = assignments.map((assignment, index) => ({
			service_id: serviceId,
			option_id: assignment.option?.id,
			custom_price: assignment.option?.customPrice || null,
			is_required: assignment.isRequired,
			sort_order: index,
			is_active: true,
		}));

		if (insertData.length > 0) {
			const { error: insertError } = await supabaseAdmin.from("service_option_assignments").insert(insertData);

			if (insertError) {
				console.error("Error creating service option assignments:", insertError);
				return NextResponse.json({ error: "Failed to create assignments" }, { status: 500 });
			}
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Service option assignments PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// DELETE /api/admin/services/[id]/option-assignments - Delete all service option assignments
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const serviceId = params.id;

		const supabaseAdmin = getSupabaseAdmin();
		const { error } = await supabaseAdmin
			.from("service_option_assignments")
			.update({ is_active: false, updated_at: new Date().toISOString() })
			.eq("service_id", serviceId);

		if (error) {
			console.error("Error deleting service option assignments:", error);
			return NextResponse.json({ error: "Failed to delete assignments" }, { status: 500 });
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Service option assignments DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

"use client";

import { adminApi } from "@/lib/api-client";
import { Equipment, EquipmentInsert } from "@/lib/types";
import { AlertCircle, Edit, Loader2, Package, Plus, Save, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";
import Button from "./ui/Button";

interface EquipmentWithStats extends Equipment {
	stats?: {
		totalCapacity: number;
		currentlyReserved: number;
		availableCapacity: number;
		utilizationRate: number;
		totalReservations: number;
		activeReservations: number;
		associatedServices: number;
		revenueGenerated: number;
		maintenanceHours: number;
		downtimeHours: number;
	};
}

interface Service {
	id: string;
	name: string;
	category: string;
	is_active: boolean;
}

const AdminEquipment = () => {
	const [equipment, setEquipment] = useState<EquipmentWithStats[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isCreating, setIsCreating] = useState(false);
	const [editingId, setEditingId] = useState<string | null>(null);
	const [editForm, setEditForm] = useState<Partial<EquipmentInsert>>({});
	const [saving, setSaving] = useState(false);
	const [services, setServices] = useState<Service[]>([]);
	const [selectedServices, setSelectedServices] = useState<{ serviceId: string }[]>([]);

	useEffect(() => {
		fetchEquipment();
		fetchServices();
	}, []);

	const fetchEquipment = async () => {
		try {
			setLoading(true);
			setError(null);
			const response = await adminApi.getEquipment();
			if (response?.equipment) {
				setEquipment(response.equipment);
			}
		} catch (err) {
			console.error("Error fetching equipment:", err);
			setError("Erreur lors du chargement des équipements");
		} finally {
			setLoading(false);
		}
	};

	const fetchServices = async () => {
		try {
			const response = await adminApi.getServices({ limit: 100 });
			if (response?.services) {
				setServices(response.services);
			}
		} catch (err) {
			console.error("Error fetching services:", err);
		}
	};

	const handleCreate = () => {
		setIsCreating(true);
		setEditForm({
			name: "",
			description: "",
			number_of_units: 1,
			capacity_per_unit: 1,
			is_active: true,
		});
	};

	const handleEdit = async (equipmentItem: EquipmentWithStats) => {
		setEditingId(equipmentItem.id);
		setEditForm({
			name: equipmentItem.name,
			description: equipmentItem.description || "",
			number_of_units: equipmentItem.number_of_units,
			capacity_per_unit: equipmentItem.capacity_per_unit,
			is_active: equipmentItem.is_active,
		});

		// Load existing service equipment requirements for this equipment
		try {
			const response = await adminApi.getEquipmentItem(equipmentItem.id);
			if (response?.equipment?.service_equipment_requirements) {
				const existingServices = response.equipment.service_equipment_requirements.map((req: any) => ({
					serviceId: req.service.id,
				}));
				setSelectedServices(existingServices);
			} else {
				setSelectedServices([]);
			}
		} catch (error) {
			console.error("Error loading equipment service requirements:", error);
			setSelectedServices([]);
		}
	};

	const handleSave = async () => {
		if (!editForm.name || !editForm.number_of_units || !editForm.capacity_per_unit) {
			setError("Veuillez remplir tous les champs obligatoires");
			return;
		}

		try {
			setSaving(true);
			setError(null);

			let equipmentId = editingId;

			if (isCreating) {
				const response = await adminApi.createEquipment(editForm);
				equipmentId = response.equipment?.id;
				setIsCreating(false);
			} else if (editingId) {
				await adminApi.updateEquipment(editingId, editForm);
				setEditingId(null);
			}

			// Handle service equipment requirements if equipment was created/updated successfully
			if (equipmentId) {
				// If editing existing equipment, first get current requirements to compare
				let currentRequirements: any[] = [];
				if (editingId) {
					try {
						const currentResponse = await adminApi.getEquipmentItem(equipmentId);
						currentRequirements = currentResponse?.equipment?.service_equipment_requirements || [];
					} catch (error) {
						console.error("Error fetching current requirements:", error);
					}
				}

				// Get new service IDs
				const newServiceIds = new Set(selectedServices.filter((s) => s.serviceId).map((s) => s.serviceId));

				// Remove requirements that are no longer selected
				for (const req of currentRequirements) {
					if (!newServiceIds.has(req.service.id)) {
						try {
							await adminApi.deleteServiceEquipmentRequirement(req.id);
						} catch (error) {
							console.error("Error removing service equipment requirement:", error);
						}
					}
				}

				// Add new requirements (simplified - no capacity_per_participant)
				for (const service of selectedServices) {
					if (service.serviceId) {
						const existingReq = currentRequirements.find(
							(req: any) => req.service.id === service.serviceId
						);

						if (!existingReq) {
							// Create new requirement (simplified)
							try {
								await adminApi.createServiceEquipmentRequirement({
									service_id: service.serviceId,
									equipment_id: equipmentId,
								});
							} catch (error) {
								console.error("Error creating service equipment requirement:", error);
							}
						}
					}
				}
			}

			setEditForm({});
			setSelectedServices([]);
			await fetchEquipment(); // Refresh the list
		} catch (err) {
			console.error("Error saving equipment:", err);
			setError("Erreur lors de la sauvegarde de l'équipement");
		} finally {
			setSaving(false);
		}
	};

	const handleCancel = () => {
		setIsCreating(false);
		setEditingId(null);
		setEditForm({});
		setSelectedServices([]);
		setError(null);
	};

	const handleDelete = async (equipmentId: string) => {
		if (window.confirm("Êtes-vous sûr de vouloir supprimer cet équipement ?")) {
			try {
				await adminApi.deleteEquipment(equipmentId);
				await fetchEquipment(); // Refresh the list
			} catch (err) {
				console.error("Error deleting equipment:", err);
				setError("Erreur lors de la suppression de l'équipement");
			}
		}
	};

	const handleInputChange = (field: keyof EquipmentInsert, value: any) => {
		setEditForm((prev: Partial<EquipmentInsert>) => ({ ...prev, [field]: value }));
	};

	if (loading) {
		return (
			<div className="p-6">
				<div className="flex items-center justify-center h-64">
					<Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
				</div>
			</div>
		);
	}

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Équipements</h1>
					<p className="text-gray-600">Gérez vos équipements et leur capacité</p>
				</div>
				<Button onClick={handleCreate} icon={Plus}>
					Nouvel Équipement
				</Button>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Create/Edit Form */}
			{(isCreating || editingId) && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
					<h2 className="text-xl font-bold text-gray-900 mb-6">
						{isCreating ? "Créer un équipement" : "Modifier l'équipement"}
					</h2>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Nom de l'équipement *
							</label>
							<input
								type="text"
								value={editForm.name || ""}
								onChange={(e) => handleInputChange("name", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Ex: Kayaks, Masques de plongée..."
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Nombre d'unités *</label>
							<input
								type="number"
								min="1"
								value={editForm.number_of_units || 1}
								onChange={(e) => handleInputChange("number_of_units", parseInt(e.target.value))}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Ex: 4 kayaks, 1 bateau"
							/>
							<p className="text-xs text-gray-500 mt-1">
								Combien d'unités de cet équipement possédez-vous ?
							</p>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Capacité par unité *</label>
							<input
								type="number"
								min="1"
								value={editForm.capacity_per_unit || 1}
								onChange={(e) => handleInputChange("capacity_per_unit", parseInt(e.target.value))}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Ex: 1 personne par kayak, 11 personnes par bateau"
							/>
							<p className="text-xs text-gray-500 mt-1">
								Combien de personnes peut accueillir chaque unité ?
							</p>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Statut</label>
							<select
								value={editForm.is_active ? "true" : "false"}
								onChange={(e) => handleInputChange("is_active", e.target.value === "true")}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							>
								<option value="true">Actif</option>
								<option value="false">Inactif</option>
							</select>
						</div>

						<div className="md:col-span-2">
							<label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
							<textarea
								value={editForm.description || ""}
								onChange={(e) => handleInputChange("description", e.target.value)}
								rows={3}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Description de l'équipement..."
							/>
						</div>

						{/* Services Linking Section */}
						<div className="md:col-span-2">
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Services utilisant cet équipement
							</label>
							<div className="space-y-3">
								{selectedServices.map((item, index) => (
									<div
										key={index}
										className="flex items-center gap-3 p-3 border border-gray-300 rounded-lg"
									>
										<select
											value={item.serviceId}
											onChange={(e) => {
												const newSelected = [...selectedServices];
												newSelected[index].serviceId = e.target.value;
												setSelectedServices(newSelected);
											}}
											className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
										>
											<option value="">Sélectionner un service</option>
											{services
												.filter((service) => service.is_active)
												.map((service) => (
													<option key={service.id} value={service.id}>
														{service.name} ({service.category})
													</option>
												))}
										</select>

										<button
											type="button"
											onClick={() => {
												setSelectedServices(selectedServices.filter((_, i) => i !== index));
											}}
											className="text-red-600 hover:text-red-800"
										>
											<X className="w-4 h-4" />
										</button>
									</div>
								))}
								<button
									type="button"
									onClick={() => {
										setSelectedServices([...selectedServices, { serviceId: "" }]);
									}}
									className="flex items-center gap-2 px-3 py-2 text-sm text-emerald-600 border border-emerald-300 rounded-lg hover:bg-emerald-50"
								>
									<Plus className="w-4 h-4" />
									Ajouter un service
								</button>
							</div>
							<p className="text-xs text-gray-500 mt-1">
								Définissez les services qui utilisent cet équipement
							</p>
						</div>
					</div>

					<div className="flex justify-end gap-4 mt-6 pt-6 border-t">
						<Button variant="outline" onClick={handleCancel} icon={X} disabled={saving}>
							Annuler
						</Button>
						<Button onClick={handleSave} icon={saving ? Loader2 : Save} disabled={saving}>
							{saving ? "Sauvegarde..." : isCreating ? "Créer l'équipement" : "Sauvegarder"}
						</Button>
					</div>
				</div>
			)}

			{/* Equipment List */}
			{!loading && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200">
					<div className="p-6 border-b border-gray-200">
						<h2 className="text-xl font-bold text-gray-900">Équipements ({equipment.length})</h2>
					</div>
					<div className="divide-y divide-gray-200">
						{equipment.length === 0 ? (
							<div className="p-12 text-center">
								<Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
								<p className="text-gray-500">Aucun équipement trouvé</p>
							</div>
						) : (
							equipment.map((item) => (
								<div key={item.id} className="p-6">
									<div className="flex items-start justify-between">
										<div className="flex-1">
											<div className="flex items-center gap-3 mb-2">
												<Package className="h-5 w-5 text-emerald-600" />
												<h3 className="text-lg font-semibold text-gray-900">{item.name}</h3>
												<span
													className={`px-2 py-1 text-xs font-medium rounded-full ${
														item.is_active
															? "bg-green-100 text-green-800"
															: "bg-red-100 text-red-800"
													}`}
												>
													{item.is_active ? "Actif" : "Inactif"}
												</span>
											</div>
											{item.description && (
												<p className="text-gray-600 text-sm mb-3">{item.description}</p>
											)}
											<div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
												<div>
													<span className="text-gray-500">Nombre d'unités:</span>
													<p className="font-medium">{item.number_of_units}</p>
												</div>
												<div>
													<span className="text-gray-500">Capacité par unité:</span>
													<p className="font-medium">{item.capacity_per_unit}</p>
												</div>
												{item.stats && (
													<>
														<div>
															<span className="text-gray-500">Disponible:</span>
															<p className="font-medium text-emerald-600">
																{item.stats.availableCapacity}
															</p>
														</div>
														<div>
															<span className="text-gray-500">Utilisation:</span>
															<p className="font-medium">
																{item.stats.utilizationRate?.toFixed(1)}%
															</p>
														</div>
													</>
												)}
											</div>
										</div>
										<div className="flex gap-2 ml-4">
											<Button
												variant="outline"
												size="sm"
												icon={Edit}
												onClick={() => handleEdit(item)}
												disabled={saving}
											>
												Modifier
											</Button>
											<Button
												variant="outline"
												size="sm"
												icon={Trash2}
												onClick={() => handleDelete(item.id)}
												className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
												disabled={saving}
											>
												Supprimer
											</Button>
										</div>
									</div>
								</div>
							))
						)}
					</div>
				</div>
			)}
		</div>
	);
};

export default AdminEquipment;

import { AdminUser, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// GET /api/admin/audit-log - Get admin audit log entries
export const GET = withAdminAuth(async (request: NextRequest, user: AdminUser) => {
	try {
		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get("page") || "1");
		const limit = parseInt(searchParams.get("limit") || "50");
		const search = searchParams.get("search");
		const action = searchParams.get("action");
		const tableName = searchParams.get("table_name");
		const adminUserId = searchParams.get("admin_user_id");
		const startDate = searchParams.get("start_date");
		const endDate = searchParams.get("end_date");

		const offset = (page - 1) * limit;

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
		}

		// Build the query
		let query = supabaseAdmin
			.from("admin_audit_log")
			.select("*", { count: "exact" })
			.order("created_at", { ascending: false })
			.range(offset, offset + limit - 1);

		// Apply filters
		if (search) {
			query = query.or(`action.ilike.%${search}%,table_name.ilike.%${search}%`);
		}
		if (action) {
			query = query.eq("action", action);
		}
		if (tableName) {
			query = query.eq("table_name", tableName);
		}
		if (adminUserId) {
			query = query.eq("admin_user_id", adminUserId);
		}
		if (startDate) {
			query = query.gte("created_at", startDate);
		}
		if (endDate) {
			query = query.lte("created_at", endDate);
		}

		const { data: auditLogs, error, count } = await query;

		if (error) {
			console.error("Error fetching audit logs:", error);
			return NextResponse.json({ error: "Failed to fetch audit logs" }, { status: 500 });
		}

		// Manually fetch admin user details for each audit log entry
		const auditLogsWithUsers = await Promise.all(
			(auditLogs || []).map(async (log) => {
				if (log.admin_user_id && supabaseAdmin) {
					const { data: adminUser } = await supabaseAdmin
						.from("profiles")
						.select("id, first_name, last_name, email")
						.eq("id", log.admin_user_id)
						.single();

					return {
						...log,
						admin_user: adminUser,
					};
				}
				return {
					...log,
					admin_user: null,
				};
			})
		);

		// Get unique actions and table names for filters
		const { data: actions } = await supabaseAdmin
			.from("admin_audit_log")
			.select("action")
			.not("action", "is", null);

		const { data: tableNames } = await supabaseAdmin
			.from("admin_audit_log")
			.select("table_name")
			.not("table_name", "is", null);

		const uniqueActions = [...new Set(actions?.map((a) => a.action) || [])].sort();
		const uniqueTableNames = [...new Set(tableNames?.map((t) => t.table_name) || [])].sort();

		return NextResponse.json({
			auditLogs: auditLogsWithUsers,
			pagination: {
				page,
				limit,
				total: count || 0,
				totalPages: Math.ceil((count || 0) / limit),
			},
			filters: {
				actions: uniqueActions,
				tableNames: uniqueTableNames,
			},
		});
	} catch (error) {
		console.error("Audit log GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "admin:read");

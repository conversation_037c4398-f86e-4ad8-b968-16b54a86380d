"use client";

import { useState, useEffect, useRef } from "react";
import { Search, Plus, User, Mail, Phone, Loader2 } from "lucide-react";
import { adminApi } from "@/lib/api-client";

interface Customer {
	id: string;
	first_name?: string;
	last_name?: string;
	email?: string;
	phone?: string;
}

interface CustomerSearchSelectorProps {
	selectedCustomer: Customer | null;
	onCustomerSelect: (customer: Customer | null) => void;
	onCreateNewCustomer: (customerData: Partial<Customer>) => void;
	disabled?: boolean;
}

export default function CustomerSearchSelector({
	selectedCustomer,
	onCustomerSelect,
	onCreateNewCustomer,
	disabled = false,
}: CustomerSearchSelectorProps) {
	const [searchTerm, setSearchTerm] = useState("");
	const [searchResults, setSearchResults] = useState<Customer[]>([]);
	const [isSearching, setIsSearching] = useState(false);
	const [showDropdown, setShowDropdown] = useState(false);
	const [showCreateForm, setShowCreateForm] = useState(false);
	const [newCustomerData, setNewCustomerData] = useState({
		first_name: "",
		last_name: "",
		email: "",
		phone: "",
	});
	const [isCreating, setIsCreating] = useState(false);

	const searchRef = useRef<HTMLDivElement>(null);
	const searchTimeoutRef = useRef<NodeJS.Timeout>();

	// Handle search with debouncing
	useEffect(() => {
		if (searchTimeoutRef.current) {
			clearTimeout(searchTimeoutRef.current);
		}

		if (searchTerm.length >= 2) {
			searchTimeoutRef.current = setTimeout(async () => {
				setIsSearching(true);
				try {
					const response = await adminApi.getCustomers({
						search: searchTerm,
						limit: 10,
					});
					setSearchResults(response.customers || []);
					setShowDropdown(true);
				} catch (error) {
					console.error("Error searching customers:", error);
					setSearchResults([]);
				} finally {
					setIsSearching(false);
				}
			}, 300);
		} else {
			setSearchResults([]);
			setShowDropdown(false);
		}

		return () => {
			if (searchTimeoutRef.current) {
				clearTimeout(searchTimeoutRef.current);
			}
		};
	}, [searchTerm]);

	// Close dropdown when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
				setShowDropdown(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, []);

	const handleCustomerSelect = (customer: Customer) => {
		onCustomerSelect(customer);
		setSearchTerm("");
		setShowDropdown(false);
	};

	const handleCreateCustomer = async () => {
		if (
			!newCustomerData.first_name ||
			!newCustomerData.last_name ||
			!newCustomerData.email ||
			!newCustomerData.phone
		) {
			return;
		}

		setIsCreating(true);
		try {
			onCreateNewCustomer(newCustomerData);
			setNewCustomerData({ first_name: "", last_name: "", email: "", phone: "" });
			setShowCreateForm(false);
		} catch (error) {
			console.error("Error creating customer:", error);
		} finally {
			setIsCreating(false);
		}
	};

	const formatCustomerName = (customer: Customer) => {
		const name = `${customer.first_name || ""} ${customer.last_name || ""}`.trim();
		return name || "Client sans nom";
	};

	return (
		<div className="space-y-4">
			<div className="relative" ref={searchRef}>
				<label className="block text-sm font-medium text-gray-700 mb-2">
					Client <span className="text-red-500">*</span>
				</label>

				{/* Selected Customer Display */}
				{selectedCustomer && (
					<div className="mb-3 p-3 bg-emerald-50 border border-emerald-200 rounded-lg">
						<div className="flex items-center justify-between">
							<div className="flex items-center space-x-3">
								<User className="h-5 w-5 text-emerald-600" />
								<div>
									<p className="font-medium text-gray-900">{formatCustomerName(selectedCustomer)}</p>
									<div className="flex items-center space-x-4 text-sm text-gray-600">
										{selectedCustomer.email && (
											<span className="flex items-center">
												<Mail className="h-3 w-3 mr-1" />
												{selectedCustomer.email}
											</span>
										)}
										{selectedCustomer.phone && (
											<span className="flex items-center">
												<Phone className="h-3 w-3 mr-1" />
												{selectedCustomer.phone}
											</span>
										)}
									</div>
								</div>
							</div>
							<button
								onClick={() => onCustomerSelect(null)}
								className="text-gray-400 hover:text-gray-600"
								disabled={disabled}
							>
								✕
							</button>
						</div>
					</div>
				)}

				{/* Search Input */}
				{!selectedCustomer && (
					<div className="relative">
						<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							{isSearching ? (
								<Loader2 className="h-4 w-4 text-gray-400 animate-spin" />
							) : (
								<Search className="h-4 w-4 text-gray-400" />
							)}
						</div>
						<input
							type="text"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							placeholder="Rechercher un client par nom, email ou téléphone..."
							className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
							disabled={disabled}
						/>

						{/* Search Results Dropdown */}
						{showDropdown && (
							<div className="absolute z-50 mt-1 w-full bg-white shadow-lg max-h-80 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
								{searchResults.length > 0 ? (
									<>
										{searchResults.map((customer) => (
											<button
												key={customer.id}
												onClick={() => handleCustomerSelect(customer)}
												className="w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
											>
												<div className="flex items-center space-x-3">
													<User className="h-4 w-4 text-gray-400" />
													<div>
														<p className="font-medium text-gray-900">
															{formatCustomerName(customer)}
														</p>
														<div className="flex items-center space-x-3 text-sm text-gray-600">
															{customer.email && <span>{customer.email}</span>}
															{customer.phone && <span>{customer.phone}</span>}
														</div>
													</div>
												</div>
											</button>
										))}
										<div className="border-t border-gray-100 mt-1 pt-1">
											<button
												onClick={() => setShowCreateForm(true)}
												className="w-full text-left px-4 py-2 hover:bg-emerald-50 focus:bg-emerald-50 focus:outline-none text-emerald-600"
											>
												<div className="flex items-center space-x-2">
													<Plus className="h-4 w-4" />
													<span>Créer un nouveau client</span>
												</div>
											</button>
										</div>
									</>
								) : (
									<div className="px-4 py-2">
										<p className="text-gray-500 text-sm">Aucun client trouvé</p>
										<button
											onClick={() => setShowCreateForm(true)}
											className="mt-2 flex items-center space-x-2 text-emerald-600 hover:text-emerald-700"
										>
											<Plus className="h-4 w-4" />
											<span>Créer un nouveau client</span>
										</button>
									</div>
								)}
							</div>
						)}
					</div>
				)}

				{/* Create New Customer Button (when no search) */}
				{!selectedCustomer && !searchTerm && (
					<button
						onClick={() => setShowCreateForm(true)}
						className="mt-2 flex items-center space-x-2 text-emerald-600 hover:text-emerald-700"
						disabled={disabled}
					>
						<Plus className="h-4 w-4" />
						<span>Créer un nouveau client</span>
					</button>
				)}
			</div>

			{/* Create New Customer Form */}
			{showCreateForm && (
				<div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
					<h4 className="font-medium text-gray-900 mb-3">Créer un nouveau client</h4>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Prénom <span className="text-red-500">*</span>
							</label>
							<input
								type="text"
								value={newCustomerData.first_name}
								onChange={(e) => setNewCustomerData({ ...newCustomerData, first_name: e.target.value })}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								disabled={isCreating}
							/>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Nom <span className="text-red-500">*</span>
							</label>
							<input
								type="text"
								value={newCustomerData.last_name}
								onChange={(e) => setNewCustomerData({ ...newCustomerData, last_name: e.target.value })}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								disabled={isCreating}
							/>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Email <span className="text-red-500">*</span>
							</label>
							<input
								type="email"
								value={newCustomerData.email}
								onChange={(e) => setNewCustomerData({ ...newCustomerData, email: e.target.value })}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								disabled={isCreating}
							/>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Téléphone <span className="text-red-500">*</span>
							</label>
							<input
								type="tel"
								value={newCustomerData.phone}
								onChange={(e) => setNewCustomerData({ ...newCustomerData, phone: e.target.value })}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								disabled={isCreating}
							/>
						</div>
					</div>
					<div className="flex justify-end space-x-3 mt-4">
						<button
							onClick={() => setShowCreateForm(false)}
							className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
							disabled={isCreating}
						>
							Annuler
						</button>
						<button
							onClick={handleCreateCustomer}
							disabled={
								isCreating ||
								!newCustomerData.first_name ||
								!newCustomerData.last_name ||
								!newCustomerData.email ||
								!newCustomerData.phone
							}
							className="px-4 py-2 text-sm font-medium text-white bg-emerald-600 border border-transparent rounded-md hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
						>
							{isCreating && <Loader2 className="h-4 w-4 animate-spin" />}
							<span>Créer</span>
						</button>
					</div>
				</div>
			)}
		</div>
	);
}

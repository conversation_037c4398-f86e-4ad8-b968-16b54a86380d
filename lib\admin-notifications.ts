import { createClient } from "@supabase/supabase-js";

// Use service role client for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Get the admin notification email from business settings
 * Falls back to default email if not configured
 */
export async function getAdminNotificationEmail(): Promise<string> {
	try {
		console.log("Fetching admin notification email from business settings...");

		const { data: setting, error } = await adminClient
			.from("business_settings")
			.select("value")
			.eq("key", "admin_notification_email")
			.single();

		if (error) {
			console.log("No admin notification email setting found, using default");
			return "<EMAIL>";
		}

		const adminEmail = setting?.value || "<EMAIL>";
		console.log("Admin notification email:", adminEmail);

		return adminEmail;
	} catch (error) {
		console.error("Error fetching admin notification email:", error);
		// Fall back to default email if there's an error
		return "<EMAIL>";
	}
}

/**
 * Get admin notification email for client-side usage
 * Uses the useSettings hook
 */
export function useAdminNotificationEmail(): string {
	// This will be used in client components
	// For now, return the default - we'll update this if needed
	return "<EMAIL>";
}

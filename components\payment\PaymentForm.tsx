"use client";

import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatAmount } from "@/lib/stripe";
import { PaymentElement, useElements, useStripe } from "@stripe/react-stripe-js";
import { AlertCircle, CheckCircle, CreditCard, Loader2 } from "lucide-react";
import { useState, useEffect, useRef } from "react";
// Debug logging removed for production

// Utility functions for payment error handling
const getPaymentErrorMessage = (error: any): string => {
	if (error.type === "card_error") {
		switch (error.code) {
			case "card_declined":
				return "Votre carte a été refusée. Veuillez essayer avec une autre carte.";
			case "insufficient_funds":
				return "Fonds insuffisants. Veuillez vérifier votre solde.";
			case "expired_card":
				return "Votre carte a expiré. Veuillez utiliser une carte valide.";
			case "incorrect_cvc":
				return "Le code de sécurité est incorrect.";
			case "processing_error":
				return "Erreur de traitement. Veuillez réessayer.";
			default:
				return error.message || "Erreur de carte. Veuillez réessayer.";
		}
	}

	// Check for network/ad blocker related errors
	if (error.message?.includes("Failed to fetch") || error.message?.includes("ERR_BLOCKED_BY_CLIENT")) {
		return "Erreur de réseau détectée. Si vous utilisez un bloqueur de publicités, veuillez le désactiver temporairement et réessayer. Votre paiement pourrait avoir été traité malgré cette erreur.";
	}

	return error.message || "Erreur de paiement. Veuillez réessayer.";
};

const isRetryableError = (error: any): boolean => {
	const retryableCodes = ["processing_error", "rate_limit", "api_connection_error"];
	return retryableCodes.includes(error.code) || error.type === "api_connection_error";
};

interface PaymentFormProps {
	amount: number; // in euros
	currency?: string;
	reservationId: string;
	onSuccess: (paymentIntentId: string) => void;
	onError: (error: string) => void;
	onRetry?: () => void;
	disabled?: boolean;
}

export function PaymentForm({
	amount,
	currency = "EUR",
	reservationId,
	onSuccess,
	onError,
	onRetry,
	disabled = false,
}: PaymentFormProps) {
	const stripe = useStripe();
	const elements = useElements();

	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [isComplete, setIsComplete] = useState(false);
	const [canRetry, setCanRetry] = useState(false);
	const [isRetrying, setIsRetrying] = useState(false);
	const paymentCompletedRef = useRef(false);

	// Cleanup disabled - rely on cron job for cleanup instead
	// This prevents premature cancellation of reservations that require security deposits
	useEffect(() => {
		// No cleanup listeners added - cron job will handle abandoned reservations
	}, [reservationId]);

	// Check button state
	const isButtonDisabled = !stripe || !elements || isLoading || disabled;

	const handleSubmit = async (event: React.FormEvent) => {
		event.preventDefault();

		if (!stripe || !elements) {
			setError("Stripe n'est pas encore chargé. Veuillez réessayer.");
			return;
		}

		setIsLoading(true);
		setError(null);

		try {
			// Confirm payment
			const { error: submitError } = await elements.submit();
			if (submitError) {
				setError(submitError.message || "Erreur lors de la soumission du paiement");
				setIsLoading(false);
				return;
			}

			const { error: confirmError, paymentIntent } = await stripe.confirmPayment({
				elements,
				confirmParams: {
					return_url: `${typeof window !== "undefined" ? window.location.origin : ""}/reservation/payment-success?booking=${reservationId}`,
				},
				redirect: "if_required",
			});

			// Check if payment succeeded without redirect
			if (!confirmError && paymentIntent && paymentIntent.status === "succeeded") {
				paymentCompletedRef.current = true;
				setIsComplete(true);
				onSuccess(paymentIntent.id);
				return;
			}

			if (confirmError) {
				// Check if this is a network error that might indicate ad blocker interference
				if (
					confirmError.message?.includes("Failed to fetch") ||
					confirmError.message?.includes("ERR_BLOCKED_BY_CLIENT")
				) {
					// Payment might have succeeded despite the error - try to confirm with backend

					// Wait a moment for potential webhook processing
					await new Promise((resolve) => setTimeout(resolve, 2000));

					try {
						// Try to get payment status from our backend
						const statusResponse = await fetch(`/api/payments/status/reservation/${reservationId}`, {
							method: "GET",
						});

						if (statusResponse.ok) {
							const statusResult = await statusResponse.json();
							if (statusResult.success && statusResult.payment.status === "completed") {
								// Payment actually succeeded!
								paymentCompletedRef.current = true;
								setIsComplete(true);
								onSuccess(statusResult.payment.paymentIntentId);
								return;
							}
						}
					} catch (statusError) {
						console.error("Error checking payment status:", statusError);
					}
				}

				const userFriendlyError = getPaymentErrorMessage(confirmError);
				const retryable = isRetryableError(confirmError);

				setError(userFriendlyError);
				setCanRetry(retryable);
				onError(confirmError.message || "Payment failed");
			} else if (paymentIntent && paymentIntent.status === "succeeded") {
				// Payment successful
				paymentCompletedRef.current = true;
				setIsComplete(true);

				// Confirm payment on our backend
				try {
					const response = await fetch("/api/payments/confirm", {
						method: "POST",
						headers: {
							"Content-Type": "application/json",
						},
						body: JSON.stringify({
							paymentIntentId: paymentIntent.id,
						}),
					});

					const result = await response.json();

					if (result.success) {
						onSuccess(paymentIntent.id);
					} else {
						setError(result.error || "Erreur lors de la confirmation du paiement");
						onError(result.error || "Payment confirmation failed");
					}
				} catch (confirmationError) {
					console.error("Error confirming payment:", confirmationError);
					setError("Erreur lors de la confirmation du paiement");
					onError("Payment confirmation failed");
				}
			}
		} catch (err) {
			console.error("Payment error:", err);
			setError("Une erreur inattendue s'est produite");
			onError("Unexpected error occurred");
		} finally {
			setIsLoading(false);
		}
	};

	const handleElementChange = (event: any) => {
		if (event.error) {
			setError(event.error.message);
		} else {
			setError(null);
		}
	};

	const handleRetry = async () => {
		if (!onRetry) return;

		setIsRetrying(true);
		setError(null);
		setCanRetry(false);

		try {
			if (onRetry) {
				onRetry();
			}
		} catch (error) {
			console.error("Retry failed:", error);
			setError("Échec de la nouvelle tentative");
		} finally {
			setIsRetrying(false);
		}
	};

	if (isComplete) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="text-center">
						<CheckCircle className="w-16 h-16 text-emerald-500 mx-auto mb-4" />
						<h3 className="text-lg font-semibold text-emerald-600 mb-2">Paiement réussi !</h3>
						<p className="text-gray-600">
							Votre paiement de {formatAmount(amount * 100)} a été traité avec succès.
						</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<CreditCard className="w-5 h-5" />
					Paiement sécurisé
				</CardTitle>
				<div className="text-sm text-gray-600">
					Montant à payer : <span className="font-semibold">{formatAmount(amount * 100)}</span>
				</div>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					<div className="p-4 border rounded-lg bg-gray-50">
						<PaymentElement
							onChange={handleElementChange}
							options={{
								layout: "tabs",
							}}
						/>
					</div>

					{error && (
						<Alert variant="destructive">
							<AlertCircle className="h-4 w-4" />
							<AlertDescription>
								<div className="space-y-2">
									<p>{error}</p>
									{canRetry && onRetry && (
										<Button
											variant="outline"
											size="sm"
											onClick={handleRetry}
											disabled={isRetrying}
											className="mt-2"
										>
											{isRetrying ? (
												<>
													<Loader2 className="w-3 h-3 mr-1 animate-spin" />
													Nouvelle tentative...
												</>
											) : (
												"Réessayer"
											)}
										</Button>
									)}
								</div>
							</AlertDescription>
						</Alert>
					)}

					<div className="space-y-2">
						<Button
							type="submit"
							disabled={isButtonDisabled}
							className="w-full bg-emerald-500 hover:bg-emerald-600"
						>
							{isLoading ? (
								<>
									<Loader2 className="w-4 h-4 mr-2 animate-spin" />
									Traitement en cours...
								</>
							) : (
								<>
									<CreditCard className="w-4 h-4 mr-2" />
									Payer {formatAmount(amount * 100)}
								</>
							)}
						</Button>

						<div className="text-xs text-gray-500 text-center">
							Paiement sécurisé par Stripe. Vos informations sont protégées.
						</div>
					</div>
				</form>
			</CardContent>
		</Card>
	);
}

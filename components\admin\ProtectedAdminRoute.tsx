"use client"

import { useAuth } from '@/lib/auth-context'
import { Loader2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

interface ProtectedAdminRouteProps {
  children: React.ReactNode
}

export default function ProtectedAdminRoute({ children }: ProtectedAdminRouteProps) {
  const { user, loading, isAdmin, isManager, isEmployee } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (loading) return

    // Not authenticated
    if (!user) {
      router.push('/admin/login')
      return
    }

    // Not admin, manager, or employee
    if (!isAdmin && !isManager && !isEmployee) {
      router.push('/admin/login')
      return
    }
  }, [user, loading, router, isAdmin, isManager, isEmployee])

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
          <span className="text-gray-600">Vérification des autorisations...</span>
        </div>
      </div>
    )
  }

  // Not authenticated or insufficient permissions
  if (!user || (!isAdmin && !isManager && !isEmployee)) {
    return null // Will redirect in useEffect
  }

  return <>{children}</>
}

import { withAdminAuth } from "@/lib/admin-auth";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// GET /api/admin - Admin API documentation and health check
export const GET = withAdminAuth(async (request: NextRequest, user) => {
	const baseUrl = new URL(request.url).origin;

	return NextResponse.json({
		message: "Soleil et Découverte Admin API",
		version: "1.0.0",
		user: {
			id: user.id,
			email: user.email,
			role: user.role,
		},
		endpoints: {
			analytics: {
				description: "Business intelligence and dashboard analytics",
				endpoints: {
					"GET /api/admin/analytics": {
						description: "Get dashboard KPIs and analytics data",
						parameters: {
							period: "Number of days (default: 30)",
							start_date: "Start date (ISO string)",
							end_date: "End date (ISO string)",
						},
						permission: "analytics:read",
					},
				},
			},
			services: {
				description: "Service management and configuration",
				endpoints: {
					"GET /api/admin/services": {
						description: "List all services with admin details",
						parameters: {
							page: "Page number (default: 1)",
							limit: "Items per page (default: 20)",
							search: "Search in name and description",
							category: "Filter by category",
							active: "Filter by active status (true/false)",
						},
						permission: "services:read",
					},
					"POST /api/admin/services": {
						description: "Create new service",
						body: "ServiceInsert object",
						permission: "services:write",
					},
					"PUT /api/admin/services": {
						description: "Bulk update services",
						body: "{ serviceIds: string[], updates: ServiceUpdate }",
						permission: "services:write",
					},
					"DELETE /api/admin/services": {
						description: "Bulk deactivate services",
						body: "{ serviceIds: string[] }",
						permission: "services:delete",
					},
					"GET /api/admin/services/[id]": {
						description: "Get single service with full details and statistics",
						permission: "services:read",
					},
					"PUT /api/admin/services/[id]": {
						description: "Update single service",
						body: "ServiceUpdate object",
						permission: "services:write",
					},
					"DELETE /api/admin/services/[id]": {
						description: "Deactivate single service",
						permission: "services:delete",
					},
				},
			},
			reservations: {
				description: "Reservation management and oversight",
				endpoints: {
					"GET /api/admin/reservations": {
						description: "List all reservations with admin details",
						parameters: {
							page: "Page number (default: 1)",
							limit: "Items per page (default: 20)",
							status: "Filter by status",
							service_id: "Filter by service",
							customer_id: "Filter by customer",
							employee_id: "Filter by assigned employee",
							date_from: "Filter from date",
							date_to: "Filter to date",
							search: "Search in reservation number and requests",
						},
						permission: "reservations:read",
					},
					"PUT /api/admin/reservations": {
						description: "Bulk update reservations",
						body: "{ reservationIds: string[], updates: ReservationUpdate }",
						permission: "reservations:write",
					},
					"DELETE /api/admin/reservations": {
						description: "Cancel reservations",
						body: "{ reservationIds: string[], reason?: string }",
						permission: "reservations:write",
					},
					"GET /api/admin/reservations/[id]": {
						description:
							"Get single reservation with full details, customer history, and financial summary",
						permission: "reservations:read",
					},
					"PUT /api/admin/reservations/[id]": {
						description: "Update single reservation with status change tracking",
						body: "ReservationUpdate & { status_change_reason?: string }",
						permission: "reservations:write",
					},
					"DELETE /api/admin/reservations/[id]": {
						description: "Cancel single reservation with optional refund processing",
						body: "{ reason?: string, refund_amount?: number }",
						permission: "reservations:write",
					},
				},
			},
			employees: {
				description: "Employee management and scheduling",
				endpoints: {
					"GET /api/admin/employees": {
						description: "List all employees with admin details",
						parameters: {
							page: "Page number (default: 1)",
							limit: "Items per page (default: 20)",
							search: "Search in name and email",
							active: "Filter by active status (true/false)",
							role: "Filter by role",
						},
						permission: "employees:read",
					},
					"POST /api/admin/employees": {
						description: "Create new employee",
						body: "EmployeeInsert object",
						permission: "employees:write",
					},
					"PUT /api/admin/employees": {
						description: "Bulk update employees",
						body: "{ employeeIds: string[], updates: EmployeeUpdate }",
						permission: "employees:write",
					},
					"DELETE /api/admin/employees": {
						description: "Deactivate employees",
						body: "{ employeeIds: string[] }",
						permission: "employees:write",
					},
					"GET /api/admin/employees/[id]": {
						description: "Get single employee with performance metrics, schedule, and qualifications",
						permission: "employees:read",
					},
					"PUT /api/admin/employees/[id]": {
						description: "Update single employee",
						body: "EmployeeUpdate object",
						permission: "employees:write",
					},
					"DELETE /api/admin/employees/[id]": {
						description: "Deactivate single employee",
						permission: "employees:write",
					},
				},
			},
			customers: {
				description: "Customer management and data",
				endpoints: {
					"GET /api/admin/customers": {
						description: "List all customers with admin details",
						parameters: {
							page: "Page number (default: 1)",
							limit: "Items per page (default: 20)",
							search: "Search in name, email, phone",
							has_reservations: "Filter by reservation status (true/false)",
							marketing_consent: "Filter by marketing consent (true/false)",
						},
						permission: "customers:read",
					},
					"PUT /api/admin/customers": {
						description: "Bulk update customers",
						body: "{ customerIds: string[], updates: CustomerUpdate }",
						permission: "customers:write",
					},
					"POST /api/admin/customers/export": {
						description: "Export customer data",
						body: '{ customerIds?: string[], format?: "json" | "csv" }',
						permission: "customers:read",
					},
					"GET /api/admin/customers/[id]": {
						description: "Get single customer with full details, analytics, and journey insights",
						permission: "customers:read",
					},
					"PUT /api/admin/customers/[id]": {
						description: "Update single customer",
						body: "CustomerUpdate object",
						permission: "customers:write",
					},
					"DELETE /api/admin/customers/[id]": {
						description: "Anonymize customer data (GDPR compliant)",
						permission: "customers:delete",
					},
				},
			},
			equipment: {
				description: "Equipment management and utilization",
				endpoints: {
					"GET /api/admin/equipment": {
						description: "List all equipment with utilization data",
						parameters: {
							page: "Page number (default: 1)",
							limit: "Items per page (default: 20)",
							search: "Search in name and description",
							active: "Filter by active status (true/false)",
						},
						permission: "equipment:read",
					},
					"POST /api/admin/equipment": {
						description: "Create new equipment",
						body: "EquipmentInsert object",
						permission: "equipment:write",
					},
					"PUT /api/admin/equipment": {
						description: "Bulk update equipment",
						body: "{ equipmentIds: string[], updates: EquipmentUpdate }",
						permission: "equipment:write",
					},
					"DELETE /api/admin/equipment": {
						description: "Deactivate equipment",
						body: "{ equipmentIds: string[] }",
						permission: "equipment:write",
					},
					"GET /api/admin/equipment/[id]": {
						description: "Get single equipment with utilization data, schedule, and service associations",
						permission: "equipment:read",
					},
					"PUT /api/admin/equipment/[id]": {
						description: "Update single equipment with capacity validation",
						body: "EquipmentUpdate object",
						permission: "equipment:write",
					},
					"DELETE /api/admin/equipment/[id]": {
						description: "Deactivate single equipment",
						permission: "equipment:write",
					},
				},
			},
		},
		authentication: {
			description: "All admin endpoints require Bearer token authentication",
			header: "Authorization: Bearer <jwt_token>",
			roles: ["admin", "manager", "employee"],
		},
		response_format: {
			success: {
				data: "Requested data",
				pagination: "For list endpoints: { page, limit, total, totalPages }",
			},
			error: {
				error: "Error message",
				details: "Additional error details (optional)",
			},
		},
		status_codes: {
			200: "Success",
			201: "Created",
			400: "Bad Request - Invalid input or business rule violation",
			401: "Unauthorized - Missing or invalid authentication",
			403: "Forbidden - Insufficient permissions",
			404: "Not Found - Resource not found",
			500: "Internal Server Error",
		},
	});
}, "services:read"); // Minimal permission required to access API docs

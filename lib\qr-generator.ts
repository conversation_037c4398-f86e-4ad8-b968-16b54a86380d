import QRCode from "qrcode";
import { appConfig } from "./env";

/**
 * Generate QR code for booking verification
 */
export async function generateBookingQRCode(reservationId: string): Promise<string> {
	try {
		const verificationUrl = generateVerificationUrl(reservationId);
		const qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
			errorCorrectionLevel: "M",
			margin: 1,
			color: {
				dark: "#000000",
				light: "#FFFFFF",
			},
			width: 256,
		});

		return qrCodeDataUrl;
	} catch (error) {
		console.error("Error generating QR code:", error);
		throw new Error("Failed to generate QR code");
	}
}

/**
 * Generate verification URL for a reservation
 */
export function generateVerificationUrl(reservationId: string): string {
	return `${appConfig.url}/verify/${reservationId}`;
}

/**
 * Generate QR code as buffer for PDF embedding
 */
export async function generateBookingQRCodeBuffer(reservationId: string): Promise<Buffer> {
	try {
		const verificationUrl = generateVerificationUrl(reservationId);
		const qrCodeBuffer = await QRCode.toBuffer(verificationUrl, {
			errorCorrectionLevel: "M",
			margin: 1,
			color: {
				dark: "#000000",
				light: "#FFFFFF",
			},
			width: 256,
		});

		return qrCodeBuffer;
	} catch (error) {
		console.error("Error generating QR code buffer:", error);
		throw new Error("Failed to generate QR code buffer");
	}
}

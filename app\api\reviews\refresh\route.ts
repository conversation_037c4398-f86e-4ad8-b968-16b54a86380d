import { googleReviewsService } from "@/lib/google-reviews-service";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// POST /api/reviews/refresh - Weekly refresh of Google reviews
export async function POST(request: NextRequest) {
	try {
		// Check if refresh is needed
		const needsRefresh = await googleReviewsService.needsWeeklyRefresh();
		
		if (!needsRefresh) {
			return NextResponse.json({
				success: true,
				message: "Reviews are still fresh, no refresh needed",
				refreshed: false,
				timestamp: new Date().toISOString(),
			});
		}

		console.log("Starting weekly Google Reviews refresh...");

		// Perform the refresh
		const refreshResult = await googleReviewsService.refreshReviews();

		if (refreshResult.success) {
			const reviews = await googleReviewsService.getCachedReviews();

			return NextResponse.json({
				success: true,
				message: `Successfully refreshed ${refreshResult.count} reviews`,
				refreshed: true,
				count: refreshResult.count,
				cachedCount: reviews.length,
				timestamp: new Date().toISOString(),
			});
		} else {
			return NextResponse.json(
				{
					success: false,
					error: "Failed to refresh reviews",
					details: refreshResult.error,
					refreshed: false,
					count: 0,
				},
				{ status: 500 }
			);
		}
	} catch (error) {
		console.error("Error in weekly refresh:", error);
		return NextResponse.json(
			{
				success: false,
				error: "Internal server error during refresh",
				refreshed: false,
				count: 0,
			},
			{ status: 500 }
		);
	}
}

// GET /api/reviews/refresh - Check refresh status
export async function GET(request: NextRequest) {
	try {
		const needsRefresh = await googleReviewsService.needsWeeklyRefresh();
		const reviews = await googleReviewsService.getCachedReviews();
		
		// Get last refresh time
		const googlePlacesReviews = reviews.filter(r => r.source === 'google_places');
		const lastRefresh = googlePlacesReviews.length > 0 
			? Math.max(...googlePlacesReviews.map(r => new Date(r.updated_at).getTime()))
			: null;

		return NextResponse.json({
			needsRefresh,
			totalCachedReviews: reviews.length,
			googlePlacesReviews: googlePlacesReviews.length,
			lastRefresh: lastRefresh ? new Date(lastRefresh).toISOString() : null,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		console.error("Error checking refresh status:", error);
		return NextResponse.json(
			{ error: "Failed to check refresh status" },
			{ status: 500 }
		);
	}
}

import { NextRequest, NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { getStripeServer } from "@/lib/stripe";
import { withAdminAuth } from "@/lib/admin-auth";

export const GET = withAdminAuth(async (request: NextRequest, user, { params }) => {
	try {
		const reservationId = params.id;

		if (!reservationId) {
			return NextResponse.json({ error: "Reservation ID is required" }, { status: 400 });
		}

		const supabaseAdmin = getSupabaseAdmin();

		// Get reservation with security deposit info
		const { data: reservation, error: reservationError } = await supabaseAdmin
			.from("reservations")
			.select(`
				id,
				security_deposit_status,
				security_deposit_payment_intent_id,
				security_deposit_method
			`)
			.eq("id", reservationId)
			.single();

		if (reservationError || !reservation) {
			return NextResponse.json({ error: "Reservation not found" }, { status: 404 });
		}

		if (!reservation.security_deposit_payment_intent_id) {
			return NextResponse.json({ error: "No payment intent found for security deposit" }, { status: 404 });
		}

		// Get current Stripe payment intent status
		const stripe = getStripeServer();
		const paymentIntent = await stripe.paymentIntents.retrieve(reservation.security_deposit_payment_intent_id);

		return NextResponse.json({
			success: true,
			data: {
				database_status: reservation.security_deposit_status,
				stripe_status: paymentIntent.status,
				stripe_amount: paymentIntent.amount,
				stripe_amount_capturable: paymentIntent.amount_capturable,
				stripe_capture_method: paymentIntent.capture_method,
				stripe_created: new Date(paymentIntent.created * 1000).toISOString(),
				stripe_metadata: paymentIntent.metadata,
				can_capture: paymentIntent.status === "requires_capture",
				can_cancel: ["requires_payment_method", "requires_confirmation", "requires_action", "requires_capture"].includes(paymentIntent.status),
			},
		});
	} catch (error) {
		console.error("Error getting security deposit Stripe status:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}, "reservations:read");

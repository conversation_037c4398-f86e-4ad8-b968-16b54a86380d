"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { calculateDepositAmount, calculateRemainingAmount, getDepositSettings } from "@/lib/deposit-settings";
import { DepositCalculation, PaymentOption, PaymentType } from "@/lib/types/payments";
import { CheckCircle, Clock, CreditCard, Percent } from "lucide-react";
import { useEffect, useState } from "react";

interface DepositSelectionProps {
	totalAmount: number;
	currency?: string;
	onPaymentTypeSelect: (paymentType: PaymentType, amount: number) => void;
	selectedPaymentType?: PaymentType;
	disabled?: boolean;
}

export function DepositSelection({
	totalAmount,
	currency = "EUR",
	onPaymentTypeSelect,
	selectedPaymentType,
	disabled = false,
}: DepositSelectionProps) {
	const [depositCalculation, setDepositCalculation] = useState<DepositCalculation | null>(null);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		const loadDepositSettings = async () => {
			try {
				setLoading(true);

				// Fetch deposit settings
				const response = await fetch("/api/deposit-settings");
				const result = await response.json();

				if (!result.success) {
					throw new Error("Failed to load deposit settings");
				}

				const { depositPercentage, isDepositEnabled } = result;

				if (!isDepositEnabled) {
					// If deposits are disabled, only show full payment option
					const calculation: DepositCalculation = {
						totalAmount,
						depositAmount: totalAmount,
						remainingAmount: 0,
						depositPercentage: 100,
						paymentOptions: [
							{
								type: "full",
								label: "Paiement intégral",
								description: "Payez le montant total maintenant",
								amount: totalAmount,
								isRecommended: true,
							},
						],
					};
					setDepositCalculation(calculation);
					return;
				}

				// Calculate deposit and remaining amounts
				const depositSettings = await getDepositSettings();
				const depositAmount = calculateDepositAmount(totalAmount, depositSettings);
				const remainingAmount = calculateRemainingAmount(totalAmount, depositAmount);

				const paymentOptions: PaymentOption[] = [
					{
						type: "full",
						label: "Paiement intégral",
						description: "Payez le montant total maintenant",
						amount: totalAmount,
						isRecommended: true,
					},
					{
						type: "deposit",
						label: `Acompte (${depositPercentage}%)`,
						description: `Payez ${depositPercentage}% maintenant, le reste sur place`,
						amount: depositAmount,
						isRecommended: false,
					},
				];

				const calculation: DepositCalculation = {
					totalAmount,
					depositAmount,
					remainingAmount,
					depositPercentage,
					paymentOptions,
				};

				setDepositCalculation(calculation);
			} catch (error) {
				console.error("Error loading deposit settings:", error);
				// Fallback to full payment only
				const calculation: DepositCalculation = {
					totalAmount,
					depositAmount: totalAmount,
					remainingAmount: 0,
					depositPercentage: 100,
					paymentOptions: [
						{
							type: "full",
							label: "Paiement intégral",
							description: "Payez le montant total maintenant",
							amount: totalAmount,
							isRecommended: true,
						},
					],
				};
				setDepositCalculation(calculation);
			} finally {
				setLoading(false);
			}
		};

		loadDepositSettings();
	}, [totalAmount]);

	const formatAmount = (amount: number): string => {
		return new Intl.NumberFormat("fr-FR", {
			style: "currency",
			currency,
		}).format(amount);
	};

	if (loading) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="flex items-center justify-center">
						<div className="animate-spin rounded-full h-6 w-6 border-b-2 border-emerald-500"></div>
						<span className="ml-2 text-gray-600">Chargement des options de paiement...</span>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (!depositCalculation) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="text-center text-red-600">Erreur lors du chargement des options de paiement</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<CreditCard className="w-5 h-5" />
					Options de paiement
				</CardTitle>
				<p className="text-sm text-gray-600">Choisissez votre mode de paiement préféré</p>
			</CardHeader>
			<CardContent className="space-y-4">
				{depositCalculation.paymentOptions.map((option) => (
					<div
						key={option.type}
						className={`relative border rounded-lg p-4 cursor-pointer transition-all ${
							selectedPaymentType === option.type
								? "border-emerald-500 bg-emerald-50"
								: "border-gray-200 hover:border-gray-300"
						} ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
						onClick={() => !disabled && onPaymentTypeSelect(option.type, option.amount)}
					>
						<div className="flex items-start justify-between">
							<div className="flex-1">
								<div className="flex items-center gap-2 mb-2">
									{option.type === "deposit" && <Percent className="w-4 h-4 text-emerald-600" />}
									{option.type === "full" && <CreditCard className="w-4 h-4 text-blue-600" />}
									<h3 className="font-semibold text-gray-900">{option.label}</h3>
									{selectedPaymentType === option.type && (
										<CheckCircle className="w-4 h-4 text-emerald-600" />
									)}
								</div>
								<p className="text-sm text-gray-600 mb-3">{option.description}</p>

								{option.type === "deposit" && (
									<div className="text-xs text-gray-500 space-y-1">
										<div className="flex items-center gap-1">
											<Clock className="w-3 h-3" />
											<span>
												Solde restant: {formatAmount(depositCalculation.remainingAmount)}
											</span>
										</div>
									</div>
								)}
							</div>

							<div className="text-right">
								<div className="text-lg font-bold text-gray-900">{formatAmount(option.amount)}</div>
								{option.type === "deposit" && (
									<div className="text-xs text-gray-500">
										sur {formatAmount(depositCalculation.totalAmount)}
									</div>
								)}
							</div>
						</div>
					</div>
				))}
			</CardContent>
		</Card>
	);
}

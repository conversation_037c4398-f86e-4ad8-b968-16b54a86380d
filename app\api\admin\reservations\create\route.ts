import { NextRequest, NextResponse } from "next/server";
import { withAdminAuth } from "@/lib/admin-auth";
import { getSupabaseAdmin } from "@/lib/supabase";
import { reserveEquipmentForReservation } from "@/lib/availability";
import { calculateBookingPrice, validateBookingForCreation } from "@/lib/booking-validation";
import { generateBookingQRCode, generateVerificationUrl } from "@/lib/qr-code";
import { OptionSelection } from "@/lib/types/service-options";
import { getServiceOptions, calculateOptionsPricing } from "@/lib/service-options";
// import { logAdminAction } from "@/lib/admin-audit-log"; // TODO: Implement admin audit logging
import { sendBookingConfirmationEmail } from "@/lib/email-service";
import { generateBookingConfirmationPDF } from "@/lib/pdf-generator";

interface TierParticipant {
	tierId: string;
	count: number;
}

interface AdminReservationRequest {
	customerId: string;
	serviceId: string;
	date: string; // YYYY-MM-DD format
	timeSlotId: string;
	tierParticipants: TierParticipant[];
	selectedOptions?: OptionSelection[];
	specialRequests?: string;
	adminNotes?: string;
}

// POST /api/admin/reservations/create - Create reservation as admin
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const adminClient = getSupabaseAdmin();
		if (!adminClient) {
			return NextResponse.json({ error: "Database connection not available" }, { status: 500 });
		}

		const requestData: AdminReservationRequest = await request.json();
		const {
			customerId,
			serviceId,
			date,
			timeSlotId,
			tierParticipants,
			selectedOptions = [],
			specialRequests,
			adminNotes,
		} = requestData;

		console.log("=== ADMIN RESERVATION CREATION ===");
		console.log("Admin user:", user.email);
		console.log("Request data:", requestData);

		// Validate required fields
		if (!customerId || !serviceId || !date || !timeSlotId || !tierParticipants || tierParticipants.length === 0) {
			return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
		}

		// Validate customer exists
		const { data: customer, error: customerError } = await adminClient
			.from("customers")
			.select("*")
			.eq("id", customerId)
			.single();

		if (customerError || !customer) {
			return NextResponse.json({ error: "Customer not found" }, { status: 404 });
		}

		// Validate service exists and get details
		const { data: service, error: serviceError } = await adminClient
			.from("services")
			.select("*")
			.eq("id", serviceId)
			.eq("is_active", true)
			.single();

		if (serviceError || !service) {
			return NextResponse.json({ error: "Service not found or inactive" }, { status: 404 });
		}

		// Calculate total participants
		const totalParticipants = tierParticipants.reduce((sum, tp) => sum + tp.count, 0);

		if (totalParticipants === 0) {
			return NextResponse.json({ error: "At least one participant is required" }, { status: 400 });
		}

		if (totalParticipants > service.max_participants) {
			return NextResponse.json(
				{ error: `Maximum ${service.max_participants} participants allowed for this service` },
				{ status: 400 }
			);
		}

		// Parse and validate time slot
		const timeSlotParts = timeSlotId.split("_");
		if (timeSlotParts.length !== 2) {
			return NextResponse.json({ error: "Invalid time slot format" }, { status: 400 });
		}

		const time = timeSlotParts[1];
		if (!/^\d{2}:\d{2}$/.test(time)) {
			return NextResponse.json({ error: "Invalid time format" }, { status: 400 });
		}

		// Create timezone-safe datetime strings
		const startTimeISO = `${date}T${time}:00.000Z`;
		const startTime = new Date(startTimeISO);
		const endTime = new Date(startTime);
		endTime.setMinutes(endTime.getMinutes() + service.duration_minutes);
		const endTimeISO = endTime.toISOString();

		console.log("Calculated times:", { startTimeISO, endTimeISO });

		// Validate booking availability using existing validation logic
		const bookingData = {
			serviceId,
			timeSlotId,
			tierParticipants,
			customerInfo: {
				firstName: customer.first_name || "",
				lastName: customer.last_name || "",
				email: customer.email || "",
				phone: customer.phone || "",
			},
			selectedOptions,
			specialRequests,
		};

		const validation = await validateBookingForCreation(bookingData);
		if (!validation.isValid) {
			return NextResponse.json(
				{
					error: "Booking validation failed",
					details: validation.errors,
					warnings: validation.warnings,
				},
				{ status: 400 }
			);
		}

		// Calculate pricing
		const priceCalculation = await calculateBookingPrice(serviceId, tierParticipants, selectedOptions);

		console.log("Price calculation:", priceCalculation);

		// Generate reservation number
		const reservationNumber = `RES-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

		// Create reservation
		const { data: reservation, error: reservationError } = await adminClient
			.from("reservations")
			.insert({
				reservation_number: reservationNumber,
				customer_id: customerId,
				service_id: serviceId,
				start_time: startTimeISO,
				end_time: endTimeISO,
				participant_count: totalParticipants,
				tier_participants: tierParticipants,
				total_amount: priceCalculation.total,
				deposit_amount: Math.round(priceCalculation.total * 0.3 * 100) / 100, // 30% deposit
				remaining_amount: Math.round(priceCalculation.total * 0.7 * 100) / 100,
				status: "confirmed", // Admin reservations are confirmed immediately
				special_requests: specialRequests || null,
				admin_notes: adminNotes
					? `[CRÉÉ PAR ADMIN: ${user.email}] ${adminNotes}`
					: `[CRÉÉ PAR ADMIN: ${user.email}]`,
				// Admin tracking fields
				created_by_admin: true,
				created_by_admin_id: user.id,
			})
			.select()
			.single();

		if (reservationError) {
			console.error("Error creating reservation:", reservationError);
			return NextResponse.json({ error: "Failed to create reservation" }, { status: 500 });
		}

		// Save selected options if any
		if (selectedOptions && selectedOptions.length > 0) {
			try {
				const { error: optionsError } = await adminClient
					.from("reservations")
					.update({ selected_options: selectedOptions })
					.eq("id", reservation.id);

				if (optionsError) {
					console.error("Error saving options:", optionsError);
					// Don't fail the reservation for option errors
				}
			} catch (error) {
				console.error("Error saving option selections:", error);
			}
		}

		// Reserve equipment
		console.log("Reserving equipment for reservation:", reservation.id);
		const equipmentReserved = await reserveEquipmentForReservation(
			serviceId,
			reservation.id,
			totalParticipants,
			startTimeISO,
			endTimeISO
		);

		if (!equipmentReserved) {
			console.warn("Equipment reservation failed, but continuing with reservation creation");
		}

		// Generate QR code
		let qrCodeDataURL = null;
		try {
			const verificationUrl = generateVerificationUrl(reservation.id, "https://soleil-et-decouverte.com");
			const qrData = {
				reservationId: reservation.id,
				reservationNumber: reservationNumber,
				customerName: `${customer.first_name || ""} ${customer.last_name || ""}`.trim(),
				serviceName: service.name,
				date: new Date(startTimeISO).toLocaleDateString("fr-FR"),
				time: new Date(startTimeISO).toLocaleTimeString("fr-FR", {
					hour: "2-digit",
					minute: "2-digit",
					timeZone: "UTC",
				}),
				participants: totalParticipants,
				totalAmount: priceCalculation.total,
				verificationUrl: verificationUrl,
			};
			qrCodeDataURL = await generateBookingQRCode(qrData);
		} catch (error) {
			console.error("Error generating QR code:", error);
			// Continue without QR code
		}

		// Update reservation with QR code if generated
		if (qrCodeDataURL) {
			await adminClient.from("reservations").update({ qr_code: qrCodeDataURL }).eq("id", reservation.id);
		}

		// TODO: Log admin action
		// await logAdminAction(
		//   user.id,
		//   "CREATE",
		//   "reservations",
		//   reservation.id,
		//   null,
		//   {
		//     reservation_number: reservationNumber,
		//     customer_id: customerId,
		//     service_id: serviceId,
		//     total_amount: priceCalculation.total,
		//     created_by_admin: true,
		//   },
		//   request
		// );

		// Send confirmation email to customer
		console.log("Sending confirmation email to customer:", customer.email);
		try {
			const customerName = `${customer.first_name || ""} ${customer.last_name || ""}`.trim();
			const serviceName = service.name;
			const date = new Date(startTimeISO).toLocaleDateString("fr-FR");
			const time = new Date(startTimeISO).toLocaleTimeString("fr-FR", {
				hour: "2-digit",
				minute: "2-digit",
				timeZone: "UTC",
			});

			// Prepare booking data for email
			const bookingData = {
				reservationNumber: reservationNumber,
				customerName: customerName,
				customerEmail: customer.email || "",
				customerPhone: customer.phone || "",
				serviceName: serviceName,
				date: date,
				time: time,
				participants: totalParticipants,
				totalAmount: priceCalculation.total,
				selectedOptions:
					selectedOptions?.map((option) => ({
						name: option.optionId || "Option", // Use optionId as fallback for name
						price: (option as any).totalPrice || 0,
						quantity: option.quantity || 1,
						perParticipant: false, // Admin reservations show total price
					})) || [],
				specialRequests: specialRequests,
				adminCreated: true, // Flag to indicate this was created by admin
				// Security deposit information
				securityDepositRequired: service.requires_security_deposit || false,
				securityDepositAmount: service.security_deposit_amount || 0,
				securityDepositStatus: "manual", // Admin reservations default to manual deposit
				adminEmail: user.email,
				qrCodeData: qrCodeDataURL,
			};

			// Generate PDF attachment
			let pdfBuffer: Buffer | undefined;
			try {
				// For now, skip PDF generation due to type issues
				// const pdfBlob = await generateBookingConfirmationPDF(bookingData);
				// pdfBuffer = Buffer.from(await pdfBlob.arrayBuffer());
				pdfBuffer = undefined;
			} catch (pdfError) {
				console.error("Error generating PDF for admin reservation:", pdfError);
				// Continue without PDF
			}

			// Send email
			const emailResult = await sendBookingConfirmationEmail(
				customer.email || "",
				customerName,
				bookingData,
				pdfBuffer
			);

			if (emailResult.success) {
				console.log("Confirmation email sent successfully:", emailResult.messageId);
			} else {
				console.error("Failed to send confirmation email:", emailResult.error);
			}
		} catch (emailError) {
			console.error("Error sending confirmation email for admin reservation:", emailError);
			// Don't fail the reservation creation for email errors
		}

		console.log("=== ADMIN RESERVATION CREATION SUCCESS ===");
		console.log("Reservation ID:", reservation.id);

		return NextResponse.json({
			success: true,
			reservationId: reservation.id,
			reservationNumber: reservationNumber,
			totalAmount: priceCalculation.total,
			message: "Réservation créée avec succès",
		});
	} catch (error) {
		console.error("Error creating admin reservation:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "reservations:write");

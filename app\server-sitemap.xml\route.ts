import { supabase } from "@/lib/supabase";
import { generateSlug } from "@/lib/utils/slug";
import { NextResponse } from "next/server";

export async function GET() {
	try {
		// Fetch all active services from the database
		const { data: services, error } = await supabase
			.from("services")
			.select("id, name, updated_at")
			.eq("is_active", true)
			.order("name");

		if (error) {
			console.error("Error fetching services for sitemap:", error);
			return new NextResponse(generateEmptySitemap(), {
				headers: { "Content-Type": "application/xml" },
			});
		}

		// Generate sitemap fields for services
		const serviceFields = (services || []).map((service) => {
			// Generate slug from service name
			const serviceSlug = generateSlug(service.name);

			return {
				loc: `https://www.soleiletdecouverte.com/services/${serviceSlug}`,
				lastmod: service.updated_at || new Date().toISOString(),
				changefreq: "monthly",
				priority: 0.8,
			};
		});

		// Generate XML sitemap
		const sitemap = generateSitemapXML(serviceFields);

		return new NextResponse(sitemap, {
			headers: {
				"Content-Type": "application/xml",
				"Cache-Control": "public, max-age=3600, s-maxage=3600",
			},
		});
	} catch (error) {
		console.error("Error generating server sitemap:", error);
		return new NextResponse(generateEmptySitemap(), {
			headers: { "Content-Type": "application/xml" },
		});
	}
}

function generateSitemapXML(urls: Array<{ loc: string; lastmod: string; changefreq: string; priority: number }>) {
	const urlEntries = urls
		.map(
			(url) => `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`
		)
		.join("\n");

	return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlEntries}
</urlset>`;
}

function generateEmptySitemap() {
	return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
</urlset>`;
}

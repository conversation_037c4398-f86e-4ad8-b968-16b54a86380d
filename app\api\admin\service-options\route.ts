import { NextRequest, NextResponse } from "next/server";
import { withAdminAuth } from "@/lib/admin-auth";
import { getSupabaseAdmin } from "@/lib/supabase";

// GET /api/admin/service-options - Get all service options or filter by IDs
export const GET = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const supabaseAdmin = getSupabaseAdmin();
		const { searchParams } = new URL(request.url);
		const idsParam = searchParams.get("ids");

		let query = supabaseAdmin.from("service_options").select("*").eq("is_active", true);

		// Filter by IDs if provided
		if (idsParam) {
			const ids = idsParam.split(",").filter((id) => id.trim());
			if (ids.length > 0) {
				query = query.in("id", ids);
			}
		}

		const { data: options, error } = await query.order("name");

		if (error) {
			console.error("Error fetching service options:", error);
			return NextResponse.json({ error: "Failed to fetch options" }, { status: 500 });
		}

		return NextResponse.json({
			options: options.map((option) => ({
				id: option.id,
				name: option.name,
				description: option.description,
				basePrice: option.base_price,
				quoteBased: option.quote_based,
				perParticipant: option.per_participant,
				isActive: option.is_active,
			})),
		});
	} catch (error) {
		console.error("Service options GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:read");

// POST /api/admin/service-options - Create new service option
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const supabaseAdmin = getSupabaseAdmin();
		const body = await request.json();
		const { name, description, basePrice, quoteBased, perParticipant } = body;

		if (!name || typeof basePrice !== "number") {
			return NextResponse.json({ error: "Name and base price are required" }, { status: 400 });
		}

		const { data: option, error } = await supabaseAdmin
			.from("service_options")
			.insert({
				name,
				description: description || null,
				base_price: basePrice,
				quote_based: quoteBased || false,
				per_participant: perParticipant || false,
				is_active: true,
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating service option:", error);
			return NextResponse.json({ error: "Failed to create option" }, { status: 500 });
		}

		return NextResponse.json({
			option: {
				id: option.id,
				name: option.name,
				description: option.description,
				basePrice: option.base_price,
				quoteBased: option.quote_based,
				perParticipant: option.per_participant,
				isActive: option.is_active,
			},
		});
	} catch (error) {
		console.error("Service options POST error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// PUT /api/admin/service-options - Update service option
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const supabaseAdmin = getSupabaseAdmin();
		const body = await request.json();
		const { id, name, description, basePrice, quoteBased, perParticipant, isActive } = body;

		if (!id) {
			return NextResponse.json({ error: "Option ID is required" }, { status: 400 });
		}

		const updateData: any = {};
		if (name !== undefined) updateData.name = name;
		if (description !== undefined) updateData.description = description;
		if (basePrice !== undefined) updateData.base_price = basePrice;
		if (quoteBased !== undefined) updateData.quote_based = quoteBased;
		if (perParticipant !== undefined) updateData.per_participant = perParticipant;
		if (isActive !== undefined) updateData.is_active = isActive;
		updateData.updated_at = new Date().toISOString();

		const { data: option, error } = await supabaseAdmin
			.from("service_options")
			.update(updateData)
			.eq("id", id)
			.select()
			.single();

		if (error) {
			console.error("Error updating service option:", error);
			return NextResponse.json({ error: "Failed to update option" }, { status: 500 });
		}

		return NextResponse.json({
			option: {
				id: option.id,
				name: option.name,
				description: option.description,
				basePrice: option.base_price,
				quoteBased: option.quote_based,
				perParticipant: option.per_participant,
				isActive: option.is_active,
			},
		});
	} catch (error) {
		console.error("Service options PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// DELETE /api/admin/service-options - Delete service option
export const DELETE = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const supabaseAdmin = getSupabaseAdmin();
		const { searchParams } = new URL(request.url);
		const id = searchParams.get("id");

		if (!id) {
			return NextResponse.json({ error: "Option ID is required" }, { status: 400 });
		}

		// For now, allow deletion since assignments table might not exist yet
		// In the future, this will check for active assignments
		// const { data: assignments, error: assignmentError } = await supabaseAdmin
		//   .from("service_option_assignments")
		//   .select("id")
		//   .eq("option_id", id)
		//   .eq("is_active", true);
		//
		// if (assignmentError) {
		//   console.error("Error checking option assignments:", assignmentError);
		//   return NextResponse.json({ error: "Failed to check option usage" }, { status: 500 });
		// }
		//
		// if (assignments && assignments.length > 0) {
		//   return NextResponse.json({ error: "Cannot delete option that is assigned to services" }, { status: 400 });
		// }

		// Soft delete the option
		const { error } = await supabaseAdmin
			.from("service_options")
			.update({ is_active: false, updated_at: new Date().toISOString() })
			.eq("id", id);

		if (error) {
			console.error("Error deleting service option:", error);
			return NextResponse.json({ error: "Failed to delete option" }, { status: 500 });
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Service options DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// Payment types for deposit system

export type PaymentType = 'full' | 'deposit' | 'remaining';

export interface PaymentData {
  id: string;
  reservation_id: string;
  payment_method: string;
  payment_intent_id: string | null;
  amount: number;
  currency: string;
  status: string;
  payment_date: string | null;
  failure_reason: string | null;
  created_at: string | null;
  updated_at: string | null;
  // Deposit-related fields
  payment_type: PaymentType | null;
  deposit_percentage: number | null;
  is_deposit: boolean | null;
}

export interface ReservationData {
  id: string;
  customer_id: string;
  reservation_number: string;
  participant_count: number;
  total_amount: number;
  currency: string;
  status: string;
  special_requests: string | null;
  discount_code: string | null;
  discount_amount: number;
  check_in_time: string | null;
  qr_code: string | null;
  created_at: string | null;
  updated_at: string | null;
  service_id: string;
  assigned_employee_id: string | null;
  start_time: string;
  end_time: string;
  booking_source: string | null;
  admin_notes: string | null;
  requires_confirmation: boolean | null;
  confirmed_at: string | null;
  confirmed_by: string | null;
  // Deposit-related fields
  deposit_amount: number | null;
  remaining_amount: number | null;
  deposit_paid: boolean | null;
  deposit_payment_id: string | null;
}

export interface PaymentOption {
  type: PaymentType;
  label: string;
  description: string;
  amount: number;
  isRecommended?: boolean;
}

export interface DepositCalculation {
  totalAmount: number;
  depositAmount: number;
  remainingAmount: number;
  depositPercentage: number;
  paymentOptions: PaymentOption[];
}

export interface CreatePaymentIntentRequest {
  reservationId: string;
  paymentType: PaymentType;
  amount?: number; // Optional override for amount
}

export interface CreatePaymentIntentResponse {
  success: boolean;
  clientSecret?: string;
  paymentIntentId?: string;
  amount: number;
  currency: string;
  paymentType: PaymentType;
  error?: string;
}

export interface PaymentReceiptData {
  paymentId: string;
  reservationId: string;
  reservationNumber: string;
  customerName: string;
  customerEmail: string;
  serviceName: string;
  serviceDate: string;
  serviceTime: string;
  participants: number;
  amount: number;
  currency: string;
  paymentDate: string;
  paymentMethod: string;
  paymentIntentId: string;
  // Deposit-specific fields
  paymentType: PaymentType;
  depositPercentage?: number;
  totalAmount?: number;
  remainingAmount?: number;
  isDepositPayment: boolean;
}

// Helper functions for payment types
export const getPaymentTypeLabel = (type: PaymentType): string => {
  switch (type) {
    case 'full':
      return 'Paiement intégral';
    case 'deposit':
      return 'Acompte';
    case 'remaining':
      return 'Solde restant';
    default:
      return 'Paiement';
  }
};

export const getPaymentTypeDescription = (type: PaymentType, percentage?: number): string => {
  switch (type) {
    case 'full':
      return 'Payez le montant total maintenant';
    case 'deposit':
      return `Payez ${percentage || 20}% maintenant, le reste plus tard`;
    case 'remaining':
      return 'Payez le solde restant de votre réservation';
    default:
      return '';
  }
};

export const isDepositPayment = (paymentType: PaymentType | null): boolean => {
  return paymentType === 'deposit';
};

export const isFullPayment = (paymentType: PaymentType | null): boolean => {
  return paymentType === 'full';
};

export const isRemainingPayment = (paymentType: PaymentType | null): boolean => {
  return paymentType === 'remaining';
};

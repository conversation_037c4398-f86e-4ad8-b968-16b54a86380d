import { existsSync } from "fs";
import { mkdir, writeFile } from "fs/promises";
import { NextRequest, NextResponse } from "next/server";
import { join } from "path";

export async function POST(request: NextRequest) {
	try {
		const formData = await request.formData();
		const file = formData.get("file") as File;

		if (!file) {
			return NextResponse.json({ error: "Aucun fichier fourni" }, { status: 400 });
		}

		// Validate file type
		if (!file.type.startsWith("image/")) {
			return NextResponse.json({ error: "Le fichier doit être une image" }, { status: 400 });
		}

		// Validate file size (5MB max)
		if (file.size > 5 * 1024 * 1024) {
			return NextResponse.json({ error: "La taille du fichier ne doit pas dépasser 5MB" }, { status: 400 });
		}

		// Generate unique filename
		const timestamp = Date.now();
		const randomString = Math.random().toString(36).substring(2, 15);
		const fileExtension = file.name.split(".").pop();
		const fileName = `service-${timestamp}-${randomString}.${fileExtension}`;

		// Convert file to buffer
		const bytes = await file.arrayBuffer();
		const buffer = Buffer.from(bytes);

		// Save to public/uploads directory
		const uploadDir = join(process.cwd(), "public", "uploads");

		// Create uploads directory if it doesn't exist
		if (!existsSync(uploadDir)) {
			try {
				await mkdir(uploadDir, { recursive: true });
			} catch (mkdirError) {
				console.error("Error creating upload directory:", mkdirError);
				return NextResponse.json({ error: "Erreur lors de la création du dossier d'upload" }, { status: 500 });
			}
		}

		const filePath = join(uploadDir, fileName);

		try {
			await writeFile(filePath, buffer);
		} catch (writeError) {
			console.error("Error writing file:", writeError);
			return NextResponse.json({ error: "Erreur lors de la sauvegarde du fichier" }, { status: 500 });
		}

		// Return public URL
		const publicUrl = `/uploads/${fileName}`;

		return NextResponse.json({
			url: publicUrl,
			fileName: fileName,
		});
	} catch (error) {
		console.error("Upload error:", error);
		return NextResponse.json({ error: "Erreur serveur" }, { status: 500 });
	}
}

"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import Footer from "@/components/Footer";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronLeft, ChevronRight, Download, Heart, MapPin, Share2, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

const galleryImages = [
	{
		id: 1,
		src: "/images/waterbike_petit_canal_gallery.png",
		service: "WaterBikes",
		tags: ["#waterbike", "#petit-canal", "#eco-responsable"],
	},
	{
		id: 2,
		src: "/images/pelican_encounter_gallery.png",
		service: "Pélicans",
		tags: ["#pelican", "#îlet-sauvage", "#nature"],
	},
	{
		id: 3,
		src: "/images/cultural_tour_gallery.png",
		service: "Culturel",
		tags: ["#histoire", "#patrimoine", "#guadeloupe"],
	},
	{
		id: 4,
		src: "/images/mangrove_exploration_gallery.png",
		service: "Nature",
		tags: ["#mangrove", "#biodiversité", "#écosystème"],
	},
	{
		id: 5,
		src: "/images/local_products_gallery.png",
		service: "Gastronomie",
		tags: ["#dégustation", "#produits-locaux", "#saveurs"],
	},
	{
		id: 6,
		src: "/images/petit_canal_port_gallery.png",
		service: "Paysages",
		tags: ["#petit-canal", "#port-pêche", "#authentique"],
	},
	{
		id: 7,
		src: "/images/eco_exploration_gallery.png",
		service: "Nature",
		tags: ["#éco-responsable", "#préservation", "#environnement"],
	},
	{
		id: 8,
		src: "/images/sunset_family_adventure_service.png",
		service: "Culturel",
		tags: ["#chasse-trésor", "#famille", "#ludique"],
	},
	{
		id: 9,
		src: "/images/pelican_encounter_service.png",
		service: "Nature",
		tags: ["#observation", "#faune", "#jumelles"],
	},
	{
		id: 10,
		src: "/images/cultural_tour_service.png",
		service: "Culturel",
		tags: ["#patrimoine", "#histoire", "#mémoire"],
	},
	{
		id: 11,
		src: "/images/founders_story_gallery.png",
		service: "Souvenirs",
		tags: ["#fondateurs", "#petit-canal", "#passion"],
	},
];

const services = ["Tous", "WaterBikes", "Pélicans", "Culturel", "Nature", "Gastronomie", "Paysages", "Souvenirs"];

export default function GalleryPage() {
	const [selectedImage, setSelectedImage] = useState<(typeof galleryImages)[0] | null>(null);
	const [favorites, setFavorites] = useState<number[]>([]);
	const [currentImageIndex, setCurrentImageIndex] = useState(0);
	const [selectedService, setSelectedService] = useState("Tous");

	const filteredImages = galleryImages.filter((image) => {
		return selectedService === "Tous" || image.service === selectedService;
	});

	const toggleFavorite = (imageId: number) => {
		setFavorites((prev) => (prev.includes(imageId) ? prev.filter((id) => id !== imageId) : [...prev, imageId]));
	};

	const openLightbox = (image: (typeof galleryImages)[0]) => {
		setSelectedImage(image);
		setCurrentImageIndex(filteredImages.findIndex((img) => img.id === image.id));
	};

	const navigateImage = (direction: "prev" | "next") => {
		if (!selectedImage) return;

		const newIndex =
			direction === "next"
				? (currentImageIndex + 1) % filteredImages.length
				: (currentImageIndex - 1 + filteredImages.length) % filteredImages.length;

		setCurrentImageIndex(newIndex);
		setSelectedImage(filteredImages[newIndex]);
	};

	const handleDownload = () => {
		if (selectedImage) {
			console.log("Téléchargement de l'image:", selectedImage.service);
			// Simulate download
		}
	};

	const handleShare = () => {
		if (selectedImage) {
			console.log("Partage de l'image:", selectedImage.service);
			// Simulate share
		}
	};

	return (
		<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50">
			{/* Header */}
			<header className="bg-white/90 backdrop-blur-md shadow-sm sticky top-0 z-40">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center justify-between">
						<Link href="/" className="flex items-center space-x-3">
							<Image
								src="/images/logo-hd.png"
								alt="Soleil & Découverte"
								width={60}
								height={60}
								className="object-contain"
							/>
							<div>
								<h1 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-yellow-500 bg-clip-text text-transparent">
									Soleil & Découverte
								</h1>
								<p className="text-sm text-emerald-600">L'aventure au cœur de la Guadeloupe</p>
							</div>
						</Link>

						<nav className="hidden md:flex items-center space-x-8">
							<Link
								href="/"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Accueil
							</Link>
							<Link
								href="/services"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Services
							</Link>
							<Link
								href="/about"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								À propos
							</Link>
							<Link href="/gallery" className="text-emerald-600 font-semibold">
								Galerie
							</Link>
							<Link
								href="/contact"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Contact
							</Link>
							<Link href="/reservation">
								<Button className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white px-6 py-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-300">
									Réserver
								</Button>
							</Link>
						</nav>
					</div>
				</div>
			</header>

			{/* Hero Section */}
			<section className="relative py-20 overflow-hidden">
				<Image
					src="/images/gallery_hero.png"
					alt="Galerie"
					fill
					className="object-cover"
					style={{ zIndex: 1 }}
				/>
				<div className="absolute inset-0 bg-black/20" style={{ zIndex: 2 }}></div>

				<div className="container mx-auto px-4 relative z-10">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center max-w-4xl mx-auto"
					>
						<Badge className="mb-6 bg-white/20 text-white border-white/30 backdrop-blur-sm px-4 py-2 text-lg">
							📸 Galerie Photo
						</Badge>
						<h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
							Revivez Nos Plus Belles
							<span className="block bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
								Aventures
							</span>
						</h1>
						<p className="text-xl text-white/90 max-w-3xl mx-auto">
							Découvrez la Guadeloupe à travers les yeux de nos aventuriers. Chaque image raconte une
							histoire, chaque moment capture la magie de nos excursions.
						</p>
					</motion.div>
				</div>
			</section>

			{/* Minimal Filter */}
			<section className="py-6 bg-white/80 backdrop-blur-sm">
				<div className="container mx-auto px-4">
					<div className="flex justify-center">
						<Select value={selectedService} onValueChange={setSelectedService}>
							<SelectTrigger className="w-48 border-emerald-200 focus:border-emerald-500">
								<SelectValue placeholder="Filtrer par service" />
							</SelectTrigger>
							<SelectContent>
								{services.map((service) => (
									<SelectItem key={service} value={service}>
										{service}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				</div>
			</section>

			{/* Stylized Gallery Grid */}
			<section className="py-12">
				<div className="container mx-auto px-4">
					{filteredImages.length === 0 ? (
						<div className="text-center py-12">
							<p className="text-gray-500 text-lg mb-4">Aucune image ne correspond à ce service.</p>
							<Button
								onClick={() => setSelectedService("Tous")}
								className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white"
							>
								Voir toutes les images
							</Button>
						</div>
					) : (
						<div className="columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-6 space-y-6">
							{filteredImages.map((image, index) => (
								<motion.div
									key={image.id}
									initial={{ opacity: 0, y: 50 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.6, delay: index * 0.1 }}
									className="break-inside-avoid group cursor-pointer"
									onClick={() => openLightbox(image)}
								>
									<Card className="overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 border-0 bg-white">
										<div className="relative overflow-hidden">
											<Image
												src={image.src || "/placeholder.svg"}
												alt={`Image ${image.id}`}
												width={600}
												height={400}
												className="w-full h-auto object-cover group-hover:scale-105 transition-transform duration-500"
											/>

											{/* Only hashtags in bottom right */}
											<div className="absolute bottom-3 right-3">
												<div className="flex flex-wrap gap-1 justify-end">
													{image.tags.slice(0, 2).map((tag, idx) => (
														<Badge
															key={idx}
															className="bg-black/70 text-white text-xs px-2 py-1 backdrop-blur-sm border-0"
														>
															{tag}
														</Badge>
													))}
												</div>
											</div>

											{/* Favorite button - top right */}
											<div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
												<button
													onClick={(e) => {
														e.stopPropagation();
														toggleFavorite(image.id);
													}}
													className="p-2 bg-white/90 rounded-full hover:bg-white transition-colors shadow-lg"
												>
													<Heart
														className={`w-4 h-4 ${
															favorites.includes(image.id)
																? "text-red-500 fill-current"
																: "text-gray-600"
														}`}
													/>
												</button>
											</div>
										</div>
									</Card>
								</motion.div>
							))}
						</div>
					)}
				</div>
			</section>

			{/* Lightbox Modal */}
			<AnimatePresence>
				{selectedImage && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						exit={{ opacity: 0 }}
						className="fixed inset-0 bg-black/90 flex items-center justify-center p-4 z-50"
						onClick={() => setSelectedImage(null)}
					>
						<motion.div
							initial={{ scale: 0.9, opacity: 0 }}
							animate={{ scale: 1, opacity: 1 }}
							exit={{ scale: 0.9, opacity: 0 }}
							className="relative max-w-6xl w-full max-h-[90vh] bg-white rounded-2xl overflow-hidden"
							onClick={(e) => e.stopPropagation()}
						>
							{/* Close button */}
							<button
								onClick={() => setSelectedImage(null)}
								className="absolute top-4 right-4 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors"
							>
								<X className="w-6 h-6" />
							</button>

							{/* Navigation buttons */}
							<button
								onClick={() => navigateImage("prev")}
								className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full p-3 transition-colors"
							>
								<ChevronLeft className="w-6 h-6" />
							</button>

							<button
								onClick={() => navigateImage("next")}
								className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full p-3 transition-colors"
							>
								<ChevronRight className="w-6 h-6" />
							</button>

							<div className="grid lg:grid-cols-3 h-full">
								{/* Image */}
								<div className="lg:col-span-2 relative aspect-[4/3] lg:aspect-auto">
									<Image
										src={selectedImage.src || "/placeholder.svg"}
										alt={`Image ${selectedImage.id}`}
										fill
										className="object-cover"
									/>
								</div>

								{/* Update the lightbox details section */}
								<div className="p-6 lg:p-8 flex flex-col">
									<div className="flex items-start justify-between mb-4">
										<Badge className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white">
											{selectedImage.service}
										</Badge>
										<div className="flex gap-2">
											<button
												onClick={handleShare}
												className="p-2 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
											>
												<Share2 className="w-4 h-4" />
											</button>
											<button
												onClick={handleDownload}
												className="p-2 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
											>
												<Download className="w-4 h-4" />
											</button>
											<button
												onClick={() => toggleFavorite(selectedImage.id)}
												className="p-2 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
											>
												<Heart
													className={`w-4 h-4 ${
														favorites.includes(selectedImage.id)
															? "text-red-500 fill-current"
															: "text-gray-600"
													}`}
												/>
											</button>
										</div>
									</div>

									<div className="mb-6">
										<h3 className="font-semibold text-gray-900 mb-2">Tags</h3>
										<div className="flex flex-wrap gap-2">
											{selectedImage.tags.map((tag, idx) => (
												<Badge
													key={idx}
													variant="outline"
													className="text-emerald-600 border-emerald-200"
												>
													{tag}
												</Badge>
											))}
										</div>
									</div>

									<div className="mt-auto">
										<div className="text-sm text-gray-500 mb-4">
											Image {currentImageIndex + 1} sur {filteredImages.length}
										</div>
										<Link href="/reservation">
											<Button className="w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white">
												Réserver cette Excursion
											</Button>
										</Link>
									</div>
								</div>
							</div>
						</motion.div>
					</motion.div>
				)}
			</AnimatePresence>

			{/* CTA Section */}
			<section className="py-20 bg-gradient-to-br from-emerald-600 to-teal-700 relative overflow-hidden">
				<div className="absolute inset-0 bg-black/20"></div>
				<Image
					src="/images/cta_background_gallery.png"
					alt="Adventure"
					fill
					className="object-cover"
					style={{ zIndex: 1 }}
				/>

				<div className="container mx-auto px-4 relative z-10">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center max-w-4xl mx-auto"
					>
						<h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
							Créez Vos Propres Souvenirs !
						</h2>
						<p className="text-xl text-white/90 mb-8">
							Rejoignez-nous pour vivre des aventures inoubliables et repartez avec des souvenirs qui
							dureront toute une vie.
						</p>
						<div className="flex flex-col sm:flex-row gap-4 justify-center">
							<Link href="/reservation">
								<Button
									size="lg"
									className="bg-white text-emerald-600 hover:bg-gray-100 px-8 py-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 text-lg font-semibold"
								>
									Découvrir Nos Excursions
								</Button>
							</Link>
							<Link href="/contact">
								<Button
									size="lg"
									variant="outline"
									className="border-2 border-white text-white hover:bg-white hover:text-emerald-600 px-8 py-4 rounded-full backdrop-blur-sm bg-white/10 transition-all duration-300 text-lg font-semibold"
								>
									Nous Contacter
								</Button>
							</Link>
						</div>
					</motion.div>
				</div>
			</section>

			<Footer />
		</div>
	);
}

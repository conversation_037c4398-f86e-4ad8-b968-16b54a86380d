"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Plus, Trash2, Save, X, Edit } from "lucide-react";
import { ServiceOption, ServiceOptionAssignment, RequiredOptionGroup } from "@/lib/types/service-options";
import { adminApi } from "@/lib/api-client";
import Link from "next/link";

interface ServiceOptionsManagerProps {
	serviceId: string;
	assignments: ServiceOptionAssignment[];
	onAssignmentsChange: (assignments: ServiceOptionAssignment[]) => void;
	requiredGroups?: RequiredOptionGroup[];
	onRequiredGroupsChange?: (groups: RequiredOptionGroup[]) => void;
}

interface OptionFormData {
	name: string;
	description: string;
	basePrice: number;
	quoteBased: boolean;
	perParticipant: boolean;
}

export default function ServiceOptionsManager({
	serviceId,
	assignments,
	onAssignmentsChange,
	requiredGroups = [],
	onRequiredGroupsChange,
}: ServiceOptionsManagerProps) {
	const [availableOptions, setAvailableOptions] = useState<ServiceOption[]>([]);
	const [editingAssignment, setEditingAssignment] = useState<string | null>(null);

	// Required groups state
	const [isCreatingGroup, setIsCreatingGroup] = useState(false);
	const [editingGroup, setEditingGroup] = useState<string | null>(null);
	const [newGroup, setNewGroup] = useState({
		name: "",
		description: "",
		minRequired: 1,
		maxRequired: undefined as number | undefined,
		selectedOptions: [] as string[],
	});

	// Load available options and groups
	useEffect(() => {
		loadAvailableOptions();
	}, []);

	const loadAvailableOptions = async () => {
		try {
			const data = await adminApi.getServiceOptions();
			setAvailableOptions(data.options || []);
		} catch (error) {
			console.error("Error loading options:", error);
		}
	};

	// Option management moved to dedicated page

	const createRequiredGroup = () => {
		if (!onRequiredGroupsChange || newGroup.selectedOptions.length === 0) return;

		const group: RequiredOptionGroup = {
			id: `group-${Date.now()}`,
			name: newGroup.name,
			description: newGroup.description || undefined,
			minRequired: newGroup.minRequired,
			maxRequired: newGroup.maxRequired,
			optionIds: newGroup.selectedOptions,
		};

		onRequiredGroupsChange([...requiredGroups, group]);
		resetGroupForm();
		setIsCreatingGroup(false);
	};

	const updateRequiredGroup = () => {
		if (!onRequiredGroupsChange || !editingGroup || newGroup.selectedOptions.length === 0) return;

		const updatedGroups = requiredGroups.map((group) =>
			group.id === editingGroup
				? {
						...group,
						name: newGroup.name,
						description: newGroup.description || undefined,
						minRequired: newGroup.minRequired,
						maxRequired: newGroup.maxRequired,
						optionIds: newGroup.selectedOptions,
					}
				: group
		);

		onRequiredGroupsChange(updatedGroups);
		resetGroupForm();
		setEditingGroup(null);
	};

	const startEditingGroup = (group: RequiredOptionGroup) => {
		setNewGroup({
			name: group.name,
			description: group.description || "",
			minRequired: group.minRequired,
			maxRequired: group.maxRequired,
			selectedOptions: group.optionIds,
		});
		setEditingGroup(group.id);
		setIsCreatingGroup(false);
	};

	const resetGroupForm = () => {
		setNewGroup({
			name: "",
			description: "",
			minRequired: 1,
			maxRequired: undefined,
			selectedOptions: [],
		});
	};

	const deleteRequiredGroup = (groupId: string) => {
		if (!onRequiredGroupsChange) return;
		if (!confirm("Êtes-vous sûr de vouloir supprimer ce groupe requis ?")) return;

		onRequiredGroupsChange(requiredGroups.filter((g) => g.id !== groupId));
	};

	const addOptionAssignment = (optionId: string) => {
		const option = availableOptions.find((o) => o.id === optionId);
		if (!option) return;

		const newAssignment: ServiceOptionAssignment = {
			id: `temp_${Date.now()}`,
			type: "individual",
			option,
			isRequired: false,
			sortOrder: assignments.length,
		};

		onAssignmentsChange([...assignments, newAssignment]);
	};

	// Group assignments removed

	const removeAssignment = (assignmentId: string) => {
		onAssignmentsChange(assignments.filter((a) => a.id !== assignmentId));
	};

	const updateAssignment = (assignmentId: string, updates: Partial<ServiceOptionAssignment>) => {
		onAssignmentsChange(assignments.map((a) => (a.id === assignmentId ? { ...a, ...updates } : a)));
	};

	// Selection type labels removed - no longer needed without groups

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>Options de l'activité</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{/* Current Assignments */}
					<div className="space-y-3">
						{assignments.map((assignment) => (
							<div
								key={assignment.id}
								className="flex items-center justify-between p-3 border rounded-lg"
							>
								<div className="flex-1">
									<div className="font-medium">{assignment.option.name}</div>
									<div className="text-sm text-gray-500">
										{assignment.option.customPrice || assignment.option.basePrice}€
										{assignment.option.perParticipant && " par participant"}
										{assignment.option.quoteBased && " (sur devis)"}
										{assignment.option.description && (
											<div className="text-xs text-gray-400 mt-1">
												{assignment.option.description}
											</div>
										)}
									</div>
								</div>

								<div className="flex items-center gap-2">
									<Switch
										checked={assignment.isRequired}
										onCheckedChange={(checked) =>
											updateAssignment(assignment.id, { isRequired: checked })
										}
									/>
									<Label className="text-sm">Requis</Label>
									<Button variant="ghost" size="sm" onClick={() => removeAssignment(assignment.id)}>
										<Trash2 className="h-4 w-4" />
									</Button>
								</div>
							</div>
						))}

						{assignments.length === 0 && (
							<div className="text-center py-8 text-gray-500">
								Aucune option assignée à cette activité
							</div>
						)}
					</div>

					{/* Manage Options Link */}
					<div className="flex justify-end mb-4">
						<Link href="/admin/service-options">
							<Button size="sm" variant="outline">
								<ExternalLink className="h-4 w-4 mr-2" />
								Gérer les options
							</Button>
						</Link>
					</div>

					{/* Add Options */}
					<div>
						<Label className="text-sm font-medium">Ajouter une option</Label>
						<Select onValueChange={addOptionAssignment}>
							<SelectTrigger>
								<SelectValue placeholder="Sélectionner une option" />
							</SelectTrigger>
							<SelectContent>
								{availableOptions
									.filter((option) => !assignments.some((a) => a.option?.id === option.id))
									.map((option) => (
										<SelectItem key={option.id} value={option.id}>
											{option.name} ({option.basePrice}€)
											{option.perParticipant && " par participant"}
											{option.quoteBased && " (sur devis)"}
										</SelectItem>
									))}
							</SelectContent>
						</Select>
					</div>

					{/* Required Groups Management */}
					{onRequiredGroupsChange && (
						<div className="border rounded-lg p-4 mt-6">
							<div className="flex items-center justify-between mb-4">
								<div>
									<h4 className="font-medium">Groupes d'options requis</h4>
									<p className="text-sm text-gray-500">
										Créez des règles pour obliger les clients à sélectionner au moins X options
										parmi un groupe
									</p>
								</div>
								{!isCreatingGroup && !editingGroup && (
									<Button onClick={() => setIsCreatingGroup(true)} size="sm" variant="outline">
										<Plus className="h-4 w-4 mr-2" />
										Nouveau groupe
									</Button>
								)}
							</div>

							{/* Existing Required Groups */}
							{requiredGroups.length > 0 && (
								<div className="space-y-3 mb-4">
									{requiredGroups.map((group) => (
										<div
											key={group.id}
											className="flex items-center justify-between p-3 border rounded-lg bg-amber-50"
										>
											<div className="flex-1">
												<div className="font-medium">{group.name}</div>
												<div className="text-sm text-gray-600">
													{group.maxRequired === 1 && group.minRequired === 1
														? "Exactement 1 option requise"
														: group.maxRequired
															? `Entre ${group.minRequired} et ${group.maxRequired} option(s) requise(s)`
															: `Au moins ${group.minRequired} option(s) requise(s)`}{" "}
													parmi {group.optionIds.length} options
												</div>
												{group.description && (
													<div className="text-xs text-gray-500 mt-1">
														{group.description}
													</div>
												)}
												<div className="text-xs text-gray-400 mt-1">
													Options:{" "}
													{group.optionIds
														.map((id) => {
															const assignment = assignments.find(
																(a) => a.option?.id === id
															);
															return assignment?.option?.name;
														})
														.filter(Boolean)
														.join(", ")}
												</div>
											</div>
											<div className="flex gap-2">
												<Button
													variant="ghost"
													size="sm"
													onClick={() => startEditingGroup(group)}
													className="text-blue-600 hover:text-blue-700"
												>
													<Edit className="h-4 w-4" />
												</Button>
												<Button
													variant="ghost"
													size="sm"
													onClick={() => deleteRequiredGroup(group.id)}
													className="text-red-600 hover:text-red-700"
												>
													<Trash2 className="h-4 w-4" />
												</Button>
											</div>
										</div>
									))}
								</div>
							)}

							{/* Create/Edit Required Group */}
							{(isCreatingGroup || editingGroup) && (
								<div className="border rounded-lg p-4 bg-amber-50">
									<h5 className="font-medium mb-3">
										{editingGroup ? "Modifier le groupe requis" : "Créer un groupe requis"}
									</h5>
									<div className="space-y-3">
										<div className="grid grid-cols-3 gap-3">
											<div>
												<Label>Nom du groupe</Label>
												<Input
													value={newGroup.name}
													onChange={(e) => setNewGroup({ ...newGroup, name: e.target.value })}
													placeholder="Ex: Équipement obligatoire"
												/>
											</div>
											<div>
												<Label>Minimum requis</Label>
												<Input
													type="number"
													min="1"
													value={newGroup.minRequired}
													onChange={(e) =>
														setNewGroup({
															...newGroup,
															minRequired: Number(e.target.value),
														})
													}
												/>
											</div>
											<div>
												<Label>Maximum autorisé (optionnel)</Label>
												<Input
													type="number"
													min="1"
													value={newGroup.maxRequired || ""}
													onChange={(e) =>
														setNewGroup({
															...newGroup,
															maxRequired: e.target.value
																? Number(e.target.value)
																: undefined,
														})
													}
													placeholder="Laissez vide pour illimité"
												/>
											</div>
										</div>
										<div>
											<Label>Description (optionnelle)</Label>
											<Input
												value={newGroup.description}
												onChange={(e) =>
													setNewGroup({ ...newGroup, description: e.target.value })
												}
												placeholder="Ex: Choisissez au moins un équipement de plongée"
											/>
										</div>
										<div>
											<Label>Options du groupe</Label>
											<p className="text-xs text-gray-500 mb-2">
												Sélectionnez parmi les options assignées à cette activité
											</p>
											<div className="space-y-2 max-h-40 overflow-y-auto border rounded p-2">
												{assignments.filter((a) => a.option).length === 0 ? (
													<p className="text-sm text-gray-500 text-center py-4">
														Aucune option assignée à cette activité. Assignez d'abord des
														options pour créer des groupes.
													</p>
												) : (
													assignments
														.filter((a) => a.option)
														.map((assignment) => {
															const option = assignment.option!;
															return (
																<div
																	key={option.id}
																	className="flex items-center space-x-2"
																>
																	<input
																		type="checkbox"
																		id={`group-option-${option.id}`}
																		checked={newGroup.selectedOptions.includes(
																			option.id
																		)}
																		onChange={(e) => {
																			if (e.target.checked) {
																				setNewGroup({
																					...newGroup,
																					selectedOptions: [
																						...newGroup.selectedOptions,
																						option.id,
																					],
																				});
																			} else {
																				setNewGroup({
																					...newGroup,
																					selectedOptions:
																						newGroup.selectedOptions.filter(
																							(id) => id !== option.id
																						),
																				});
																			}
																		}}
																		className="rounded"
																	/>
																	<label
																		htmlFor={`group-option-${option.id}`}
																		className="text-sm"
																	>
																		{option.name} ({option.basePrice}€)
																	</label>
																</div>
															);
														})
												)}
											</div>
										</div>
										<div className="flex gap-2">
											<Button
												onClick={editingGroup ? updateRequiredGroup : createRequiredGroup}
												disabled={!newGroup.name || newGroup.selectedOptions.length === 0}
											>
												<Save className="h-4 w-4 mr-2" />
												{editingGroup ? "Modifier le groupe" : "Créer le groupe"}
											</Button>
											<Button
												variant="outline"
												onClick={() => {
													setIsCreatingGroup(false);
													setEditingGroup(null);
													resetGroupForm();
												}}
											>
												<X className="h-4 w-4 mr-2" />
												Annuler
											</Button>
										</div>
									</div>
								</div>
							)}
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}

import { NextRequest, NextResponse } from "next/server";
import { withAdminAuth } from "@/lib/admin-auth";
import { getSupabaseAdmin } from "@/lib/supabase";

// GET /api/admin/reservations/[id]/security-deposit - Get security deposit status
export const GET = withAdminAuth(async (request: NextRequest, user, { params }) => {
	try {
		const reservationId = params?.id as string;

		if (!reservationId) {
			return NextResponse.json({ error: "Reservation ID is required" }, { status: 400 });
		}

		const supabaseAdmin = getSupabaseAdmin();

		// Get reservation with service info
		const { data: reservation, error: reservationError } = await supabaseAdmin
			.from("reservations")
			.select(
				`
				id,
				security_deposit_status,
				security_deposit_method,
				security_deposit_payment_intent_id,
				service:services (
					requires_security_deposit,
					security_deposit_amount
				)
			`
			)
			.eq("id", reservationId)
			.single();

		if (reservationError) {
			console.error("Error fetching reservation:", reservationError);
			return NextResponse.json({ error: "Reservation not found" }, { status: 404 });
		}

		// If service doesn't require security deposit, return null
		if (!reservation.service || !reservation.service.requires_security_deposit) {
			return NextResponse.json(null);
		}

		// Get security deposit transactions
		const { data: transactions, error: transactionsError } = await supabaseAdmin
			.from("security_deposit_transactions")
			.select("*")
			.eq("reservation_id", reservationId)
			.order("created_at", { ascending: false });

		if (transactionsError) {
			console.error("Error fetching security deposit transactions:", transactionsError);
			// Don't fail the request, just return empty transactions
		}

		const depositStatus = {
			status: reservation.security_deposit_status || "none",
			method: reservation.security_deposit_method,
			amount: reservation.service?.security_deposit_amount || 0,
			payment_intent_id: reservation.security_deposit_payment_intent_id,
			transactions: transactions || [],
		};

		return NextResponse.json(depositStatus);
	} catch (error) {
		console.error("Security deposit status error:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}, "reservations:read");

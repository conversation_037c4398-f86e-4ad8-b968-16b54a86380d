import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: NextRequest) {
	try {
		console.log("Adding admin notification email setting...");

		// First check if it already exists
		const { data: existing } = await supabase
			.from("business_settings")
			.select("*")
			.eq("key", "admin_notification_email")
			.single();

		if (existing) {
			return NextResponse.json({
				success: true,
				message: "Setting already exists",
				data: existing
			});
		}

		// Add the setting
		const { data, error } = await supabase
			.from("business_settings")
			.insert({
				key: "admin_notification_email",
				value: "<EMAIL>",
				value_type: "string",
				category: "notifications",
				description: "Email address for admin notifications (new reservations, payments, etc.)",
				is_public: false,
			})
			.select()
			.single();

		if (error) {
			console.error("Error adding setting:", error);
			return NextResponse.json({ 
				success: false, 
				error: error.message 
			}, { status: 500 });
		}

		console.log("✅ Admin notification email setting added successfully:", data);
		
		return NextResponse.json({
			success: true,
			message: "Admin notification email setting added successfully",
			data
		});

	} catch (error) {
		console.error("API error:", error);
		return NextResponse.json({
			success: false,
			error: error instanceof Error ? error.message : "Unknown error"
		}, { status: 500 });
	}
}

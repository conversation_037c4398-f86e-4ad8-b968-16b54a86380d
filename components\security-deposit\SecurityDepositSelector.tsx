"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, CreditCard, Shield, Store } from "lucide-react";
import { useState } from "react";

interface SecurityDepositSelectorProps {
	reservationId: string;
	securityDepositAmount: number;
	onMethodSelected: (method: "authorization" | "manual") => void;
	onComplete: () => void;
	disabled?: boolean;
}

export default function SecurityDepositSelector({
	reservationId,
	securityDepositAmount,
	onMethodSelected,
	onComplete,
	disabled = false,
}: SecurityDepositSelectorProps) {
	const [selectedMethod, setSelectedMethod] = useState<"authorization" | "manual" | null>(null);
	const [isProcessing, setIsProcessing] = useState(false);

	const handleMethodSelect = async (method: "authorization" | "manual") => {
		if (disabled || isProcessing) return;

		setSelectedMethod(method);
		setIsProcessing(true);

		try {
			if (method === "manual") {
				// For manual method, just update the reservation and proceed
				const response = await fetch("/api/reservations/update-security-deposit", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						reservationId,
						method: "manual",
					}),
				});

				if (response.ok) {
					onMethodSelected(method);
					onComplete();
				} else {
					console.error("Failed to update reservation for manual security deposit");
					setSelectedMethod(null);
				}
			} else {
				// For authorization method, proceed to authorization form
				onMethodSelected(method);
			}
		} catch (error) {
			console.error("Error selecting security deposit method:", error);
			setSelectedMethod(null);
		} finally {
			setIsProcessing(false);
		}
	};

	const formatAmount = (amount: number) => {
		return new Intl.NumberFormat("fr-FR", {
			style: "currency",
			currency: "EUR",
		}).format(amount);
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Shield className="w-5 h-5" />
					Caution d'équipement
				</CardTitle>
				<p className="text-sm text-gray-600">
					Comment souhaitez-vous gérer la caution de {formatAmount(securityDepositAmount)} ?
				</p>
			</CardHeader>
			<CardContent className="space-y-4">
				{/* Authorization Option */}
				<div
					className={`relative border rounded-lg p-4 cursor-pointer transition-all ${
						selectedMethod === "authorization"
							? "border-emerald-500 bg-emerald-50"
							: "border-gray-200 hover:border-gray-300"
					} ${disabled || isProcessing ? "opacity-50 cursor-not-allowed" : ""}`}
					onClick={() => !disabled && !isProcessing && handleMethodSelect("authorization")}
				>
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<div className="flex items-center gap-2 mb-2">
								<CreditCard className="w-4 h-4 text-emerald-600" />
								<h3 className="font-semibold text-gray-900">Sécuriser la caution maintenant</h3>
								{selectedMethod === "authorization" && (
									<CheckCircle className="w-4 h-4 text-emerald-600" />
								)}
							</div>
							<p className="text-sm text-gray-600 mb-3">
								Pré-autorisation sécurisée - Aucun prélèvement immédiat
							</p>
							<div className="text-xs text-gray-500 space-y-1">
								<p>✓ La pré-autorisation réserve le montant sur votre carte sans prélèvement</p>
								<p>✓ Libération automatique sous 5-7 jours si aucun dommage n'est constaté</p>
								<p>✓ Prélèvement uniquement en cas de dommage ou de non-respect des conditions</p>
							</div>
						</div>
						<div className="text-right">
							<div className="text-lg font-bold text-emerald-600">
								{formatAmount(securityDepositAmount)}
							</div>
							<div className="text-xs text-gray-500">Pré-autorisation</div>
						</div>
					</div>
				</div>

				{/* Manual Option */}
				<div
					className={`relative border rounded-lg p-4 cursor-pointer transition-all ${
						selectedMethod === "manual"
							? "border-blue-500 bg-blue-50"
							: "border-gray-200 hover:border-gray-300"
					} ${disabled || isProcessing ? "opacity-50 cursor-not-allowed" : ""}`}
					onClick={() => !disabled && !isProcessing && handleMethodSelect("manual")}
				>
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<div className="flex items-center gap-2 mb-2">
								<Store className="w-4 h-4 text-blue-600" />
								<h3 className="font-semibold text-gray-900">Gérer la caution à l'accueil</h3>
								{selectedMethod === "manual" && <CheckCircle className="w-4 h-4 text-blue-600" />}
							</div>
							<p className="text-sm text-gray-600 mb-3">
								Caution à régler lors de la prise en charge de l'équipement
							</p>
							<div className="text-xs text-gray-500 space-y-1">
								<p>✓ Paiement en espèce ou chèque directement à notre accueil</p>
								<p>✓ Restitution immédiate après vérification de l'équipement</p>
							</div>
						</div>
						<div className="text-right">
							<div className="text-lg font-bold text-blue-600">{formatAmount(securityDepositAmount)}</div>
							<div className="text-xs text-gray-500">À l'accueil</div>
						</div>
					</div>
				</div>

				{/* Information Box */}
				<div className="bg-gray-50 rounded-lg p-4">
					<h4 className="font-medium text-gray-900 mb-2">À propos de la caution d'équipement</h4>
					<p className="text-sm text-gray-600">
						Cette caution garantit la bonne utilisation de nos équipements. Elle couvre les éventuels
						dommages, pertes ou non-respect des conditions d'utilisation. En l'absence de problème, la
						caution vous sera intégralement restituée.
					</p>
				</div>

				{isProcessing && (
					<div className="text-center py-4">
						<div className="inline-flex items-center gap-2 text-gray-600">
							<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-emerald-600"></div>
							Traitement en cours...
						</div>
					</div>
				)}
			</CardContent>
		</Card>
	);
}

import { NextRequest, NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { getStripeServer } from "@/lib/stripe";
import { withAdminAuth } from "@/lib/admin-auth";

export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { reservationId } = await request.json();

		if (!reservationId) {
			return NextResponse.json({ error: "Reservation ID is required" }, { status: 400 });
		}

		const supabaseAdmin = getSupabaseAdmin();

		// Get reservation with security deposit info
		const { data: reservation, error: reservationError } = await supabaseAdmin
			.from("reservations")
			.select(
				`
				id,
				security_deposit_status,
				security_deposit_payment_intent_id,
				service:services (
					security_deposit_amount
				)
			`
			)
			.eq("id", reservationId)
			.single();

		if (reservationError || !reservation) {
			return NextResponse.json({ error: "Reservation not found" }, { status: 404 });
		}

		// Check if deposit is authorized or pending with payment intent (not charged)
		if (
			reservation.security_deposit_status !== "authorized" &&
			!(reservation.security_deposit_status === "pending" && reservation.security_deposit_payment_intent_id)
		) {
			return NextResponse.json(
				{ error: "Security deposit must be authorized (not charged) to release" },
				{ status: 400 }
			);
		}

		if (!reservation.security_deposit_payment_intent_id) {
			return NextResponse.json({ error: "No payment intent found for security deposit" }, { status: 400 });
		}

		// Release the security deposit using Stripe
		const stripe = getStripeServer();

		try {
			const paymentIntent = await stripe.paymentIntents.cancel(reservation.security_deposit_payment_intent_id);

			// Update reservation status
			const { error: updateError } = await supabaseAdmin
				.from("reservations")
				.update({
					security_deposit_status: "released",
					updated_at: new Date().toISOString(),
				})
				.eq("id", reservationId);

			if (updateError) {
				console.error("Error updating reservation status:", updateError);
				// Don't fail the request as the release was successful
			}

			// Record the release transaction
			const { error: transactionError } = await supabaseAdmin.from("security_deposit_transactions").insert({
				reservation_id: reservationId,
				transaction_type: "release",
				amount: (reservation.service as any)?.security_deposit_amount || 0,
				payment_intent_id: reservation.security_deposit_payment_intent_id,
				status: "completed",
				processor_response: {
					stripe_payment_intent_id: paymentIntent.id,
					stripe_status: paymentIntent.status,
				},
			});

			if (transactionError) {
				console.error("Error recording release transaction:", transactionError);
				// Don't fail the request as the release was successful
			}

			return NextResponse.json({
				success: true,
				message: "Security deposit released successfully",
				paymentIntent: {
					id: paymentIntent.id,
					status: paymentIntent.status,
					amount: paymentIntent.amount,
				},
			});
		} catch (stripeError: any) {
			console.error("Stripe release error:", stripeError);

			// Record the failed release attempt
			await supabaseAdmin.from("security_deposit_transactions").insert({
				reservation_id: reservationId,
				transaction_type: "release",
				amount: (reservation.service as any)?.security_deposit_amount || 0,
				payment_intent_id: reservation.security_deposit_payment_intent_id,
				status: "failed",
				processor_response: {
					stripe_error: stripeError.message,
					stripe_code: stripeError.code,
				},
			});

			return NextResponse.json(
				{
					error: "Failed to release security deposit",
					details: stripeError.message,
				},
				{ status: 400 }
			);
		}
	} catch (error) {
		console.error("Security deposit release error:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}, "reservations:write");

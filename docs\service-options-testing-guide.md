# Service Options System - Comprehensive Testing Guide

## Overview

This guide provides step-by-step instructions for thoroughly testing the new service options system from both admin and customer perspectives. The system has been migrated from JSON-based storage to a relational database structure with enhanced functionality.

## Prerequisites

- Admin access to the dashboard
- Test customer account
- Access to browser developer tools for debugging
- Understanding of the service options data structure

---

## 1. Admin Testing Workflow

### 1.1 Service Options Management

#### Test 1.1.1: Create Individual Service Options

**Objective:** Verify that individual service options can be created and managed.

**Steps:**

1. Navigate to Admin Dashboard → Service Options
2. Click "Create New Option"
3. Test creating options with different configurations:

    **Test Case A: Basic Option**
    - Name: "Équipement de plongée"
    - Description: "Masque, palmes et tuba inclus"
    - Base Price: 15.00
    - Quote Based: false
    - Per Participant: true
    - Click "Save"

    **Test Case B: Quote-Based Option**
    - Name: "Transport privé"
    - Description: "Transport depuis votre hôtel"
    - Base Price: 0.00
    - Quote Based: true
    - Per Participant: false
    - Click "Save"

    **Test Case C: Fixed Price Option**
    - Name: "Assurance annulation"
    - Description: "Couverture complète"
    - Base Price: 25.00
    - Quote Based: false
    - Per Participant: false
    - Click "Save"

**Expected Results:**

- Options are created successfully
- Options appear in the options list
- Pricing displays correctly ("sur devis" for quote-based, "€" for priced)
- Per-participant flag is saved correctly

#### Test 1.1.2: Edit Service Options

**Steps:**

1. From the options list, click "Edit" on an existing option
2. Modify the name, price, and settings
3. Save changes
4. Verify changes are reflected in the list

**Expected Results:**

- Changes are saved successfully
- Updated information displays correctly
- No data corruption occurs

#### Test 1.1.3: Delete Service Options

**Steps:**

1. Try to delete an option that is NOT assigned to any service
2. Try to delete an option that IS assigned to a service

**Expected Results:**

- Unassigned options can be deleted
- Assigned options show warning and prevent deletion
- Error messages are clear and helpful

### 1.2 Service Option Groups Management

#### Test 1.2.1: Create Option Groups

**Objective:** Test different group types and selection rules.

**Test Case A: Single Required Group**

```
Name: "Type de sortie"
Description: "Choisissez votre formule"
Selection Type: single_required
Min Selections: 1
Max Selections: 1
Options: ["Demi-journée", "Journée complète", "Coucher de soleil"]
```

**Test Case B: Multiple Optional Group**

```
Name: "Équipements optionnels"
Description: "Sélectionnez vos équipements"
Selection Type: multiple_optional
Min Selections: 0
Max Selections: 3
Options: ["Équipement de plongée", "Appareil photo étanche", "Serviette"]
```

**Test Case C: Multiple Required Group**

```
Name: "Repas inclus"
Description: "Minimum 1 repas requis"
Selection Type: multiple_required
Min Selections: 1
Max Selections: 2
Options: ["Petit-déjeuner", "Déjeuner", "Collation"]
```

**Steps:**

1. Navigate to Admin Dashboard → Option Groups
2. Click "Create New Group"
3. Fill in group details for each test case
4. Select options to include in the group
5. Save the group

**Expected Results:**

- Groups are created with correct selection rules
- Options are properly associated with groups
- Validation rules are enforced during creation

#### Test 1.2.2: Edit Option Groups

**Steps:**

1. Edit an existing group
2. Add/remove options from the group
3. Change selection type and limits
4. Save changes

**Expected Results:**

- Group modifications are saved correctly
- Option associations are updated
- Selection rules are properly updated

### 1.3 Service Option Assignments

#### Test 1.3.1: Assign Options to Services

**Objective:** Test assigning both individual options and groups to services.

**Steps:**

1. Navigate to Admin Dashboard → Services
2. Select a service to edit
3. Go to the "Options" tab
4. Test different assignment scenarios:

    **Scenario A: Individual Option Assignment**
    - Assign "Transport privé" as optional
    - Set custom price override (if different from base price)
    - Set sort order

    **Scenario B: Group Assignment**
    - Assign "Type de sortie" group as required
    - Assign "Équipements optionnels" group as optional
    - Set appropriate sort orders

    **Scenario C: Mixed Assignments**
    - Combine individual options and groups
    - Test different requirement levels
    - Verify sort order affects display order

5. Save assignments

**Expected Results:**

- Options and groups are assigned correctly
- Custom pricing overrides work
- Required/optional flags are respected
- Sort order determines display sequence

#### Test 1.3.2: Custom Pricing for Service Assignments

**Steps:**

1. Assign an option to a service
2. Set a custom price different from the base price
3. Save and verify the custom price is used for this service only

**Expected Results:**

- Custom prices override base prices for specific services
- Base prices remain unchanged for other services
- Pricing calculations use the correct price

### 1.4 Validation Testing

#### Test 1.4.1: Business Rule Validation

**Test Required Groups:**

1. Create a service with a required group
2. Try to save without selecting from the required group
3. Verify validation error appears

**Test Selection Limits:**

1. Create a group with max selections = 2
2. Try to select 3 options
3. Verify selection is limited to 2

**Test Minimum Selections:**

1. Create a group with min selections = 2
2. Try to select only 1 option
3. Verify validation error for insufficient selections

### 1.5 Migration Verification

#### Test 1.5.1: Legacy Data Preservation

**Steps:**

1. Check services that had options before migration
2. Verify all legacy options are now available as assignments
3. Check existing reservations still display their selected options correctly

**Expected Results:**

- No data loss during migration
- Legacy option types are correctly converted
- Existing reservations maintain their option selections

---

## 2. Customer/End-User Testing Workflow

### 2.1 Service Discovery and Option Display

#### Test 2.1.1: Service Listing with Options

**Steps:**

1. Navigate to the public services page
2. Browse services that have options assigned
3. Verify option information is displayed appropriately

**Expected Results:**

- Services with options show indication of available options
- Basic option information is visible
- "Starting from" pricing accounts for base options

#### Test 2.1.2: Service Detail Page

**Steps:**

1. Click on a service with assigned options
2. Verify the options section displays correctly
3. Check that different option types are clearly distinguished

**Expected Results:**

- All assigned options and groups are visible
- Required vs optional options are clearly marked
- Pricing information is accurate
- Quote-based options show "sur devis"

### 2.2 Booking Flow Testing

#### Test 2.2.1: Basic Option Selection

**Objective:** Test the complete booking flow with option selection.

**Steps:**

1. Start booking a service with options
2. Select date and time
3. Choose participant count
4. Navigate to options selection

**Test Scenarios:**

**Scenario A: Required Single Choice**

- Service has a required single-choice group
- Try to proceed without selection → Should show validation error
- Select one option → Should allow proceeding
- Try to select multiple → Should only allow one

**Scenario B: Optional Multiple Choice**

- Service has optional multiple-choice group
- Proceed without selections → Should be allowed
- Select multiple options within limit → Should work
- Try to exceed maximum → Should prevent selection

**Scenario C: Mixed Requirements**

- Service has both required and optional groups
- Test various combinations of selections
- Verify validation works correctly

### 2.2.2: Pricing Calculation Testing

**Objective:** Verify that option pricing is calculated correctly throughout the booking flow.

**Test Cases:**

**Test Case A: Per-Participant Options**

```
Setup:
- Service: "Excursion bateau" (50€/person)
- Option: "Équipement plongée" (15€/person, per_participant=true)
- Participants: 3

Expected Calculation:
- Base: 3 × 50€ = 150€
- Option: 3 × 15€ = 45€
- Total: 195€
```

**Test Case B: Fixed Price Options**

```
Setup:
- Service: "Excursion bateau" (50€/person)
- Option: "Assurance groupe" (25€ fixed, per_participant=false)
- Participants: 3

Expected Calculation:
- Base: 3 × 50€ = 150€
- Option: 1 × 25€ = 25€
- Total: 175€
```

**Test Case C: Quote-Based Options**

```
Setup:
- Service: "Excursion bateau" (50€/person)
- Option: "Transport privé" (quote-based)
- Participants: 2

Expected Behavior:
- Base: 2 × 50€ = 100€
- Option: Shows "sur devis" instead of price
- Total: 100€ + "options sur devis"
```

**Steps:**

1. For each test case, configure the service and options as specified
2. Go through the booking flow
3. Verify pricing at each step:
    - Option selection page
    - Booking summary
    - Payment page
4. Complete the booking
5. Verify final pricing in confirmation

### 2.2.3: Edge Cases Testing

#### Test *******: Quantity Selection

**Steps:**

1. Select an option that allows quantity selection
2. Test different quantities
3. Verify pricing updates correctly

#### Test *******: Dynamic Pricing Updates

**Steps:**

1. Start with a certain participant count
2. Select options
3. Change participant count
4. Verify per-participant option pricing updates automatically

#### Test *******: Option Dependencies

**Steps:**

1. If any options have dependencies, test the dependency logic
2. Verify dependent options appear/disappear correctly
3. Test validation of dependent selections

### 2.3 Reservation Management

#### Test 2.3.1: Reservation Display

**Steps:**

1. Complete a booking with various option selections
2. View the reservation in customer account
3. Check reservation details page

**Expected Results:**

- All selected options are displayed
- Pricing breakdown shows option costs
- Option details are clear and accurate

#### Test 2.3.2: Reservation Modifications

**Steps:**

1. Try to modify a reservation with options
2. Test changing option selections
3. Verify pricing updates correctly

**Expected Results:**

- Option modifications are handled correctly
- Price adjustments are calculated properly
- Changes are saved and displayed accurately

---

## 3. Integration Testing

### 3.1 API Testing

#### Test 3.1.1: Service Options API

**Endpoints to Test:**

```
GET /api/admin/service-options
POST /api/admin/service-options
PUT /api/admin/service-options
DELETE /api/admin/service-options

GET /api/admin/service-option-groups
POST /api/admin/service-option-groups
PUT /api/admin/service-option-groups
DELETE /api/admin/service-option-groups

GET /api/admin/services/[id]/option-assignments
PUT /api/admin/services/[id]/option-assignments
DELETE /api/admin/services/[id]/option-assignments
```

**Test Methods:**

1. Use browser developer tools or API testing tool
2. Test each endpoint with valid and invalid data
3. Verify response formats and error handling
4. Test authentication and authorization

#### Test 3.1.2: Booking API Integration

**Steps:**

1. Test the booking API with option selections
2. Verify option data is properly saved
3. Test pricing calculations via API
4. Verify reservation creation with options

### 3.2 Database Integrity Testing

#### Test 3.2.1: Data Consistency

**Steps:**

1. Create options, groups, and assignments via admin interface
2. Verify data integrity in database
3. Test cascading deletes and updates
4. Check foreign key constraints

#### Test 3.2.2: RLS (Row Level Security) Testing

**Steps:**

1. Test that admin users can access all option data
2. Test that regular users can only access appropriate data
3. Verify RLS policies are working correctly

---

## 4. Performance Testing

### 4.1 Load Testing

#### Test 4.1.1: Option Loading Performance

**Steps:**

1. Create a service with many option assignments (20+ options)
2. Load the service detail page
3. Measure loading time
4. Test with multiple concurrent users

#### Test 4.1.2: Booking Flow Performance

**Steps:**

1. Test booking flow with complex option selections
2. Measure response times for pricing calculations
3. Test with multiple simultaneous bookings

---

## 5. Browser Compatibility Testing

### 5.1 Cross-Browser Testing

**Browsers to Test:**

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

**Test Areas:**

1. Option selection interface
2. Pricing calculations
3. Form submissions
4. Admin interface functionality

### 5.2 Mobile Responsiveness

**Steps:**

1. Test booking flow on mobile devices
2. Verify option selection interface works on touch devices
3. Test admin interface on tablets

---

## 6. Error Handling and Recovery

### 6.1 Network Error Testing

**Steps:**

1. Simulate network interruptions during booking
2. Test offline behavior
3. Verify data recovery and error messages

### 6.2 Data Validation Testing

**Steps:**

1. Test with malformed option data
2. Verify graceful error handling
3. Test recovery from validation errors

---

## 7. Reporting and Documentation

### 7.1 Test Results Documentation

For each test, document:

- Test case ID
- Steps performed
- Expected results
- Actual results
- Pass/Fail status
- Screenshots of any issues
- Browser/device information

### 7.2 Issue Tracking

Create issues for any problems found:

- Clear description of the problem
- Steps to reproduce
- Expected vs actual behavior
- Priority level
- Screenshots or videos

---

## 8. Acceptance Criteria Checklist

### 8.1 Admin Functionality

- [ ] Can create, edit, and delete service options
- [ ] Can create, edit, and delete option groups
- [ ] Can assign options and groups to services
- [ ] Can set custom pricing for service assignments
- [ ] Can manage option requirements and sort order
- [ ] Validation prevents invalid configurations
- [ ] Migration preserved all existing data

### 8.2 Customer Functionality

- [ ] Options display correctly on service pages
- [ ] Option selection interface is intuitive
- [ ] Pricing calculations are accurate
- [ ] Validation prevents invalid selections
- [ ] Booking flow works with all option types
- [ ] Reservations display selected options correctly

### 8.3 Technical Requirements

- [ ] All API endpoints function correctly
- [ ] Database integrity is maintained
- [ ] RLS policies are enforced
- [ ] Performance is acceptable
- [ ] Cross-browser compatibility confirmed
- [ ] Mobile responsiveness verified

### 8.4 Business Requirements

- [ ] All legacy option types are supported
- [ ] Complex selection rules work correctly
- [ ] Pricing flexibility meets business needs
- [ ] Admin workflow is efficient
- [ ] Customer experience is improved

---

## 9. Post-Testing Actions

### 9.1 Production Deployment Checklist

- [ ] All tests passed
- [ ] Database migration completed successfully
- [ ] RLS policies are active
- [ ] Admin training completed
- [ ] Customer-facing documentation updated
- [ ] Monitoring and alerting configured

### 9.2 Rollback Plan

- [ ] Database backup verified
- [ ] Rollback procedure documented
- [ ] Emergency contacts identified
- [ ] Rollback testing completed

## Quick Start Testing Checklist

### Priority 1: Critical Path Testing (30 minutes)

1. **Admin Quick Test:**
    - Create one service option: "Test Option" (10€, per-participant)
    - Create one option group: "Test Group" (single_required) with 2 options
    - Assign both to an existing service
    - Save and verify assignments appear

2. **Customer Quick Test:**
    - Navigate to the service with new options
    - Start booking process
    - Select options and verify pricing updates
    - Complete booking and verify options in confirmation

### Priority 2: Core Functionality (1 hour)

1. Test all option types (per-participant, fixed, quote-based)
2. Test all group selection types
3. Test validation rules
4. Test pricing calculations with different scenarios

### Priority 3: Edge Cases and Integration (2 hours)

1. Test complex option combinations
2. Test error handling and recovery
3. Test performance with many options
4. Test cross-browser compatibility

## Troubleshooting Common Issues

### Issue: Options not displaying on service page

**Check:**

- Option assignments are active (`is_active = true`)
- Service has option assignments in database
- RLS policies allow access to option data

### Issue: Pricing calculations incorrect

**Check:**

- Per-participant flag is set correctly
- Custom pricing overrides are applied
- Participant count is being passed correctly

### Issue: Validation not working

**Check:**

- Group selection types are configured correctly
- Min/max selections are set appropriately
- Required flags are set on assignments

### Issue: Admin interface not loading options

**Check:**

- Admin authentication is working
- API endpoints are accessible
- Database connections are stable

This comprehensive testing guide ensures that the new service options system is thoroughly validated before production deployment and provides confidence that all functionality works as expected.

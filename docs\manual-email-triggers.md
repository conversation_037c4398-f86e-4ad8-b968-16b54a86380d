# Manual Email Triggers - Admin Guide

This guide explains how to use the manual email trigger functionality in the admin reservation management interface.

## Overview

The manual email trigger feature allows administrators to resend emails to customers when:

- Customers report not receiving emails
- There were previous email delivery issues
- You need to send additional notifications

## Location

The email actions are available in the **Admin Reservations** page:

1. Navigate to **Admin** → **Reservations**
2. Click the **eye icon** (👁️) to view reservation details
3. Scroll down to the **"Actions Email"** section

## Available Email Types

### Customer Emails

#### 1. Confirmation de réservation

- **Description**: Booking confirmation with PDF attachment and QR code
- **When to use**: When customer didn't receive initial booking confirmation
- **Includes**:
    - Reservation details
    - PDF confirmation with QR code
    - Verification URL

#### 2. Rappel de réservation

- **Description**: Booking reminder email (normally sent 24h before)
- **When to use**: Manual reminder or when automated reminder failed
- **Includes**:
    - Reservation details
    - Reminder about upcoming service

#### 3. Confirmation de paiement

- **Description**: Payment confirmation with invoice PDF
- **When to use**: When customer didn't receive payment confirmation
- **Requires**: Completed payment (select which payment)
- **Includes**:
    - Payment details
    - Invoice PDF attachment
    - Remaining balance (if deposit)

### Admin Notification Emails

#### 4. Notification admin - Nouvelle réservation

- **Description**: Internal notification about new reservation
- **When to use**: When admin team needs to be notified again
- **Sent to**: Business admin email (configured in settings, <NAME_EMAIL>)

#### 5. Notification admin - Paiement reçu

- **Description**: Internal notification about received payment
- **When to use**: When admin team needs payment notification
- **Requires**: Completed payment (select which payment)
- **Sent to**: Business admin email (configured in settings, <NAME_EMAIL>)

## How to Send Manual Emails

### Step 1: Access Reservation Details

1. Go to **Admin** → **Reservations**
2. Find the reservation in the list
3. Click the **eye icon** (👁️) to open details modal

### Step 2: Navigate to Email Actions

1. Scroll down to the **"Actions Email"** section
2. Verify the recipient email addresses are correct:
    - **Client emails**: Sent to customer's email address
    - **Admin emails**: Sent to business admin email
3. Choose the appropriate email type

### Step 3: Send Email

1. Click the button for the desired email type
2. Review the confirmation dialog:
    - Email type and description
    - Recipient information
    - Reservation number
3. Click **"Envoyer l'email"** to confirm

### Step 4: Verify Success

- Success/error message will appear below the buttons
- Check the admin audit log for email sending records
- Messages disappear automatically after 5 seconds

## Important Notes

### Rate Limiting

- Emails are sent with built-in rate limiting (2 emails per second)
- Multiple emails are queued and sent sequentially
- This prevents API rate limit errors

### Confirmation Required

- All email sends require confirmation to prevent accidental sends
- Confirmation dialog shows detailed information about what will be sent

### Audit Logging

- All manual email sends are logged in the admin audit log
- Logs include success/failure status and recipient information
- Use for troubleshooting and compliance

### Email Requirements

#### For Customer Emails:

- Customer must have a valid email address
- Reservation must exist and be accessible

#### For Payment-Related Emails:

- At least one completed payment must exist
- Select the specific payment for payment confirmations

#### For Admin Emails:

- Admin notification email must be configured in business settings
- Falls back to default email if not configured

## Troubleshooting

### "Aucune adresse email trouvée"

- Customer doesn't have an email address in their profile
- Update customer information first

### "Payment ID is required"

- You're trying to send a payment-related email without selecting a payment
- Choose a completed payment from the list

### Email Send Failures

- Check the error message in the result notification
- Common issues:
    - Invalid email address
    - Email service configuration problems
    - Network connectivity issues

### Missing Email Options

- **No payment confirmation buttons**: No completed payments exist
- **No admin notification buttons**: Admin email not configured

## Best Practices

1. **Verify Information**: Always check recipient email and reservation details before sending
2. **Use Appropriate Type**: Choose the correct email type for the situation
3. **Check Results**: Wait for success/error confirmation before closing the modal
4. **Document Issues**: Note any recurring email delivery problems
5. **Rate Limiting**: Don't send multiple emails rapidly - the system handles queuing automatically

## Security Features

- **Admin Authentication**: Only authenticated admin users can send emails
- **Confirmation Dialogs**: Prevents accidental email sends
- **Audit Logging**: All actions are logged for security and compliance
- **Rate Limiting**: Prevents abuse and respects email service limits

## Email Service Configuration

The manual email triggers use the same email service configuration as automated emails:

- **Service**: Resend API
- **Rate Limit**: 2 emails per second
- **From Address**: Configured in environment variables
- **Admin Email**: Configurable in business settings

For email service setup, see: [Email Notification Setup Guide](./email-notification-setup.md)

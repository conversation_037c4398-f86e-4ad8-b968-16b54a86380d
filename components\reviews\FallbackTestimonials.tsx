"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { MapPin, Star } from "lucide-react";
import { useEffect, useState } from "react";

const fallbackTestimonials = [
  {
    id: "fallback_1",
    name: "<PERSON>",
    initials: "MD",
    rating: 5,
    comment: "Une expérience magique ! L'équipe est passionnée et les paysages à couper le souffle.",
    location: "Paris",
    service: "WaterBikes",
    relativeTime: "il y a 2 semaines",
  },
  {
    id: "fallback_2", 
    name: "<PERSON><PERSON><PERSON>",
    initials: "J<PERSON>",
    rating: 5,
    comment: "Parfait pour découvrir la Guadeloupe autrement. Nos enfants ont adoré la chasse au trésor !",
    location: "Lyon",
    service: "Visite Culturelle",
    relativeTime: "il y a 3 semaines",
  },
  {
    id: "fallback_3",
    name: "<PERSON>", 
    initials: "<PERSON>",
    rating: 4,
    comment: "Très belle excursion, guide compétent et paysages magnifiques. Je recommande vivement !",
    location: "Marseille",
    service: "Rencontre Pélicans",
    relativeTime: "il y a 1 mois",
  },
  {
    id: "fallback_4",
    name: "Pierre Moreau",
    initials: "PM", 
    rating: 5,
    comment: "Service exceptionnel et découverte inoubliable de la mangrove. Merci pour cette aventure !",
    location: "Toulouse",
    service: "WaterBikes",
    relativeTime: "il y a 1 semaine",
  },
  {
    id: "fallback_5",
    name: "Isabelle Rousseau",
    initials: "IR",
    rating: 4,
    comment: "Très bonne organisation, personnel accueillant. Les waterbikes sont vraiment amusants !",
    location: "Nantes",
    service: "WaterBikes", 
    relativeTime: "il y a 2 semaines",
  },
];

interface FallbackTestimonialsProps {
  className?: string;
  showFallbackNotice?: boolean;
}

export function FallbackTestimonials({ 
  className = "",
  showFallbackNotice = false 
}: FallbackTestimonialsProps) {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % fallbackTestimonials.length);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  const currentReview = fallbackTestimonials[currentTestimonial];

  return (
    <div className={className}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="text-center mb-16"
      >
        <Badge className="mb-4 bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-700 border-orange-200 px-4 py-2">
          ⭐ Témoignages
        </Badge>
        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          Ce Que Disent Nos
          <span className="block text-emerald-600">Aventuriers</span>
        </h2>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Découvrez les témoignages authentiques de nos aventuriers
        </p>
        
        {showFallbackNotice && (
          <div className="mt-4 text-sm text-gray-500 bg-gray-100 rounded-lg p-3 max-w-md mx-auto">
            ℹ️ Témoignages en mode hors ligne
          </div>
        )}
      </motion.div>

      {/* Review Card */}
      <motion.div
        key={currentTestimonial}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="text-center"
      >
        <Card className="p-8 shadow-xl border-0 bg-white/80 backdrop-blur-sm max-w-4xl mx-auto">
          <CardContent className="p-0">
            {/* Star Rating */}
            <div className="flex justify-center mb-4">
              {[...Array(currentReview.rating)].map((_, i) => (
                <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
              ))}
            </div>

            {/* Review Text */}
            <blockquote className="text-xl md:text-2xl text-gray-700 mb-6 italic leading-relaxed">
              "{currentReview.comment}"
            </blockquote>

            {/* Author Info */}
            <div className="flex items-center justify-center space-x-4">
              {/* Initials Avatar */}
              <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                <span className="text-emerald-700 font-semibold">
                  {currentReview.initials}
                </span>
              </div>

              <div className="text-center">
                <p className="font-semibold text-gray-900 text-lg">
                  {currentReview.name}
                </p>
                <div className="flex items-center justify-center text-gray-500 text-sm">
                  <MapPin className="w-4 h-4 mr-1" />
                  {currentReview.location}
                </div>
                <Badge
                  variant="outline"
                  className="mt-2 text-emerald-600 border-emerald-200"
                >
                  {currentReview.service}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Navigation Dots */}
      <div className="flex justify-center mt-8 space-x-2">
        {fallbackTestimonials.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentTestimonial(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentTestimonial
                ? "bg-emerald-600 scale-125"
                : "bg-gray-300 hover:bg-gray-400"
            }`}
            aria-label={`Voir le témoignage ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}

export default FallbackTestimonials;

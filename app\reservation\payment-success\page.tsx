"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";

export default function PaymentSuccessPage() {
	const [status, setStatus] = useState("checking");
	const [message, setMessage] = useState("Vérification du paiement...");

	const searchParams = useSearchParams();
	const bookingId = searchParams.get("booking");
	const paymentIntent = searchParams.get("payment_intent");
	const paymentIntentClientSecret = searchParams.get("payment_intent_client_secret");

	useEffect(() => {
		const handlePaymentSuccess = async () => {
			if (!bookingId) {
				setStatus("error");
				setMessage("ID de réservation manquant");
				return;
			}

			if (!paymentIntent) {
				setStatus("error");
				setMessage("ID de paiement manquant");
				return;
			}

			try {
				// First, confirm the payment with our API to update reservation status
				setMessage("Confirmation du paiement...");
				const confirmResponse = await fetch("/api/payments/confirm", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						paymentIntentId: paymentIntent,
					}),
				});

				if (!confirmResponse.ok) {
					const errorData = await confirmResponse.json();
					throw new Error(errorData.error || "Erreur lors de la confirmation du paiement");
				}

				const confirmData = await confirmResponse.json();
				console.log("Payment confirmation result:", confirmData);

				setMessage("Vérification de la caution...");

				// Fetch booking data
				const bookingResponse = await fetch(`/api/bookings/${bookingId}`);
				if (!bookingResponse.ok) {
					throw new Error("Failed to fetch booking");
				}

				const bookingData = await bookingResponse.json();
				console.log("Booking data:", bookingData.data);

				// Fetch service data
				const servicesResponse = await fetch("/api/services");
				if (!servicesResponse.ok) {
					throw new Error("Failed to fetch services");
				}

				const servicesData = await servicesResponse.json();
				const service = servicesData.services?.find((s: any) => s.id === bookingData.data?.service_id);

				console.log("Service found:", service?.name);
				console.log("Requires security deposit:", service?.requires_security_deposit);
				console.log("Security deposit amount:", service?.security_deposit_amount);
				console.log("Current status:", bookingData.data?.security_deposit_status);
				console.log("Current method:", bookingData.data?.security_deposit_method);

				// Check if security deposit is required
				if (
					service?.requires_security_deposit === true &&
					service?.security_deposit_amount > 0 &&
					bookingData.data?.security_deposit_status === "none" &&
					bookingData.data?.security_deposit_method === "manual"
				) {
					// Build redirect URL to security deposit selection
					const redirectUrl = `/reservation?step=security-deposit&booking=${bookingId}&payment_intent=${paymentIntent}&payment_intent_client_secret=${paymentIntentClientSecret}&service_id=${service.id}&amount=${service.security_deposit_amount}`;

					setMessage("Redirection vers la sélection de caution...");

					// Redirect after a short delay
					setTimeout(() => {
						window.location.href = redirectUrl;
					}, 1000);
					return;
				}

				setMessage("Redirection vers la confirmation...");

				// No security deposit required, go to confirmation
				const confirmationUrl = `/reservation/confirmation?booking=${bookingId}&payment_intent=${paymentIntent}&payment_intent_client_secret=${paymentIntentClientSecret}&redirect_status=succeeded`;

				setTimeout(() => {
					window.location.href = confirmationUrl;
				}, 1000);
			} catch (error) {
				console.error("Error in payment success handler:", error);
				setStatus("error");
				setMessage("Erreur lors de la vérification du paiement");
			}
		};

		handlePaymentSuccess();
	}, [bookingId, paymentIntent, paymentIntentClientSecret]);

	return (
		<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50 flex items-center justify-center">
			<div className="text-center">
				<div className="animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-500 mx-auto"></div>
				<p className="mt-4 text-lg text-gray-600">{message}</p>

				{status === "error" && (
					<div className="mt-4">
						<p className="text-red-600 mb-4">{message}</p>
						<button
							onClick={() => (window.location.href = "/")}
							className="bg-emerald-500 text-white px-6 py-2 rounded-lg hover:bg-emerald-600"
						>
							Retour à l'accueil
						</button>
					</div>
				)}
			</div>
		</div>
	);
}

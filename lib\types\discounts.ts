export interface DiscountCoupons {
	Row: {
		id: string;
		code: string;
		description: string | null;
		discount_type: string; // 'percentage' | 'fixed_amount'
		discount_value: number;
		min_purchase_amount: number | null;
		max_discount_amount: number | null;
		usage_limit: number | null;
		current_usage: number;
		valid_from: string | null;
		valid_until: string | null;
		applicable_services: string[] | null;
		is_active: boolean;
		created_by: string | null;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: {
		id?: string;
		code: string;
		description?: string | null;
		discount_type: string;
		discount_value: number;
		min_purchase_amount?: number | null;
		max_discount_amount?: number | null;
		usage_limit?: number | null;
		current_usage?: number;
		valid_from?: string | null;
		valid_until?: string | null;
		applicable_services?: string[] | null;
		is_active?: boolean;
		created_by?: string | null;
		created_at?: string | null;
		updated_at?: string | null;
	};
	Update: {
		id?: string;
		code?: string;
		description?: string | null;
		discount_type?: string;
		discount_value?: number;
		min_purchase_amount?: number | null;
		max_discount_amount?: number | null;
		usage_limit?: number | null;
		current_usage?: number;
		valid_from?: string | null;
		valid_until?: string | null;
		applicable_services?: string[] | null;
		is_active?: boolean;
		created_by?: string | null;
		created_at?: string | null;
		updated_at?: string | null;
	};
}

"use client";

import { NotificationProvider } from "@/contexts/NotificationContext";
import AdminHeader from "./AdminHeader";
import AdminSidebar from "./AdminSidebar";
import ProtectedAdminRoute from "./ProtectedAdminRoute";

interface AdminLayoutProps {
	children: React.ReactNode;
	requiredPermission?: string;
	adminOnly?: boolean;
}

export default function AdminLayout({ children, requiredPermission, adminOnly = false }: AdminLayoutProps) {
	return (
		<ProtectedAdminRoute>
			<NotificationProvider>
				<div className="flex">
					<AdminSidebar />
					<div className="flex-1">
						<AdminHeader />
						<main className="p-6">{children}</main>
					</div>
				</div>
			</NotificationProvider>
		</ProtectedAdminRoute>
	);
}

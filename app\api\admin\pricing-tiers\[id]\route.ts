import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type PricingTier = Database["public"]["Tables"]["pricing_tiers"]["Row"];
type PricingTierUpdate = Database["public"]["Tables"]["pricing_tiers"]["Update"];

// GET /api/admin/pricing-tiers/[id] - Get single pricing tier
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const pricingTierId = params.id;

		const { data: pricingTier, error } = await supabaseAdmin
			.from("pricing_tiers")
			.select(
				`
        *,
        service:services (
          id,
          name,
          category,
          is_active,
          max_participants
        )
      `
			)
			.eq("id", pricingTierId)
			.single();

		if (error) {
			if (error.code === "PGRST116") {
				return NextResponse.json({ error: "Pricing tier not found" }, { status: 404 });
			}
			console.error("Error fetching pricing tier:", error);
			return NextResponse.json({ error: "Failed to fetch pricing tier" }, { status: 500 });
		}

		return NextResponse.json({ pricingTier });
	} catch (error) {
		console.error("Pricing tier GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:read");

// PUT /api/admin/pricing-tiers/[id] - Update single pricing tier
export const PUT = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const pricingTierId = params.id;
		const updates: PricingTierUpdate = await request.json();

		// Get current pricing tier for audit log
		const { data: currentPricingTier } = await supabaseAdmin
			.from("pricing_tiers")
			.select("*")
			.eq("id", pricingTierId)
			.single();

		if (!currentPricingTier) {
			return NextResponse.json({ error: "Pricing tier not found" }, { status: 404 });
		}

		// Validate age ranges if provided
		if (updates.min_age !== undefined && updates.max_age !== undefined) {
			if (updates.min_age !== null && updates.max_age !== null && updates.min_age > updates.max_age) {
				return NextResponse.json({ error: "min_age cannot be greater than max_age" }, { status: 400 });
			}
		}

		// Update pricing tier
		const { data: updatedPricingTier, error } = await supabaseAdmin
			.from("pricing_tiers")
			.update(updates)
			.eq("id", pricingTierId)
			.select()
			.single();

		if (error) {
			console.error("Error updating pricing tier:", error);
			return NextResponse.json({ error: "Failed to update pricing tier" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(
			user.id,
			"update",
			"pricing_tiers",
			pricingTierId,
			currentPricingTier,
			updatedPricingTier,
			request
		);

		return NextResponse.json({ pricingTier: updatedPricingTier });
	} catch (error) {
		console.error("Pricing tier PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// DELETE /api/admin/pricing-tiers/[id] - Deactivate single pricing tier
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const pricingTierId = params.id;

		// Get pricing tier for audit log
		const { data: pricingTierToDeactivate } = await supabaseAdmin
			.from("pricing_tiers")
			.select("*")
			.eq("id", pricingTierId)
			.single();

		if (!pricingTierToDeactivate) {
			return NextResponse.json({ error: "Pricing tier not found" }, { status: 404 });
		}

		// Check if this pricing tier is used in any active reservations
		const { data: activeReservations } = await supabaseAdmin
			.from("reservation_participants")
			.select("id")
			.eq("pricing_tier_id", pricingTierId)
			.limit(1);

		if (activeReservations && activeReservations.length > 0) {
			return NextResponse.json(
				{
					error: "Cannot deactivate pricing tier that is used in existing reservations",
				},
				{ status: 400 }
			);
		}

		// Soft delete by setting is_active to false
		const { error } = await supabaseAdmin
			.from("pricing_tiers")
			.update({ is_active: false })
			.eq("id", pricingTierId);

		if (error) {
			console.error("Error deactivating pricing tier:", error);
			return NextResponse.json({ error: "Failed to deactivate pricing tier" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(
			user.id,
			"delete",
			"pricing_tiers",
			pricingTierId,
			pricingTierToDeactivate,
			{
				...pricingTierToDeactivate,
				is_active: false,
			},
			request
		);

		return NextResponse.json({ message: "Pricing tier deactivated successfully" });
	} catch (error) {
		console.error("Pricing tier DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

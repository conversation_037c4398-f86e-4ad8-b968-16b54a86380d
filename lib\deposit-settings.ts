import { supabase } from './supabase';

export interface DepositSettings {
  depositPercentage: number;
  isDepositEnabled: boolean;
  minimumDepositAmount?: number;
  maximumDepositAmount?: number;
}

export const DEFAULT_DEPOSIT_PERCENTAGE = 20;
export const MIN_DEPOSIT_PERCENTAGE = 0;
export const MAX_DEPOSIT_PERCENTAGE = 100;

// Get deposit settings from business_settings table
export const getDepositSettings = async (): Promise<DepositSettings> => {
  try {
    const { data: settings, error } = await supabase
      .from('business_settings')
      .select('key, value, value_type')
      .in('key', [
        'deposit_percentage',
        'deposit_enabled',
        'minimum_deposit_amount',
        'maximum_deposit_amount'
      ]);

    if (error) {
      console.error('Error fetching deposit settings:', error);
      return getDefaultDepositSettings();
    }

    const settingsMap = settings?.reduce((acc, setting) => {
      let value = setting.value;
      
      // Parse value based on type
      switch (setting.value_type) {
        case 'number':
          value = parseFloat(setting.value);
          break;
        case 'boolean':
          value = setting.value === 'true';
          break;
        case 'json':
          try {
            value = JSON.parse(setting.value);
          } catch {
            value = setting.value;
          }
          break;
        default:
          value = setting.value;
      }
      
      acc[setting.key] = value;
      return acc;
    }, {} as Record<string, any>) || {};

    return {
      depositPercentage: settingsMap.deposit_percentage ?? DEFAULT_DEPOSIT_PERCENTAGE,
      isDepositEnabled: settingsMap.deposit_enabled ?? true,
      minimumDepositAmount: settingsMap.minimum_deposit_amount,
      maximumDepositAmount: settingsMap.maximum_deposit_amount,
    };
  } catch (error) {
    console.error('Error getting deposit settings:', error);
    return getDefaultDepositSettings();
  }
};

// Get default deposit settings
export const getDefaultDepositSettings = (): DepositSettings => ({
  depositPercentage: DEFAULT_DEPOSIT_PERCENTAGE,
  isDepositEnabled: true,
});

// Update deposit settings
export const updateDepositSettings = async (settings: Partial<DepositSettings>): Promise<boolean> => {
  try {
    const updates = [];

    if (settings.depositPercentage !== undefined) {
      // Validate deposit percentage
      if (settings.depositPercentage < MIN_DEPOSIT_PERCENTAGE || settings.depositPercentage > MAX_DEPOSIT_PERCENTAGE) {
        throw new Error(`Deposit percentage must be between ${MIN_DEPOSIT_PERCENTAGE}% and ${MAX_DEPOSIT_PERCENTAGE}%`);
      }

      updates.push({
        key: 'deposit_percentage',
        value: settings.depositPercentage.toString(),
        value_type: 'number',
        category: 'payment',
        description: 'Default deposit percentage for reservations',
        is_public: false,
      });
    }

    if (settings.isDepositEnabled !== undefined) {
      updates.push({
        key: 'deposit_enabled',
        value: settings.isDepositEnabled.toString(),
        value_type: 'boolean',
        category: 'payment',
        description: 'Enable deposit payment option for customers',
        is_public: false,
      });
    }

    if (settings.minimumDepositAmount !== undefined) {
      updates.push({
        key: 'minimum_deposit_amount',
        value: settings.minimumDepositAmount.toString(),
        value_type: 'number',
        category: 'payment',
        description: 'Minimum deposit amount in euros',
        is_public: false,
      });
    }

    if (settings.maximumDepositAmount !== undefined) {
      updates.push({
        key: 'maximum_deposit_amount',
        value: settings.maximumDepositAmount.toString(),
        value_type: 'number',
        category: 'payment',
        description: 'Maximum deposit amount in euros',
        is_public: false,
      });
    }

    // Upsert each setting
    for (const update of updates) {
      const { error } = await supabase
        .from('business_settings')
        .upsert(update, {
          onConflict: 'key',
        });

      if (error) {
        console.error('Error updating deposit setting:', error);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error updating deposit settings:', error);
    return false;
  }
};

// Calculate deposit amount based on total amount and settings
export const calculateDepositAmount = (
  totalAmount: number,
  depositSettings: DepositSettings
): number => {
  if (!depositSettings.isDepositEnabled) {
    return totalAmount; // Full payment if deposits are disabled
  }

  const depositAmount = (totalAmount * depositSettings.depositPercentage) / 100;

  // Apply minimum and maximum constraints if set
  let finalDepositAmount = depositAmount;

  if (depositSettings.minimumDepositAmount && finalDepositAmount < depositSettings.minimumDepositAmount) {
    finalDepositAmount = depositSettings.minimumDepositAmount;
  }

  if (depositSettings.maximumDepositAmount && finalDepositAmount > depositSettings.maximumDepositAmount) {
    finalDepositAmount = depositSettings.maximumDepositAmount;
  }

  // Ensure deposit doesn't exceed total amount
  return Math.min(finalDepositAmount, totalAmount);
};

// Calculate remaining amount after deposit
export const calculateRemainingAmount = (
  totalAmount: number,
  depositAmount: number
): number => {
  return Math.max(0, totalAmount - depositAmount);
};

// Validate deposit percentage
export const validateDepositPercentage = (percentage: number): { isValid: boolean; error?: string } => {
  if (typeof percentage !== 'number' || isNaN(percentage)) {
    return { isValid: false, error: 'Deposit percentage must be a valid number' };
  }

  if (percentage < MIN_DEPOSIT_PERCENTAGE || percentage > MAX_DEPOSIT_PERCENTAGE) {
    return { 
      isValid: false, 
      error: `Deposit percentage must be between ${MIN_DEPOSIT_PERCENTAGE}% and ${MAX_DEPOSIT_PERCENTAGE}%` 
    };
  }

  return { isValid: true };
};

// Initialize default deposit settings if they don't exist
export const initializeDepositSettings = async (): Promise<void> => {
  try {
    const { data: existingSettings } = await supabase
      .from('business_settings')
      .select('key')
      .eq('key', 'deposit_percentage')
      .single();

    if (!existingSettings) {
      await updateDepositSettings({
        depositPercentage: DEFAULT_DEPOSIT_PERCENTAGE,
        isDepositEnabled: true,
      });
    }
  } catch (error) {
    console.error('Error initializing deposit settings:', error);
  }
};

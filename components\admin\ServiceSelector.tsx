"use client";

import { useState, useEffect } from "react";
import { Clock, Users, MapPin, Loader2, Search } from "lucide-react";
import { adminApi } from "@/lib/api-client";

interface Service {
	id: string;
	name: string;
	description?: string;
	duration_minutes: number;
	max_participants: number;
	min_age?: number;
	max_age?: number;
	category?: string;
	location?: string;
	image_url?: string;
	base_price: number;
	fixed_price: boolean;
	pricing_tiers?: Array<{
		id: string;
		tier_name: string;
		price: number;
		min_age?: number;
		max_age?: number;
	}>;
}

interface ServiceSelectorProps {
	selectedService: Service | null;
	onServiceSelect: (service: Service | null) => void;
	disabled?: boolean;
}

export default function ServiceSelector({
	selectedService,
	onServiceSelect,
	disabled = false,
}: ServiceSelectorProps) {
	const [services, setServices] = useState<Service[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedCategory, setSelectedCategory] = useState<string>("all");

	useEffect(() => {
		fetchServices();
	}, []);

	const fetchServices = async () => {
		try {
			setLoading(true);
			setError(null);
			const response = await adminApi.getServices();
			setServices(response.services || []);
		} catch (err) {
			console.error("Error fetching services:", err);
			setError("Erreur lors du chargement des services");
		} finally {
			setLoading(false);
		}
	};

	const formatDuration = (minutes: number) => {
		if (minutes < 60) {
			return `${minutes} min`;
		}
		const hours = Math.floor(minutes / 60);
		const remainingMinutes = minutes % 60;
		if (remainingMinutes === 0) {
			return `${hours}h`;
		}
		return `${hours}h${remainingMinutes}`;
	};

	const formatAgeRange = (minAge?: number, maxAge?: number) => {
		if (!minAge && !maxAge) return "pour tous les âges";
		if (!minAge) return `jusqu'à ${maxAge} ans`;
		if (!maxAge) return `à partir de ${minAge} ans`;
		if (minAge === maxAge) return `${minAge} ans`;
		return `${minAge}-${maxAge} ans`;
	};

	const formatPrice = (service: Service) => {
		if (service.pricing_tiers && service.pricing_tiers.length > 0) {
			const prices = service.pricing_tiers.map(tier => tier.price);
			const minPrice = Math.min(...prices);
			const maxPrice = Math.max(...prices);
			if (minPrice === maxPrice) {
				return `${minPrice.toFixed(2)}€`;
			}
			return `${minPrice.toFixed(2)}€ - ${maxPrice.toFixed(2)}€`;
		}
		return `${service.base_price.toFixed(2)}€`;
	};

	// Get unique categories
	const categories = ["all", ...new Set(services.map(s => s.category).filter(Boolean))];

	// Filter services
	const filteredServices = services.filter(service => {
		const matchesSearch = !searchTerm || 
			service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			service.description?.toLowerCase().includes(searchTerm.toLowerCase());
		
		const matchesCategory = selectedCategory === "all" || service.category === selectedCategory;
		
		return matchesSearch && matchesCategory;
	});

	if (loading) {
		return (
			<div className="space-y-4">
				<label className="block text-sm font-medium text-gray-700">
					Service <span className="text-red-500">*</span>
				</label>
				<div className="flex items-center justify-center py-8">
					<Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
					<span className="ml-2 text-gray-600">Chargement des services...</span>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="space-y-4">
				<label className="block text-sm font-medium text-gray-700">
					Service <span className="text-red-500">*</span>
				</label>
				<div className="bg-red-50 border border-red-200 rounded-lg p-4">
					<p className="text-red-700">{error}</p>
					<button
						onClick={fetchServices}
						className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
					>
						Réessayer
					</button>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			<label className="block text-sm font-medium text-gray-700">
				Service <span className="text-red-500">*</span>
			</label>

			{/* Selected Service Display */}
			{selectedService && (
				<div className="mb-4 p-4 bg-emerald-50 border border-emerald-200 rounded-lg">
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<h4 className="font-medium text-gray-900">{selectedService.name}</h4>
							<div className="mt-2 grid grid-cols-2 gap-4 text-sm text-gray-600">
								<div className="flex items-center">
									<Clock className="h-4 w-4 mr-1" />
									{formatDuration(selectedService.duration_minutes)}
								</div>
								<div className="flex items-center">
									<Users className="h-4 w-4 mr-1" />
									Max {selectedService.max_participants} pers.
								</div>
								{selectedService.location && (
									<div className="flex items-center">
										<MapPin className="h-4 w-4 mr-1" />
										{selectedService.location}
									</div>
								)}
								<div className="font-medium text-emerald-600">
									{formatPrice(selectedService)}
								</div>
							</div>
							<p className="text-sm text-gray-500 mt-1">
								{formatAgeRange(selectedService.min_age || undefined, selectedService.max_age || undefined)}
							</p>
						</div>
						<button
							onClick={() => onServiceSelect(null)}
							className="text-gray-400 hover:text-gray-600 ml-4"
							disabled={disabled}
						>
							✕
						</button>
					</div>
				</div>
			)}

			{/* Service Selection */}
			{!selectedService && (
				<>
					{/* Search and Filter */}
					<div className="space-y-3">
						<div className="relative">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
							<input
								type="text"
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								placeholder="Rechercher un service..."
								className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								disabled={disabled}
							/>
						</div>

						{categories.length > 1 && (
							<select
								value={selectedCategory}
								onChange={(e) => setSelectedCategory(e.target.value)}
								className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
								disabled={disabled}
							>
								<option value="all">Toutes les catégories</option>
								{categories.slice(1).map(category => (
									<option key={category} value={category}>
										{category}
									</option>
								))}
							</select>
						)}
					</div>

					{/* Services Grid */}
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
						{filteredServices.length === 0 ? (
							<div className="col-span-full text-center py-8 text-gray-500">
								Aucun service trouvé
							</div>
						) : (
							filteredServices.map((service) => (
								<div
									key={service.id}
									onClick={() => !disabled && onServiceSelect(service)}
									className={`border rounded-lg p-4 cursor-pointer transition-colors ${
										disabled 
											? "opacity-50 cursor-not-allowed" 
											: "hover:border-emerald-300 hover:bg-emerald-50"
									}`}
								>
									<div className="flex items-start space-x-3">
										{service.image_url && (
											<img
												src={service.image_url}
												alt={service.name}
												className="w-16 h-16 object-cover rounded-lg flex-shrink-0"
											/>
										)}
										<div className="flex-1 min-w-0">
											<h4 className="font-medium text-gray-900 truncate">{service.name}</h4>
											{service.category && (
												<span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded mt-1">
													{service.category}
												</span>
											)}
											<div className="mt-2 space-y-1 text-sm text-gray-600">
												<div className="flex items-center">
													<Clock className="h-3 w-3 mr-1" />
													{formatDuration(service.duration_minutes)}
												</div>
												<div className="flex items-center">
													<Users className="h-3 w-3 mr-1" />
													Max {service.max_participants} pers.
												</div>
												{service.location && (
													<div className="flex items-center">
														<MapPin className="h-3 w-3 mr-1" />
														{service.location}
													</div>
												)}
											</div>
											<div className="mt-2 flex items-center justify-between">
												<span className="text-sm text-gray-500">
													{formatAgeRange(service.min_age || undefined, service.max_age || undefined)}
												</span>
												<span className="font-medium text-emerald-600">
													{formatPrice(service)}
												</span>
											</div>
										</div>
									</div>
								</div>
							))
						)}
					</div>
				</>
			)}
		</div>
	);
}

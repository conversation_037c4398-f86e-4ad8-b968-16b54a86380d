export type Employees = {
	Row: {
		id: string;
		employee_code: string | null;
		hire_date: string;
		hourly_rate: number | null;
		is_active: boolean;
		skills: string[] | null;
		languages: string[] | null;
		notes: string | null;
		created_at: string | null;
		updated_at: string | null;
		default_hourly_rate: number | null;
		is_available_for_scheduling: boolean;
		max_concurrent_services: number;
		scheduling_priority: number;
		first_name: string | null;
		last_name: string | null;
		email: string | null;
		phone: string | null;
		role: string | null;
	};
	Insert: Partial<Employees["Row"]> & { hire_date: string };
	Update: Partial<Employees["Row"]>;
};

export type EmployeeAvailability = {
	Row: {
		id: string;
		employee_id: string | null;
		day_of_week: number;
		start_time: string;
		end_time: string;
		is_available: boolean;
		effective_from: string | null;
		effective_until: string | null;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<EmployeeAvailability["Row"]> & {
		day_of_week: number;
		start_time: string;
		end_time: string;
	};
	Update: Partial<EmployeeAvailability["Row"]>;
};

export type EmployeeTimeOff = {
	Row: {
		id: string;
		employee_id: string | null;
		start_date: string;
		end_date: string;
		start_time: string | null;
		end_time: string | null;
		reason: string | null;
		type: string;
		status: string;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<EmployeeTimeOff["Row"]> & {
		start_date: string;
		end_date: string;
	};
	Update: Partial<EmployeeTimeOff["Row"]>;
};

export type EmployeeAnalytics = {
	Row: {
		id: string;
		employee_id: string;
		period_start: string;
		period_end: string;
		total_assignments: number;
		completed_assignments: number;
		total_revenue_generated: number;
		average_customer_rating: number | null;
		created_at: string | null;
	};
	Insert: Partial<EmployeeAnalytics["Row"]> & {
		employee_id: string;
		period_start: string;
		period_end: string;
	};
	Update: Partial<EmployeeAnalytics["Row"]>;
};

export type EmployeeServiceQualifications = {
	Row: {
		id: string;
		employee_id: string | null;
		service_id: string | null;
		qualification_level: string;
		certified_date: string | null;
		expiry_date: string | null;
		notes: string | null;
		is_active: boolean;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<EmployeeServiceQualifications["Row"]>;
	Update: Partial<EmployeeServiceQualifications["Row"]>;
};

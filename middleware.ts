import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

export async function middleware(req: NextRequest) {
	// For now, we'll handle authentication on the client side
	// This middleware just handles basic route protection

	// Admin routes protection
	if (req.nextUrl.pathname.startsWith("/admin")) {
		// Allow access to login page
		if (req.nextUrl.pathname === "/admin/login") {
			return NextResponse.next();
		}

		// For other admin routes, let the client-side auth handle protection
		return NextResponse.next();
	}

	return NextResponse.next();
}

export const config = {
	matcher: [
		/*
		 * Match all request paths except for the ones starting with:
		 * - _next/static (static files)
		 * - _next/image (image optimization files)
		 * - favicon.ico (favicon file)
		 * - public folder
		 */
		"/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
	],
};

import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

export async function POST(request: NextRequest) {
	try {
		console.log("Adding email notification settings...");

		const settings = [
			{
				key: "admin_notification_email",
				value: "<EMAIL>",
				value_type: "string",
				category: "notifications",
				description: "Email address for admin notifications (new reservations, payments, etc.)",
				is_public: false,
			},
			{
				key: "email_notifications_enabled",
				value: "true",
				value_type: "boolean",
				category: "notifications",
				description: "Enable/disable all email notifications globally",
				is_public: false,
			},
			{
				key: "booking_confirmation_email_enabled",
				value: "true",
				value_type: "boolean",
				category: "notifications",
				description: "Enable/disable booking confirmation emails to customers",
				is_public: false,
			},
			{
				key: "admin_email_notifications_enabled",
				value: "true",
				value_type: "boolean",
				category: "notifications",
				description: "Enable/disable admin notification emails for new reservations and payments",
				is_public: false,
			},
		];

		const results = [];

		for (const setting of settings) {
			console.log(`Adding/updating setting: ${setting.key}`);

			const { data, error } = await supabase
				.from("business_settings")
				.upsert(setting, { onConflict: "key" })
				.select();

			if (error) {
				console.error(`Error updating setting ${setting.key}:`, error);
				return NextResponse.json(
					{
						success: false,
						error: `Failed to update ${setting.key}: ${error.message}`,
					},
					{ status: 500 }
				);
			}

			results.push({ key: setting.key, data });
			console.log(`✅ Setting ${setting.key} updated successfully`);
		}

		console.log("All email notification settings updated successfully!");
		return NextResponse.json({
			success: true,
			message: "All email notification settings updated successfully",
			results,
		});
	} catch (error) {
		console.error("API error:", error);
		return NextResponse.json(
			{
				success: false,
				error: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}

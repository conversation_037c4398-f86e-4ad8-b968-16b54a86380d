import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = 'force-dynamic';

type PricingTier = Database["public"]["Tables"]["pricing_tiers"]["Row"];
type PricingTierInsert = Database["public"]["Tables"]["pricing_tiers"]["Insert"];
type PricingTierUpdate = Database["public"]["Tables"]["pricing_tiers"]["Update"];

// GET /api/admin/pricing-tiers - List all pricing tiers
export const GET = withAdminAuth(async (request: NextRequest) => {
	try {
		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get("page") || "1");
		const limit = parseInt(searchParams.get("limit") || "20");
		const serviceId = searchParams.get("service_id");
		const isActive = searchParams.get("active");
		const offset = (page - 1) * limit;

		// Build query
		let query = supabaseAdmin
			.from("pricing_tiers")
			.select(
				`
        *,
        service:services (
          id,
          name,
          category,
          is_active
        )
      `
			)
			.order("created_at", { ascending: false })
			.range(offset, offset + limit - 1);

		// Apply filters
		if (serviceId) {
			query = query.eq("service_id", serviceId);
		}
		if (isActive !== null) {
			query = query.eq("is_active", isActive === "true");
		}

		const { data: pricingTiers, error, count } = await query;

		if (error) {
			console.error("Error fetching pricing tiers:", error);
			return NextResponse.json({ error: "Failed to fetch pricing tiers" }, { status: 500 });
		}

		// Get total count for pagination
		const { count: totalCount } = await supabaseAdmin
			.from("pricing_tiers")
			.select("*", { count: "exact", head: true });

		return NextResponse.json({
			pricingTiers: pricingTiers || [],
			pagination: {
				page,
				limit,
				total: totalCount || 0,
				totalPages: Math.ceil((totalCount || 0) / limit),
			},
		});
	} catch (error) {
		console.error("Pricing tiers GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:read");

// POST /api/admin/pricing-tiers - Create new pricing tier
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const pricingTierData: PricingTierInsert = await request.json();

		// Validate required fields
		if (!pricingTierData.service_id || !pricingTierData.tier_name || !pricingTierData.price) {
			return NextResponse.json(
				{ error: "Missing required fields: service_id, tier_name, price" },
				{ status: 400 }
			);
		}

		// Validate age ranges
		if (pricingTierData.min_age !== null && pricingTierData.max_age !== null) {
			if (pricingTierData.min_age > pricingTierData.max_age) {
				return NextResponse.json({ error: "min_age cannot be greater than max_age" }, { status: 400 });
			}
		}

		// Check if service exists
		const { data: service } = await supabaseAdmin
			.from("services")
			.select("id")
			.eq("id", pricingTierData.service_id)
			.single();

		if (!service) {
			return NextResponse.json({ error: "Service not found" }, { status: 404 });
		}

		// Create pricing tier
		const { data: pricingTier, error } = await supabaseAdmin
			.from("pricing_tiers")
			.insert(pricingTierData)
			.select()
			.single();

		if (error) {
			console.error("Error creating pricing tier:", error);
			return NextResponse.json({ error: "Failed to create pricing tier" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(user.id, "create", "pricing_tiers", pricingTier.id, null, pricingTier, request);

		return NextResponse.json({ pricingTier });
	} catch (error) {
		console.error("Pricing tier POST error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// PUT /api/admin/pricing-tiers - Bulk update pricing tiers
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { pricingTierIds, updates }: { pricingTierIds: string[]; updates: PricingTierUpdate } =
			await request.json();

		if (!pricingTierIds || pricingTierIds.length === 0) {
			return NextResponse.json({ error: "No pricing tier IDs provided" }, { status: 400 });
		}

		// Get current pricing tiers for audit log
		const { data: currentPricingTiers } = await supabaseAdmin
			.from("pricing_tiers")
			.select("*")
			.in("id", pricingTierIds);

		// Update pricing tiers
		const { data: updatedPricingTiers, error } = await supabaseAdmin
			.from("pricing_tiers")
			.update(updates)
			.in("id", pricingTierIds)
			.select();

		if (error) {
			console.error("Error updating pricing tiers:", error);
			return NextResponse.json({ error: "Failed to update pricing tiers" }, { status: 500 });
		}

		// Log admin actions
		for (const currentPricingTier of currentPricingTiers || []) {
			const updatedPricingTier = updatedPricingTiers?.find((pt) => pt.id === currentPricingTier.id);
			if (updatedPricingTier) {
				await logAdminAction(
					user.id,
					"update",
					"pricing_tiers",
					currentPricingTier.id,
					currentPricingTier,
					updatedPricingTier,
					request
				);
			}
		}

		return NextResponse.json({
			message: `Updated ${updatedPricingTiers?.length || 0} pricing tiers`,
			pricingTiers: updatedPricingTiers,
		});
	} catch (error) {
		console.error("Pricing tiers PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

// DELETE /api/admin/pricing-tiers - Deactivate pricing tiers
export const DELETE = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { pricingTierIds }: { pricingTierIds: string[] } = await request.json();

		if (!pricingTierIds || pricingTierIds.length === 0) {
			return NextResponse.json({ error: "No pricing tier IDs provided" }, { status: 400 });
		}

		// Get pricing tiers for audit log before deactivation
		const { data: pricingTiersToDeactivate } = await supabaseAdmin
			.from("pricing_tiers")
			.select("*")
			.in("id", pricingTierIds);

		// Soft delete by setting is_active to false
		const { error } = await supabaseAdmin
			.from("pricing_tiers")
			.update({ is_active: false })
			.in("id", pricingTierIds);

		if (error) {
			console.error("Error deactivating pricing tiers:", error);
			return NextResponse.json({ error: "Failed to deactivate pricing tiers" }, { status: 500 });
		}

		// Log admin actions
		for (const pricingTier of pricingTiersToDeactivate || []) {
			await logAdminAction(
				user.id,
				"delete",
				"pricing_tiers",
				pricingTier.id,
				pricingTier,
				{
					...pricingTier,
					is_active: false,
				},
				request
			);
		}

		return NextResponse.json({
			message: `Deactivated ${pricingTierIds.length} pricing tiers`,
		});
	} catch (error) {
		console.error("Pricing tiers DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "services:write");

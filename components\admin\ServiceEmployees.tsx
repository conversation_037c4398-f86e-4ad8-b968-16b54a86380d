"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { adminApi } from "@/lib/api-client";
import { Edit, Save, X, Users, Star } from "lucide-react";
import { useEffect, useState } from "react";

interface Employee {
	id: string;
	first_name: string;
	last_name: string;
	email: string;
	role: string;
}

interface ServiceEmployeesProps {
	serviceId: string;
	serviceName: string;
}

export default function ServiceEmployees({ serviceId, serviceName }: ServiceEmployeesProps) {
	const [employees, setEmployees] = useState<Employee[]>([]);
	const [qualifiedEmployees, setQualifiedEmployees] = useState<string[]>([]);
	const [defaultEmployeeId, setDefaultEmployeeId] = useState<string | null>(null);
	const [loading, setLoading] = useState(true);
	const [editing, setEditing] = useState(false);
	const [saving, setSaving] = useState(false);
	const [editQualifiedEmployees, setEditQualifiedEmployees] = useState<string[]>([]);
	const [editDefaultEmployeeId, setEditDefaultEmployeeId] = useState<string | null>(null);

	useEffect(() => {
		fetchData();
	}, [serviceId]);

	const fetchData = async () => {
		try {
			// Fetch all employees
			const employeesResponse = await adminApi.getEmployees({ limit: 100 });
			if (employeesResponse?.employees) {
				setEmployees(employeesResponse.employees);
			}

			// Fetch service details for default employee
			const serviceResponse = await adminApi.getService(serviceId);
			if (serviceResponse?.service) {
				setDefaultEmployeeId(serviceResponse.service.default_employee_id);
				setEditDefaultEmployeeId(serviceResponse.service.default_employee_id);
			}

			// Fetch qualified employees
			const qualificationsResponse = await adminApi.getEmployeeServiceQualifications();
			if (qualificationsResponse?.qualifications) {
				const serviceQualifications = qualificationsResponse.qualifications
					.filter((q: any) => q.service_id === serviceId && q.is_active)
					.map((q: any) => q.employee_id);
				setQualifiedEmployees(serviceQualifications);
				setEditQualifiedEmployees([...serviceQualifications]);
			}
		} catch (error) {
			console.error("Error fetching service employees data:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleEdit = () => {
		setEditing(true);
		setEditQualifiedEmployees([...qualifiedEmployees]);
		setEditDefaultEmployeeId(defaultEmployeeId);
	};

	const handleCancel = () => {
		setEditing(false);
		setEditQualifiedEmployees([...qualifiedEmployees]);
		setEditDefaultEmployeeId(defaultEmployeeId);
	};

	const handleSave = async () => {
		try {
			setSaving(true);

			// Update default employee
			if (editDefaultEmployeeId !== defaultEmployeeId) {
				await adminApi.updateService(serviceId, {
					default_employee_id: editDefaultEmployeeId,
				});
				setDefaultEmployeeId(editDefaultEmployeeId);
			}

			// Handle qualified employees changes
			const currentQualified = new Set(qualifiedEmployees);
			const newQualified = new Set(editQualifiedEmployees);

			// Remove qualifications that are no longer selected
			for (const employeeId of currentQualified) {
				if (!newQualified.has(employeeId)) {
					try {
						// Find and delete the qualification
						const qualificationsResponse = await adminApi.getEmployeeServiceQualifications();
						const qualification = qualificationsResponse.qualifications?.find(
							(q: any) => q.employee_id === employeeId && q.service_id === serviceId
						);
						if (qualification) {
							await adminApi.deleteEmployeeServiceQualification(qualification.id);
						}
					} catch (error) {
						console.error("Error removing qualification:", error);
					}
				}
			}

			// Add new qualifications
			for (const employeeId of newQualified) {
				if (!currentQualified.has(employeeId)) {
					try {
						await adminApi.createEmployeeServiceQualification({
							employee_id: employeeId,
							service_id: serviceId,
							is_active: true,
						});
					} catch (error) {
						console.error("Error adding qualification:", error);
					}
				}
			}

			setQualifiedEmployees([...editQualifiedEmployees]);
			setEditing(false);
		} catch (error) {
			console.error("Error saving employee assignments:", error);
		} finally {
			setSaving(false);
		}
	};

	const toggleEmployeeQualification = (employeeId: string) => {
		if (editQualifiedEmployees.includes(employeeId)) {
			setEditQualifiedEmployees(editQualifiedEmployees.filter(id => id !== employeeId));
		} else {
			setEditQualifiedEmployees([...editQualifiedEmployees, employeeId]);
		}
	};

	const getEmployeeName = (employeeId: string) => {
		const employee = employees.find(e => e.id === employeeId);
		return employee ? `${employee.first_name} ${employee.last_name}` : 'Employé inconnu';
	};

	const getDefaultEmployee = () => {
		return employees.find(e => e.id === defaultEmployeeId);
	};

	if (loading) {
		return <div className="p-4">Chargement...</div>;
	}

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<div>
					<h3 className="text-lg font-semibold">Gestion des employés</h3>
					<p className="text-sm text-gray-600">Service: {serviceName}</p>
				</div>
				{!editing && (
					<Button onClick={handleEdit} className="flex items-center gap-2">
						<Edit className="w-4 h-4" />
						Modifier
					</Button>
				)}
			</div>

			{/* Default Employee Section */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Star className="w-5 h-5 text-yellow-500" />
						Employé par défaut
					</CardTitle>
				</CardHeader>
				<CardContent>
					{editing ? (
						<div className="space-y-3">
							<Label>Sélectionner l'employé par défaut</Label>
							<select
								value={editDefaultEmployeeId || ""}
								onChange={(e) => setEditDefaultEmployeeId(e.target.value || null)}
								className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							>
								<option value="">Aucun employé par défaut</option>
								{employees
									.filter(emp => emp.role !== 'customer')
									.map((employee) => (
										<option key={employee.id} value={employee.id}>
											{employee.first_name} {employee.last_name} ({employee.role})
										</option>
									))}
							</select>
							<p className="text-xs text-gray-500">
								L'employé par défaut sera automatiquement assigné aux nouvelles réservations
							</p>
						</div>
					) : (
						<div>
							{defaultEmployeeId ? (
								<div className="flex items-center gap-3">
									<Badge variant="outline" className="flex items-center gap-2">
										<Star className="w-4 h-4 text-yellow-500" />
										{getEmployeeName(defaultEmployeeId)}
									</Badge>
									<span className="text-sm text-gray-500">
										({getDefaultEmployee()?.role})
									</span>
								</div>
							) : (
								<p className="text-gray-500">Aucun employé par défaut défini</p>
							)}
						</div>
					)}
				</CardContent>
			</Card>

			{/* Qualified Employees Section */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Users className="w-5 h-5" />
						Employés qualifiés
					</CardTitle>
				</CardHeader>
				<CardContent>
					{editing ? (
						<div className="space-y-4">
							<Label>Sélectionner les employés qualifiés</Label>
							<div className="space-y-2 max-h-60 overflow-y-auto">
								{employees
									.filter(emp => emp.role !== 'customer')
									.map((employee) => (
										<label key={employee.id} className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded">
											<input
												type="checkbox"
												checked={editQualifiedEmployees.includes(employee.id)}
												onChange={() => toggleEmployeeQualification(employee.id)}
												className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
											/>
											<div className="flex-1">
												<span className="font-medium">
													{employee.first_name} {employee.last_name}
												</span>
												<span className="text-sm text-gray-500 ml-2">
													({employee.role})
												</span>
												<div className="text-xs text-gray-400">{employee.email}</div>
											</div>
											{employee.id === editDefaultEmployeeId && (
												<Badge variant="outline" className="text-xs">
													<Star className="w-3 h-3 mr-1 text-yellow-500" />
													Par défaut
												</Badge>
											)}
										</label>
									))}
							</div>

							{/* Action buttons */}
							<div className="flex gap-2 pt-4 border-t">
								<Button onClick={handleSave} disabled={saving} className="flex items-center gap-2">
									<Save className="w-4 h-4" />
									{saving ? "Sauvegarde..." : "Sauvegarder"}
								</Button>
								<Button variant="outline" onClick={handleCancel} disabled={saving}>
									<X className="w-4 h-4" />
									Annuler
								</Button>
							</div>
						</div>
					) : (
						<div className="space-y-4">
							{qualifiedEmployees.length > 0 ? (
								<div className="grid grid-cols-1 md:grid-cols-2 gap-3">
									{qualifiedEmployees.map((employeeId) => {
										const employee = employees.find(e => e.id === employeeId);
										if (!employee) return null;
										
										return (
											<div key={employeeId} className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg">
												<div className="flex-1">
													<div className="font-medium">
														{employee.first_name} {employee.last_name}
													</div>
													<div className="text-sm text-gray-500">{employee.role}</div>
													<div className="text-xs text-gray-400">{employee.email}</div>
												</div>
												{employee.id === defaultEmployeeId && (
													<Badge variant="outline" className="text-xs">
														<Star className="w-3 h-3 mr-1 text-yellow-500" />
														Par défaut
													</Badge>
												)}
											</div>
										);
									})}
								</div>
							) : (
								<div className="text-center py-8 text-gray-500">
									<Users className="w-12 h-12 mx-auto mb-3 text-gray-300" />
									<p>Aucun employé qualifié</p>
									<p className="text-sm">Cliquez sur "Modifier" pour assigner des employés</p>
								</div>
							)}
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}

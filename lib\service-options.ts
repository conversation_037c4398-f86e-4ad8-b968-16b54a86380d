/**
 * Simplified Service Options Library
 *
 * Handles individual service options only (groups removed for better UX)
 */

import {
	ServiceOption,
	ServiceOptionAssignment,
	OptionSelection,
	OptionSelectionValidation,
	OptionsPricingSummary,
	RequiredOptionGroup,
	RequiredGroupValidation,
} from "./types/service-options";

/**
 * Validate option selections including required groups
 */
export function validateOptionSelections(
	assignments: ServiceOptionAssignment[],
	selections: OptionSelection[],
	requiredGroups: RequiredOptionGroup[] = []
): OptionSelectionValidation {
	const errors: string[] = [];
	const warnings: string[] = [];
	const requiredGroupValidations: RequiredGroupValidation[] = [];

	// Check required individual options
	for (const assignment of assignments) {
		if (assignment.isRequired && assignment.option) {
			const isSelected = selections.some((s) => s.optionId === assignment.option!.id);
			if (!isSelected) {
				errors.push(`L'option "${assignment.option.name}" est obligatoire`);
			}
		}
	}

	// Check required groups
	for (const group of requiredGroups) {
		const selectedFromGroup = selections.filter((s) => group.optionIds.includes(s.optionId));

		const validation: RequiredGroupValidation = {
			groupId: group.id,
			groupName: group.name,
			isValid:
				selectedFromGroup.length >= group.minRequired &&
				(group.maxRequired === undefined || selectedFromGroup.length <= group.maxRequired),
			selectedCount: selectedFromGroup.length,
			requiredMin: group.minRequired,
			errors: [],
		};

		if (selectedFromGroup.length < group.minRequired) {
			if (group.minRequired === 1) {
				validation.errors.push("Vous devez sélectionner une option");
			} else {
				const remaining = group.minRequired - selectedFromGroup.length;
				validation.errors.push(`Sélectionnez au moins ${remaining} option(s)`);
			}
			errors.push(...validation.errors);
		} else if (group.maxRequired !== undefined && selectedFromGroup.length > group.maxRequired) {
			const excess = selectedFromGroup.length - group.maxRequired;
			validation.errors.push(`Sélectionnez au maximum ${group.maxRequired} option(s)`);
			errors.push(...validation.errors);
		}

		requiredGroupValidations.push(validation);
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
		requiredGroupValidations,
	};
}

/**
 * Calculate pricing for selected options
 */
export function calculateOptionsPricing(
	assignments: ServiceOptionAssignment[],
	selections: OptionSelection[],
	participantCount: number = 1
): OptionsPricingSummary {
	let totalPrice = 0;
	const itemizedPricing: Array<{
		optionId: string;
		optionName: string;
		quantity: number;
		unitPrice: number;
		totalPrice: number;
		perParticipant: boolean;
	}> = [];

	for (const selection of selections) {
		const assignment = assignments.find((a) => a.option?.id === selection.optionId);
		if (!assignment?.option) continue;

		const option = assignment.option;
		const basePrice = option.customPrice ?? option.basePrice;
		const unitPrice = option.perParticipant ? basePrice * participantCount : basePrice;
		const itemTotal = unitPrice * selection.quantity;

		totalPrice += itemTotal;

		itemizedPricing.push({
			optionId: option.id,
			optionName: option.name,
			quantity: selection.quantity,
			unitPrice,
			totalPrice: itemTotal,
			perParticipant: option.perParticipant,
		});
	}

	return {
		total: totalPrice,
		itemizedPricing,
		participantCount,
	};
}

/**
 * Get option by ID from assignments
 */
export function getOptionById(assignments: ServiceOptionAssignment[], optionId: string): ServiceOption | null {
	const assignment = assignments.find((a) => a.option?.id === optionId);
	return assignment?.option || null;
}

/**
 * Check if an option is selected
 */
export function isOptionSelected(selections: OptionSelection[], optionId: string): boolean {
	return selections.some((s) => s.optionId === optionId);
}

/**
 * Get all available options from assignments
 */
export function getAllOptions(assignments: ServiceOptionAssignment[]): ServiceOption[] {
	return assignments.map((a) => a.option).filter((option): option is ServiceOption => option !== undefined);
}

/**
 * Format option price for display
 */
export function formatOptionPrice(option: ServiceOption, participantCount: number = 1): string {
	if (option.quoteBased && option.basePrice === 0) {
		return "sur devis";
	}

	const basePrice = option.customPrice ?? option.basePrice;
	const finalPrice = option.perParticipant ? basePrice * participantCount : basePrice;
	const priceText = `${finalPrice.toFixed(2)}€`;

	if (option.perParticipant && participantCount > 1) {
		return `${priceText} (${basePrice.toFixed(2)}€ × ${participantCount})`;
	}

	return priceText;
}

/**
 * Convert selections to database format for saving
 */
export function selectionsToDbFormat(
	selections: OptionSelection[],
	reservationId: string
): Array<{
	reservation_id: string;
	option_id: string;
	quantity: number;
}> {
	return selections.map((selection) => ({
		reservation_id: reservationId,
		option_id: selection.optionId,
		quantity: selection.quantity,
	}));
}

/**
 * Get service option assignments for a service
 */
export async function getServiceOptions(supabase: any, serviceId: string): Promise<ServiceOptionAssignment[]> {
	try {
		const { data: assignments, error } = await supabase
			.from("service_option_assignments")
			.select(
				`
        *,
        service_options (
          id,
          name,
          description,
          base_price,
          quote_based,
          per_participant,
          is_active
        )
      `
			)
			.eq("service_id", serviceId)
			.eq("is_active", true)
			.order("sort_order");

		if (error) {
			console.error("Error fetching service option assignments:", error);
			return [];
		}

		const formattedAssignments: ServiceOptionAssignment[] = assignments.map((assignment: any) => {
			const option = assignment.service_options;
			return {
				id: assignment.id,
				type: "individual" as const,
				option: {
					id: option.id,
					name: option.name,
					description: option.description || undefined,
					basePrice: option.base_price,
					customPrice: assignment.custom_price || undefined,
					quoteBased: option.quote_based,
					perParticipant: option.per_participant,
					isActive: option.is_active,
				},
				isRequired: assignment.is_required,
				sortOrder: assignment.sort_order,
			};
		});

		return formattedAssignments;
	} catch (error) {
		console.error("Error in getServiceOptions:", error);
		return [];
	}
}

// Note: saveReservationOptionSelections function removed - options are now stored directly in reservations.selected_options

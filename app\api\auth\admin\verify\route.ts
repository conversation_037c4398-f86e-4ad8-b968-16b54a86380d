import { verifyAdminAuth } from "@/lib/admin-auth";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
	try {
		const authResult = await verifyAdminAuth(request);

		if (!authResult.success || !authResult.user) {
			return NextResponse.json({ error: authResult.error || "Authentication failed" }, { status: 401 });
		}

		// Return user data if authentication is valid
		return NextResponse.json({
			user: {
				id: authResult.user.id,
				email: authResult.user.email,
				role: authResult.user.role,
			},
			authenticated: true,
		});
	} catch (error) {
		console.error("Admin verify error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}

import { NextRequest, NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { getStripeServer } from "@/lib/stripe";
import { withAdminAuth } from "@/lib/admin-auth";

export const POST = withAdminAuth(async (request: NextRequest, user, { params }) => {
	try {
		const reservationId = params.id;

		console.log("=== MANUAL SECURITY DEPOSIT SYNC ===");
		console.log("Reservation ID:", reservationId);
		console.log("User:", user.email);

		if (!reservationId) {
			return NextResponse.json({ error: "Reservation ID is required" }, { status: 400 });
		}

		const supabaseAdmin = getSupabaseAdmin();

		// Get reservation with security deposit info
		const { data: reservation, error: reservationError } = await supabaseAdmin
			.from("reservations")
			.select(`
				id,
				security_deposit_status,
				security_deposit_payment_intent_id,
				security_deposit_method
			`)
			.eq("id", reservationId)
			.single();

		if (reservationError || !reservation) {
			console.log("ERROR: Reservation not found:", reservationError);
			return NextResponse.json({ error: "Reservation not found" }, { status: 404 });
		}

		if (!reservation.security_deposit_payment_intent_id) {
			return NextResponse.json({ error: "No payment intent found for security deposit" }, { status: 404 });
		}

		console.log("Current reservation status:", reservation.security_deposit_status);

		// Get current Stripe payment intent status
		const stripe = getStripeServer();
		const paymentIntent = await stripe.paymentIntents.retrieve(reservation.security_deposit_payment_intent_id);

		console.log("Stripe payment intent status:", paymentIntent.status);

		let updatedStatus = reservation.security_deposit_status;
		let syncPerformed = false;

		// Sync status based on Stripe status
		if (paymentIntent.status === "requires_capture" && reservation.security_deposit_status === "pending") {
			console.log("Syncing status from pending to authorized");
			
			const { error: updateError } = await supabaseAdmin
				.from("reservations")
				.update({
					security_deposit_status: "authorized",
					updated_at: new Date().toISOString(),
				})
				.eq("id", reservationId);

			if (updateError) {
				console.error("Error updating reservation status:", updateError);
				return NextResponse.json({ error: "Failed to update reservation status" }, { status: 500 });
			}

			// Update the transaction status as well
			await supabaseAdmin
				.from("security_deposit_transactions")
				.update({
					status: "succeeded",
					updated_at: new Date().toISOString(),
				})
				.eq("payment_intent_id", reservation.security_deposit_payment_intent_id)
				.eq("transaction_type", "authorization");

			updatedStatus = "authorized";
			syncPerformed = true;
		} else if (paymentIntent.status === "succeeded" && reservation.security_deposit_status !== "charged") {
			console.log("Syncing status to charged (payment intent already succeeded)");
			
			const { error: updateError } = await supabaseAdmin
				.from("reservations")
				.update({
					security_deposit_status: "charged",
					updated_at: new Date().toISOString(),
				})
				.eq("id", reservationId);

			if (updateError) {
				console.error("Error updating reservation status:", updateError);
				return NextResponse.json({ error: "Failed to update reservation status" }, { status: 500 });
			}

			updatedStatus = "charged";
			syncPerformed = true;
		} else if (paymentIntent.status === "canceled" && reservation.security_deposit_status !== "released") {
			console.log("Syncing status to released (payment intent was canceled)");
			
			const { error: updateError } = await supabaseAdmin
				.from("reservations")
				.update({
					security_deposit_status: "released",
					updated_at: new Date().toISOString(),
				})
				.eq("id", reservationId);

			if (updateError) {
				console.error("Error updating reservation status:", updateError);
				return NextResponse.json({ error: "Failed to update reservation status" }, { status: 500 });
			}

			updatedStatus = "released";
			syncPerformed = true;
		}

		return NextResponse.json({
			success: true,
			data: {
				reservation_id: reservationId,
				previous_status: reservation.security_deposit_status,
				current_status: updatedStatus,
				stripe_status: paymentIntent.status,
				sync_performed: syncPerformed,
				message: syncPerformed 
					? `Status synced from ${reservation.security_deposit_status} to ${updatedStatus}` 
					: "No sync needed - statuses are already in sync",
			},
		});
	} catch (error) {
		console.error("Error syncing security deposit status:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}, "reservations:write");

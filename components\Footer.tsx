"use client";

import Image from "next/image";
import Link from "next/link";
import { MapPin, Phone, Mail, Clock, Facebook, Instagram } from "lucide-react";

// TikTok icon component (since it's not in lucide-react)
const TikTokIcon = ({ className }: { className?: string }) => (
	<svg className={className} viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
		<path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-.88-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43V7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.43z" />
	</svg>
);
import { useSettings } from "@/lib/use-settings";

export default function Footer() {
	const { settings, loading } = useSettings(undefined, true); // Get all public settings

	// Parse JSON values safely
	const parseJsonSetting = (value: string, fallback: any = []) => {
		try {
			return JSON.parse(value);
		} catch {
			return fallback;
		}
	};

	// Get operating days in French
	const getOperatingDaysText = () => {
		const operatingDays = parseJsonSetting(settings.operating_days, []);
		const dayTranslations: Record<string, string> = {
			monday: "Lundi",
			tuesday: "Mardi",
			wednesday: "Mercredi",
			thursday: "Jeudi",
			friday: "Vendredi",
			saturday: "Samedi",
			sunday: "Dimanche",
		};

		if (operatingDays.length === 0) return "Tous les jours";

		const translatedDays = operatingDays.map((day: string) => dayTranslations[day] || day);

		if (translatedDays.length === 7) return "Tous les jours";
		if (translatedDays.length === 5 && !translatedDays.includes("Samedi") && !translatedDays.includes("Dimanche")) {
			return "Du lundi au vendredi";
		}

		return translatedDays.join(", ");
	};

	// Get operating hours text
	const getOperatingHoursText = () => {
		const startTime = settings.operating_hours_start || "08:00";
		const endTime = settings.operating_hours_end || "18:00";
		return `${startTime} - ${endTime}`;
	};

	if (loading) {
		return (
			<footer className="bg-gray-900 text-white py-16">
				<div className="container mx-auto px-4">
					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
						<div className="animate-pulse">
							<div className="h-6 bg-gray-700 rounded mb-4"></div>
							<div className="h-4 bg-gray-700 rounded mb-2"></div>
							<div className="h-4 bg-gray-700 rounded"></div>
						</div>
						<div className="animate-pulse">
							<div className="h-6 bg-gray-700 rounded mb-4"></div>
							<div className="h-4 bg-gray-700 rounded mb-2"></div>
							<div className="h-4 bg-gray-700 rounded"></div>
						</div>
						<div className="animate-pulse">
							<div className="h-6 bg-gray-700 rounded mb-4"></div>
							<div className="h-4 bg-gray-700 rounded mb-2"></div>
							<div className="h-4 bg-gray-700 rounded"></div>
						</div>
						<div className="animate-pulse">
							<div className="h-6 bg-gray-700 rounded mb-4"></div>
							<div className="h-4 bg-gray-700 rounded mb-2"></div>
							<div className="h-4 bg-gray-700 rounded"></div>
						</div>
					</div>
				</div>
			</footer>
		);
	}

	return (
		<footer className="bg-gray-900 text-white py-16">
			<div className="container mx-auto px-4">
				<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
					{/* Company Info */}
					<div>
						<div className="flex items-center space-x-3 mb-6">
							<Image
								src="/images/logo-hd.png"
								alt={settings.business_name || "Soleil & Découverte"}
								width={50}
								height={50}
								className="object-contain"
							/>
							<div>
								<h3 className="text-xl font-bold bg-gradient-to-r from-orange-400 to-yellow-400 bg-clip-text text-transparent">
									{settings.business_name || "Soleil & Découverte"}
								</h3>
								<p className="text-sm text-emerald-400">L'aventure au cœur de la Guadeloupe</p>
							</div>
						</div>
						<p className="text-gray-400 leading-relaxed">
							{settings.business_description ||
								"Découvrez la Guadeloupe authentique avec nos excursions éco-responsables, entre nature préservée et traditions culturelles."}
						</p>
					</div>

					{/* Quick Links */}
					<div>
						<h4 className="text-lg font-semibold mb-4 text-emerald-400">Navigation</h4>
						<ul className="space-y-2 text-gray-400">
							<li>
								<Link href="/" className="hover:text-white transition-colors">
									Accueil
								</Link>
							</li>
							<li>
								<Link href="/services" className="hover:text-white transition-colors">
									Nos Services
								</Link>
							</li>
							{/* <li>
								<Link href="/_about" className="hover:text-white transition-colors">
									À Propos
								</Link>
							</li>
							<li>
								<Link href="/_gallery" className="hover:text-white transition-colors">
									Galerie
								</Link>
							</li> */}
							<li>
								<Link href="/contact" className="hover:text-white transition-colors">
									Contact
								</Link>
							</li>
							<li>
								<Link href="/reservation" className="hover:text-white transition-colors">
									Réserver
								</Link>
							</li>
						</ul>
					</div>

					{/* Operating Hours */}
					<div>
						<h4 className="text-lg font-semibold mb-4 text-emerald-400">Horaires</h4>
						<div className="space-y-3 text-gray-400">
							<div className="flex items-start">
								<Clock className="w-4 h-4 mr-2 text-emerald-400 mt-0.5" />
								<div>
									<p className="text-sm font-medium">{getOperatingDaysText()}</p>
									<p className="text-sm">{getOperatingHoursText()}</p>
								</div>
							</div>
							<div className="text-sm">
								<p className="text-emerald-400 font-medium">Réservation recommandée</p>
								<p>Minimum {settings.min_booking_notice_hours || 2}h à l'avance</p>
							</div>
						</div>
					</div>

					{/* Contact Info */}
					<div>
						<h4 className="text-lg font-semibold mb-4 text-emerald-400">Contact</h4>
						<div className="space-y-3 text-gray-400">
							<div className="flex items-center">
								<MapPin className="w-4 h-4 mr-2 text-emerald-400" />
								<span className="text-sm">
									{settings.business_address || "Petit-Canal, Guadeloupe"}
								</span>
							</div>
							<div className="flex items-center">
								<Phone className="w-4 h-4 mr-2 text-emerald-400" />
								<span className="text-sm">{settings.business_phone || "+33 6 40 24 44 25"}</span>
							</div>
							<div className="flex items-center">
								<Mail className="w-4 h-4 mr-2 text-emerald-400" />
								<span className="text-sm">
									{settings.business_email || "<EMAIL>"}
								</span>
							</div>

							{/* Social Media Links */}
							{(settings.facebook_url || settings.instagram_url || settings.tiktok_url) && (
								<div className="flex items-center space-x-3 pt-2">
									{settings.facebook_url && (
										<a
											href={settings.facebook_url}
											target="_blank"
											rel="noopener noreferrer"
											className="text-gray-400 hover:text-blue-400 transition-colors"
											aria-label="Facebook"
										>
											<Facebook className="w-5 h-5" />
										</a>
									)}
									{settings.instagram_url && (
										<a
											href={settings.instagram_url}
											target="_blank"
											rel="noopener noreferrer"
											className="text-gray-400 hover:text-pink-400 transition-colors"
											aria-label="Instagram"
										>
											<Instagram className="w-5 h-5" />
										</a>
									)}
									{settings.tiktok_url && (
										<a
											href={settings.tiktok_url}
											target="_blank"
											rel="noopener noreferrer"
											className="text-gray-400 hover:text-white transition-colors"
											aria-label="TikTok"
										>
											<TikTokIcon className="w-5 h-5" />
										</a>
									)}
								</div>
							)}
						</div>
					</div>
				</div>

				{/* Bottom Bar */}
				<div className="border-t border-gray-800 mt-12 pt-8 text-center">
					<p className="text-gray-400">
						© {new Date().getFullYear()} {settings.business_name || "Soleil & Découverte"}. Tous droits
						réservés. |
						<Link href="/mentions-legales" className="hover:text-white transition-colors ml-1">
							Mentions légales
						</Link>{" "}
						|
						<Link href="/confidentialite" className="hover:text-white transition-colors ml-1">
							Confidentialité
						</Link>
					</p>
				</div>
			</div>
		</footer>
	);
}

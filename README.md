# Soleil et Découverte

Site web officiel de **Soleil et Découverte** - Excursions éco-responsables en Guadeloupe.

## 🌴 À propos

Soleil et Découverte propose des excursions authentiques et éco-responsables en Guadeloupe :

- Excursions en WaterBikes
- Visites culturelles guidées
- Rencontres avec les pélicans
- Explorations de la mangrove
- Dégustations de produits locaux

## 🚀 Technologies

- **Framework:** Next.js 14
- **Language:** TypeScript
- **Styling:** Tailwind CSS
- **UI Components:** Radix UI + shadcn/ui
- **Animations:** Framer Motion
- **Icons:** Lucide React
- **Deployment:** Vercel

## 🛠️ Installation

```bash
# Cloner le repository
git clone https://github.com/BenjaminViranin/soleil-et-decouverte.git

# Installer les dépendances
cd soleil-et-decouverte
pnpm install

# Lancer en mode développement
pnpm dev
```

## 📁 Structure du projet

```
soleil-decouverte/
├── app/                    # Pages Next.js (App Router)
│   ├── about/             # Page À propos
│   ├── contact/           # Page Contact
│   ├── gallery/           # Page Galerie
│   ├── reservation/       # Page Réservation
│   ├── services/          # Page Services
│   ├── layout.tsx         # Layout principal
│   └── page.tsx           # Page d'accueil
├── components/            # Composants réutilisables
├── public/               # Assets statiques
│   ├── images/           # Images
│   └── videos/           # Vidéos
└── styles/               # Styles globaux
```

## 🎨 Assets requis

Pour un fonctionnement optimal, ajoutez ces images dans `public/images/` :

- `logo-hd.png` ✅
- `home_hero.png`
- `services_hero.png`
- `gallery_hero.png`
- `about_hero.png`
- `contact_hero.png`
- Images des services (waterbikes, cultural_tour, pelican_encounter, etc.)

## 🎥 Vidéo

La vidéo de fond `home-banner.mp4` doit être placée dans `public/videos/` ✅

## 🌐 Déploiement

Le site est optimisé pour Vercel :

1. Connectez votre repository GitHub à Vercel
2. Vercel détectera automatiquement Next.js
3. Le déploiement se fera automatiquement

## 🗺️ Sitemap et SEO

Le site utilise `next-sitemap` pour générer automatiquement le sitemap et robots.txt :

```bash
# Générer le sitemap manuellement
npm run sitemap

# Ou utiliser le script personnalisé
npm run generate-sitemap
```

**Fichiers générés :**

- `public/sitemap.xml` - Sitemap principal avec toutes les pages
- `public/robots.txt` - Instructions pour les robots d'indexation
- `app/server-sitemap.xml/route.ts` - Sitemap dynamique pour les services

**URLs incluses :**

- Pages principales (accueil, services, contact, réservation)
- Pages secondaires (à propos, galerie)
- Pages de services dynamiques
- Exclusion des routes admin et API

## 🎨 Favicon et PWA

Le site inclut une configuration complète de favicon et PWA :

**Fichiers de favicon :**

- `public/favicon.ico` - Favicon principal
- `public/site.webmanifest` - Manifest PWA
- `public/browserconfig.xml` - Configuration Microsoft
- Multiple tailles d'icônes Apple Touch

## 📧 Contact

- **Email:** <EMAIL>
- **Téléphone:** +33 6 52 73 34 91
- **Localisation:** Petit-Canal, Guadeloupe

## 📄 Licence

© 2024 Soleil et Découverte. Tous droits réservés.

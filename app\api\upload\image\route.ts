import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function POST(request: NextRequest) {
	try {
		const formData = await request.formData();
		const file = formData.get("file") as File;

		if (!file) {
			return NextResponse.json({ error: "Aucun fichier fourni" }, { status: 400 });
		}

		// Validate file type
		if (!file.type.startsWith("image/")) {
			return NextResponse.json({ error: "Le fichier doit être une image" }, { status: 400 });
		}

		// Validate file size (5MB max)
		if (file.size > 5 * 1024 * 1024) {
			return NextResponse.json({ error: "La taille du fichier ne doit pas dépasser 5MB" }, { status: 400 });
		}

		// Generate unique filename
		const timestamp = Date.now();
		const randomString = Math.random().toString(36).substring(2, 15);
		const fileExtension = file.name.split(".").pop();
		const fileName = `service-${timestamp}-${randomString}.${fileExtension}`;

		// Convert file to buffer
		const bytes = await file.arrayBuffer();
		const buffer = Buffer.from(bytes);

		// Check if bucket exists, if not create it
		const { data: buckets, error: listError } = await supabase.storage.listBuckets();

		if (listError) {
			console.error("Error listing buckets:", listError);
			return NextResponse.json({ error: "Erreur de configuration du stockage" }, { status: 500 });
		}

		const bucketExists = buckets?.some((bucket) => bucket.name === "service-images");

		if (!bucketExists) {
			console.log("Creating service-images bucket...");
			const { error: createError } = await supabase.storage.createBucket("service-images", {
				public: true,
				allowedMimeTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
				fileSizeLimit: 5242880, // 5MB
			});

			if (createError) {
				console.error("Error creating bucket:", createError);
				return NextResponse.json(
					{ error: "Erreur lors de la création du bucket de stockage" },
					{ status: 500 }
				);
			}
		}

		// Upload to Supabase Storage
		const { data, error } = await supabase.storage.from("service-images").upload(fileName, buffer, {
			contentType: file.type,
			upsert: false,
		});

		if (error) {
			console.error("Supabase upload error:", error);
			console.error("Error details:", JSON.stringify(error, null, 2));
			return NextResponse.json(
				{
					error: "Erreur lors de l'upload",
					details: error.message,
				},
				{ status: 500 }
			);
		}

		// Get public URL
		const { data: urlData } = supabase.storage.from("service-images").getPublicUrl(fileName);

		return NextResponse.json({
			url: urlData.publicUrl,
			fileName: fileName,
		});
	} catch (error) {
		console.error("Upload error:", error);
		return NextResponse.json({ error: "Erreur serveur" }, { status: 500 });
	}
}

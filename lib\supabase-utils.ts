import { supabaseAdmin } from "./supabase";
import type { SupabaseClient } from "@supabase/supabase-js";
import type { Database } from "./types";

/**
 * Utility function to ensure supabaseAdmin is available
 * Throws an error if supabaseAdmin is null
 */
export function ensureSupabaseAdmin(): SupabaseClient<Database> {
	if (!supabaseAdmin) {
		throw new Error("Database connection not available");
	}
	return supabaseAdmin;
}

/**
 * Type guard to check if supabaseAdmin is available
 */
export function isSupabaseAdminAvailable(): boolean {
	return supabaseAdmin !== null;
}

/**
 * Safe wrapper for supabaseAdmin operations
 * Returns null if supabaseAdmin is not available
 */
export function getSupabaseAdmin(): SupabaseClient<Database> | null {
	return supabaseAdmin;
}

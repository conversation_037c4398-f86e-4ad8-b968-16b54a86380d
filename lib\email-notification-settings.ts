import { getSettings } from "./use-settings";
import { adminClient } from "./supabase";

/**
 * Email notification settings interface
 */
export interface EmailNotificationSettings {
	emailNotificationsEnabled: boolean;
	bookingConfirmationEmailEnabled: boolean;
	adminEmailNotificationsEnabled: boolean;
}

/**
 * Get email notification settings from the database
 * Falls back to enabled (true) if settings are not found
 */
export async function getEmailNotificationSettings(): Promise<EmailNotificationSettings> {
	try {
		console.log("=== EMAIL NOTIFICATION SETTINGS CHECK START ===");
		
		const { data: settings, error } = await adminClient
			.from("business_settings")
			.select("key, value, value_type")
			.in("key", [
				"email_notifications_enabled",
				"booking_confirmation_email_enabled", 
				"admin_email_notifications_enabled"
			]);

		if (error) {
			console.error("Error fetching email notification settings:", error);
			console.log("Falling back to default settings (all enabled)");
			return {
				emailNotificationsEnabled: true,
				bookingConfirmationEmailEnabled: true,
				adminEmailNotificationsEnabled: true,
			};
		}

		// Parse settings with fallback to true
		const settingsMap: Record<string, boolean> = {};
		settings?.forEach((setting) => {
			let value = true; // Default fallback
			
			if (setting.value_type === "boolean") {
				value = setting.value === "true";
			}
			
			settingsMap[setting.key] = value;
		});

		const result = {
			emailNotificationsEnabled: settingsMap["email_notifications_enabled"] ?? true,
			bookingConfirmationEmailEnabled: settingsMap["booking_confirmation_email_enabled"] ?? true,
			adminEmailNotificationsEnabled: settingsMap["admin_email_notifications_enabled"] ?? true,
		};

		console.log("Email notification settings:", result);
		console.log("=== EMAIL NOTIFICATION SETTINGS CHECK END ===");
		
		return result;
	} catch (error) {
		console.error("Error getting email notification settings:", error);
		console.log("Falling back to default settings (all enabled)");
		return {
			emailNotificationsEnabled: true,
			bookingConfirmationEmailEnabled: true,
			adminEmailNotificationsEnabled: true,
		};
	}
}

/**
 * Check if customer emails should be sent
 * Checks both global email notifications and booking confirmation email settings
 */
export async function shouldSendCustomerEmail(emailType: 'booking_confirmation' | 'payment_confirmation' | 'booking_reminder'): Promise<boolean> {
	try {
		const settings = await getEmailNotificationSettings();
		
		console.log(`=== CUSTOMER EMAIL CHECK (${emailType}) ===`);
		console.log("Global email notifications enabled:", settings.emailNotificationsEnabled);
		
		// If global email notifications are disabled, don't send any emails
		if (!settings.emailNotificationsEnabled) {
			console.log("❌ Customer email blocked: Global email notifications disabled");
			return false;
		}

		// Check specific email type settings
		switch (emailType) {
			case 'booking_confirmation':
				console.log("Booking confirmation emails enabled:", settings.bookingConfirmationEmailEnabled);
				if (!settings.bookingConfirmationEmailEnabled) {
					console.log("❌ Booking confirmation email blocked: Setting disabled");
					return false;
				}
				break;
			case 'payment_confirmation':
			case 'booking_reminder':
				// These are always sent if global notifications are enabled
				// Could add specific settings for these in the future
				break;
		}

		console.log("✅ Customer email allowed");
		return true;
	} catch (error) {
		console.error("Error checking customer email settings:", error);
		console.log("⚠️ Falling back to allowing email due to error");
		return true; // Fallback to allowing emails if there's an error
	}
}

/**
 * Check if admin emails should be sent
 * Checks both global email notifications and admin email notification settings
 */
export async function shouldSendAdminEmail(emailType: 'new_reservation' | 'payment_received'): Promise<boolean> {
	try {
		const settings = await getEmailNotificationSettings();
		
		console.log(`=== ADMIN EMAIL CHECK (${emailType}) ===`);
		console.log("Global email notifications enabled:", settings.emailNotificationsEnabled);
		console.log("Admin email notifications enabled:", settings.adminEmailNotificationsEnabled);
		
		// If global email notifications are disabled, don't send any emails
		if (!settings.emailNotificationsEnabled) {
			console.log("❌ Admin email blocked: Global email notifications disabled");
			return false;
		}

		// If admin email notifications are disabled, don't send admin emails
		if (!settings.adminEmailNotificationsEnabled) {
			console.log("❌ Admin email blocked: Admin email notifications disabled");
			return false;
		}

		console.log("✅ Admin email allowed");
		return true;
	} catch (error) {
		console.error("Error checking admin email settings:", error);
		console.log("⚠️ Falling back to allowing email due to error");
		return true; // Fallback to allowing emails if there's an error
	}
}

/**
 * Get a single email notification setting by key
 */
export async function getEmailNotificationSetting(key: string): Promise<boolean> {
	try {
		const { data: setting, error } = await adminClient
			.from("business_settings")
			.select("value, value_type")
			.eq("key", key)
			.single();

		if (error || !setting) {
			console.log(`Email notification setting '${key}' not found, defaulting to true`);
			return true;
		}

		if (setting.value_type === "boolean") {
			return setting.value === "true";
		}

		return true;
	} catch (error) {
		console.error(`Error getting email notification setting '${key}':`, error);
		return true; // Fallback to enabled
	}
}

/**
 * Client-side hook for email notification settings
 * Uses the useSettings hook for real-time updates
 */
export function useEmailNotificationSettings() {
	// This would use the useSettings hook for client-side components
	// For now, we'll implement the server-side functions above
	// and add client-side support later if needed
	return {
		emailNotificationsEnabled: true,
		bookingConfirmationEmailEnabled: true,
		adminEmailNotificationsEnabled: true,
	};
}

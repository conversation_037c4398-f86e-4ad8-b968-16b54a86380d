#!/usr/bin/env node

// Simple admin creation script using existing dependencies
// Run with: node scripts/create-admin.js

const { createClient } = require("@supabase/supabase-js");

// Load environment variables from .env.local
const fs = require("fs");
const path = require("path");

function loadEnvFile() {
	const envPath = path.join(__dirname, "..", ".env.local");
	if (!fs.existsSync(envPath)) {
		console.error("❌ .env.local file not found");
		console.error("Please create .env.local with NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY");
		process.exit(1);
	}

	const envContent = fs.readFileSync(envPath, "utf8");
	const envVars = {};

	envContent.split("\n").forEach((line) => {
		const [key, ...valueParts] = line.split("=");
		if (key && valueParts.length > 0) {
			envVars[key.trim()] = valueParts.join("=").trim();
		}
	});

	return envVars;
}

const env = loadEnvFile();
const SUPABASE_URL = env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
	console.error("❌ Missing required environment variables in .env.local:");
	console.error("   NEXT_PUBLIC_SUPABASE_URL");
	console.error("   SUPABASE_SERVICE_ROLE_KEY");
	process.exit(1);
}

const readline = require("readline");

// Function to prompt for user input
function askQuestion(question) {
	const rl = readline.createInterface({
		input: process.stdin,
		output: process.stdout,
	});

	return new Promise((resolve) => {
		rl.question(question, (answer) => {
			rl.close();
			resolve(answer);
		});
	});
}

async function createAdmin() {
	console.log("🚀 Creating admin user...");
	console.log("");

	// Prompt for admin details
	const ADMIN_EMAIL =
		(await askQuestion("Enter admin email (default: <EMAIL>): ")) ||
		"<EMAIL>";
	const ADMIN_PASSWORD = (await askQuestion("Enter admin password (default: admin123): ")) || "admin123";
	const FIRST_NAME = (await askQuestion("Enter first name (default: Admin): ")) || "Admin";
	const LAST_NAME = (await askQuestion("Enter last name (default: User): ")) || "User";

	console.log("");
	console.log(`Creating admin user: ${FIRST_NAME} ${LAST_NAME} (${ADMIN_EMAIL})`);
	console.log("");

	try {
		// Create Supabase client with service role
		const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
			auth: {
				autoRefreshToken: false,
				persistSession: false,
			},
		});

		console.log("📧 Creating auth user...");

		// Create user in Supabase Auth with role in user_metadata
		const { data: authData, error: authError } = await supabase.auth.admin.createUser({
			email: ADMIN_EMAIL,
			password: ADMIN_PASSWORD,
			email_confirm: true,
			user_metadata: {
				first_name: FIRST_NAME,
				last_name: LAST_NAME,
				role: "admin",
			},
		});

		if (authError) {
			console.error("❌ Auth error:", authError.message);
			process.exit(1);
		}

		if (!authData.user) {
			console.error("❌ Failed to create user");
			process.exit(1);
		}

		console.log("✅ Auth user created successfully!");
		console.log("👤 Creating profile record...");

		// Create profile record in profiles table
		const { data: profileData, error: profileError } = await supabase
			.from("profiles")
			.insert({
				id: authData.user.id,
				email: ADMIN_EMAIL,
				first_name: FIRST_NAME,
				last_name: LAST_NAME,
				role: "admin",
			})
			.select()
			.single();

		if (profileError) {
			console.error("❌ Profile creation error:", profileError.message);
			console.log("⚠️  Auth user was created but profile creation failed.");
			console.log("   You may need to manually create the profile record.");
			process.exit(1);
		}

		console.log("✅ Profile created successfully!");
		console.log("✅ Admin user setup complete!");
		console.log("");
		console.log("📋 Login Details:");
		console.log(`   Email: ${ADMIN_EMAIL}`);
		console.log(`   Password: ${ADMIN_PASSWORD}`);
		console.log(`   Role: admin`);
		console.log(`   User ID: ${authData.user.id}`);
		console.log("");
		console.log("🌐 You can now login at: http://localhost:3000/login");
	} catch (error) {
		console.error("❌ Unexpected error:", error);
		process.exit(1);
	}
}

// Run the script
createAdmin().catch(console.error);

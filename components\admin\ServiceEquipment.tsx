"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { adminApi } from "@/lib/api-client";
import { Edit, Plus, Save, Settings, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";

interface Equipment {
	id: string;
	name: string;
	description: string;
	number_of_units: number;
	capacity_per_unit: number;
	is_active: boolean;
}

interface ServiceEquipmentRequirement {
	id: string;
	equipment_id: string;
	equipment: Equipment;
	capacity_per_participant: number;
}

interface ServiceEquipmentProps {
	serviceId: string;
	serviceName: string;
}

export default function ServiceEquipment({ serviceId, serviceName }: ServiceEquipmentProps) {
	const [equipment, setEquipment] = useState<Equipment[]>([]);
	const [serviceEquipment, setServiceEquipment] = useState<ServiceEquipmentRequirement[]>([]);
	const [loading, setLoading] = useState(true);
	const [editing, setEditing] = useState(false);
	const [saving, setSaving] = useState(false);
	const [editServiceEquipment, setEditServiceEquipment] = useState<
		{ equipmentId: string; id?: string; capacityPerParticipant?: number }[]
	>([]);

	useEffect(() => {
		fetchData();
	}, [serviceId]);

	const fetchData = async () => {
		try {
			// Fetch all equipment
			const equipmentResponse = await adminApi.getEquipment({ limit: 100 });
			if (equipmentResponse?.equipment) {
				setEquipment(equipmentResponse.equipment);
			}

			// Fetch service equipment requirements
			const serviceResponse = await adminApi.getService(serviceId);
			if (serviceResponse?.service?.service_equipment_requirements) {
				setServiceEquipment(serviceResponse.service.service_equipment_requirements);

				// Convert to edit format
				const editFormat = serviceResponse.service.service_equipment_requirements.map((req: any) => ({
					equipmentId: req.equipment.id,
					capacityPerParticipant: req.capacity_per_participant,
					id: req.id,
				}));
				setEditServiceEquipment(editFormat);
			}
		} catch (error) {
			console.error("Error fetching service equipment data:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleEdit = () => {
		setEditing(true);
		const editFormat = serviceEquipment.map((req) => ({
			equipmentId: req.equipment.id,
			capacityPerParticipant: req.capacity_per_participant,
			id: req.id,
		}));
		setEditServiceEquipment(editFormat);
	};

	const handleCancel = () => {
		setEditing(false);
		const editFormat = serviceEquipment.map((req) => ({
			equipmentId: req.equipment.id,
			capacityPerParticipant: req.capacity_per_participant,
			id: req.id,
		}));
		setEditServiceEquipment(editFormat);
	};

	const handleSave = async () => {
		try {
			setSaving(true);

			// Get current requirements
			const currentRequirements = new Set(serviceEquipment.map((req) => req.equipment.id));
			const newRequirements = new Set(
				editServiceEquipment.filter((req) => req.equipmentId).map((req) => req.equipmentId)
			);

			// Remove requirements that are no longer selected
			for (const req of serviceEquipment) {
				if (!newRequirements.has(req.equipment.id)) {
					try {
						await adminApi.deleteServiceEquipmentRequirement(req.id);
					} catch (error) {
						console.error("Error removing equipment requirement:", error);
					}
				}
			}

			// Add or update requirements
			for (const editReq of editServiceEquipment) {
				if (!editReq.equipmentId) continue;

				const existingReq = serviceEquipment.find((req) => req.equipment.id === editReq.equipmentId);

				if (existingReq) {
					// Update existing requirement if capacity changed
					if (existingReq.capacity_per_participant !== editReq.capacityPerParticipant) {
						try {
							await adminApi.updateServiceEquipmentRequirement(existingReq.id, {
								capacity_per_participant: editReq.capacityPerParticipant,
							});
						} catch (error) {
							console.error("Error updating equipment requirement:", error);
						}
					}
				} else {
					// Create new requirement
					try {
						await adminApi.createServiceEquipmentRequirement({
							service_id: serviceId,
							equipment_id: editReq.equipmentId,
							capacity_per_participant: editReq.capacityPerParticipant,
						});
					} catch (error) {
						console.error("Error creating equipment requirement:", error);
					}
				}
			}

			// Refresh data
			await fetchData();
			setEditing(false);
		} catch (error) {
			console.error("Error saving equipment requirements:", error);
		} finally {
			setSaving(false);
		}
	};

	const addEquipmentRequirement = () => {
		setEditServiceEquipment([...editServiceEquipment, { equipmentId: "", capacityPerParticipant: 1 }]);
	};

	const removeEquipmentRequirement = (index: number) => {
		setEditServiceEquipment(editServiceEquipment.filter((_, i) => i !== index));
	};

	const updateEquipmentRequirement = (index: number, field: string, value: any) => {
		const updated = [...editServiceEquipment];
		updated[index] = { ...updated[index], [field]: value };
		setEditServiceEquipment(updated);
	};

	if (loading) {
		return <div className="p-4">Chargement...</div>;
	}

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<div>
					<h3 className="text-lg font-semibold">Gestion des équipements</h3>
					<p className="text-sm text-gray-600">Service: {serviceName}</p>
				</div>
				{!editing && (
					<Button onClick={handleEdit} className="flex items-center gap-2">
						<Edit className="w-4 h-4" />
						Modifier
					</Button>
				)}
			</div>

			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Settings className="w-5 h-5" />
						Équipements requis
					</CardTitle>
				</CardHeader>
				<CardContent>
					{editing ? (
						<div className="space-y-4">
							<Label>Configurer les équipements nécessaires</Label>

							<div className="space-y-3">
								{editServiceEquipment.map((item, index) => (
									<div
										key={index}
										className="flex items-center gap-3 p-3 border border-gray-300 rounded-lg"
									>
										<select
											value={item.equipmentId}
											onChange={(e) =>
												updateEquipmentRequirement(index, "equipmentId", e.target.value)
											}
											className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
										>
											<option value="">Sélectionner un équipement</option>
											{equipment
												.filter((eq) => eq.is_active)
												.map((eq) => (
													<option key={eq.id} value={eq.id}>
														{eq.name} (Capacité: {eq.number_of_units * eq.capacity_per_unit}
														)
													</option>
												))}
										</select>
										<div className="flex items-center gap-2">
											<Label className="text-sm text-gray-600">Capacité/participant:</Label>
											<Input
												type="number"
												min="1"
												value={item.capacityPerParticipant}
												onChange={(e) =>
													updateEquipmentRequirement(
														index,
														"capacityPerParticipant",
														parseInt(e.target.value) || 1
													)
												}
												className="w-20"
											/>
										</div>
										<Button
											variant="outline"
											size="sm"
											onClick={() => removeEquipmentRequirement(index)}
											className="text-red-600 hover:text-red-800"
										>
											<Trash2 className="w-4 h-4" />
										</Button>
									</div>
								))}
							</div>

							<Button
								variant="outline"
								onClick={addEquipmentRequirement}
								className="flex items-center gap-2"
							>
								<Plus className="w-4 h-4" />
								Ajouter un équipement
							</Button>

							<p className="text-xs text-gray-500">
								Définissez les équipements nécessaires et leur capacité par participant
							</p>

							{/* Action buttons */}
							<div className="flex gap-2 pt-4 border-t">
								<Button onClick={handleSave} disabled={saving} className="flex items-center gap-2">
									<Save className="w-4 h-4" />
									{saving ? "Sauvegarde..." : "Sauvegarder"}
								</Button>
								<Button variant="outline" onClick={handleCancel} disabled={saving}>
									<X className="w-4 h-4" />
									Annuler
								</Button>
							</div>
						</div>
					) : (
						<div className="space-y-4">
							{serviceEquipment.length > 0 ? (
								<div className="grid grid-cols-1 md:grid-cols-2 gap-3">
									{serviceEquipment.map((req) => (
										<div
											key={req.id}
											className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg"
										>
											<div className="flex-1">
												<div className="font-medium">{req.equipment.name}</div>
												<div className="text-sm text-gray-500">{req.equipment.description}</div>
												<div className="text-xs text-gray-400">
													Capacité totale:{" "}
													{req.equipment.number_of_units * req.equipment.capacity_per_unit}
												</div>
											</div>
											<Badge variant="outline" className="text-xs">
												{req.capacity_per_participant}/participant
											</Badge>
										</div>
									))}
								</div>
							) : (
								<div className="text-center py-8 text-gray-500">
									<Settings className="w-12 h-12 mx-auto mb-3 text-gray-300" />
									<p>Aucun équipement requis</p>
									<p className="text-sm">Cliquez sur "Modifier" pour ajouter des équipements</p>
								</div>
							)}
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}

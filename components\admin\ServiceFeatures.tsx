"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { adminApi } from "@/lib/api-client";
import { Edit, Plus, Save, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";

interface ServiceFeaturesProps {
	serviceId: string;
	serviceName: string;
}

export default function ServiceFeatures({ serviceId, serviceName }: ServiceFeaturesProps) {
	const [features, setFeatures] = useState<string[]>([]);
	const [loading, setLoading] = useState(true);
	const [editing, setEditing] = useState(false);
	const [saving, setSaving] = useState(false);
	const [editFeatures, setEditFeatures] = useState<string[]>([]);
	const [newFeature, setNewFeature] = useState("");

	useEffect(() => {
		fetchFeatures();
	}, [serviceId]);

	const fetchFeatures = async () => {
		try {
			const data = await adminApi.getService(serviceId);
			let serviceFeatures: string[] = [];

			// Handle different possible formats of features
			if (Array.isArray(data.service.features)) {
				serviceFeatures = data.service.features;
			} else if (data.service.features && typeof data.service.features === "object") {
				// If it's an object, try to extract array values
				serviceFeatures = Object.values(data.service.features).filter((f) => typeof f === "string");
			} else if (typeof data.service.features === "string") {
				// If it's a string, try to parse as JSON
				try {
					const parsed = JSON.parse(data.service.features);
					serviceFeatures = Array.isArray(parsed) ? parsed : [];
				} catch {
					serviceFeatures = [data.service.features];
				}
			}

			setFeatures(serviceFeatures);
			setEditFeatures([...serviceFeatures]);
		} catch (error) {
			console.error("Error fetching service features:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleEdit = () => {
		setEditing(true);
		setEditFeatures([...features]);
	};

	const handleCancel = () => {
		setEditing(false);
		setEditFeatures([...features]);
		setNewFeature("");
	};

	const handleSave = async () => {
		try {
			setSaving(true);

			// Filter out empty features
			const cleanFeatures = editFeatures.filter((feature) => feature.trim() !== "");

			await adminApi.updateService(serviceId, {
				features: cleanFeatures,
			});

			setFeatures(cleanFeatures);
			setEditing(false);
			setNewFeature("");
		} catch (error) {
			console.error("Error saving features:", error);
		} finally {
			setSaving(false);
		}
	};

	const addFeature = () => {
		if (newFeature.trim()) {
			setEditFeatures([...editFeatures, newFeature.trim()]);
			setNewFeature("");
		}
	};

	const removeFeature = (index: number) => {
		setEditFeatures(editFeatures.filter((_, i) => i !== index));
	};

	const updateFeature = (index: number, value: string) => {
		const updated = [...editFeatures];
		updated[index] = value;
		setEditFeatures(updated);
	};

	if (loading) {
		return <div className="p-4">Chargement...</div>;
	}

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<div>
					<h3 className="text-lg font-semibold">Caractéristiques du service</h3>
					<p className="text-sm text-gray-600">Service: {serviceName}</p>
				</div>
				{!editing && (
					<Button onClick={handleEdit} className="flex items-center gap-2">
						<Edit className="w-4 h-4" />
						Modifier
					</Button>
				)}
			</div>

			<Card>
				<CardHeader>
					<CardTitle>Caractéristiques incluses</CardTitle>
				</CardHeader>
				<CardContent>
					{editing ? (
						<div className="space-y-4">
							{/* Existing features editing */}
							<div className="space-y-3">
								{editFeatures.map((feature, index) => (
									<div key={index} className="flex items-center gap-2">
										<Input
											value={feature}
											onChange={(e) => updateFeature(index, e.target.value)}
											placeholder="Caractéristique du service"
											className="flex-1"
										/>
										<Button
											variant="outline"
											size="sm"
											onClick={() => removeFeature(index)}
											className="text-red-600 hover:text-red-800"
										>
											<Trash2 className="w-4 h-4" />
										</Button>
									</div>
								))}
							</div>

							{/* Add new feature */}
							<div className="flex items-center gap-2 pt-4 border-t">
								<Input
									value={newFeature}
									onChange={(e) => setNewFeature(e.target.value)}
									placeholder="Nouvelle caractéristique"
									className="flex-1"
									onKeyPress={(e) => {
										if (e.key === "Enter") {
											addFeature();
										}
									}}
								/>
								<Button variant="outline" size="sm" onClick={addFeature} disabled={!newFeature.trim()}>
									<Plus className="w-4 h-4" />
								</Button>
							</div>

							{/* Action buttons */}
							<div className="flex gap-2 pt-4 border-t">
								<Button onClick={handleSave} disabled={saving} className="flex items-center gap-2">
									<Save className="w-4 h-4" />
									{saving ? "Sauvegarde..." : "Sauvegarder"}
								</Button>
								<Button variant="outline" onClick={handleCancel} disabled={saving}>
									<X className="w-4 h-4" />
									Annuler
								</Button>
							</div>
						</div>
					) : (
						<div className="space-y-4">
							{features.length > 0 ? (
								<div className="grid grid-cols-1 md:grid-cols-2 gap-2">
									{features.map((feature, index) => (
										<Badge key={index} variant="outline" className="justify-start p-2">
											{feature}
										</Badge>
									))}
								</div>
							) : (
								<div className="text-center py-8 text-gray-500">
									<p>Aucune caractéristique définie</p>
									<p className="text-sm">Cliquez sur "Modifier" pour ajouter des caractéristiques</p>
								</div>
							)}
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}

import { NextRequest, NextResponse } from "next/server";
import { supabaseAdmin } from "@/lib/supabase";

export async function POST(request: NextRequest) {
	try {
		const { reservationId, method } = await request.json();

		console.log("=== UPDATE SECURITY DEPOSIT METHOD DEBUG ===");
		console.log("Request body:", { reservationId, method });

		if (!reservationId) {
			console.log("ERROR: No reservation ID provided");
			return NextResponse.json({ error: "Reservation ID is required" }, { status: 400 });
		}

		if (!method || !["authorization", "manual"].includes(method)) {
			console.log("ERROR: Invalid method provided");
			return NextResponse.json({ error: "Valid method is required (authorization or manual)" }, { status: 400 });
		}

		if (!supabaseAdmin) {
			console.log("ERROR: Database connection not available");
			return NextResponse.json({ error: "Database connection not available" }, { status: 500 });
		}

		// Update reservation with security deposit method
		const { data: reservation, error: updateError } = await supabaseAdmin
			.from("reservations")
			.update({
				security_deposit_method: method,
				security_deposit_status: method === "manual" ? "none" : "pending",
				updated_at: new Date().toISOString(),
			})
			.eq("id", reservationId)
			.select()
			.single();

		if (updateError) {
			console.error("Error updating reservation:", updateError);
			return NextResponse.json({ error: "Failed to update reservation" }, { status: 500 });
		}

		console.log("Successfully updated security deposit method:", reservation);

		return NextResponse.json({
			success: true,
			reservation,
		});
	} catch (error) {
		console.error("Error in update security deposit method:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}

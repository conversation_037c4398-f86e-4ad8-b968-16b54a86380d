"use client";

import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { apiClient } from "@/lib/api-client";

interface OptionSelection {
	id: string;
	option: {
		id: string;
		name: string;
		description?: string;
	};
	group?: {
		id: string;
		name: string;
	};
	quantity: number;
	unitPrice: number;
	totalPrice: number;
}

interface ReservationOptionsDisplayProps {
	reservationId: string;
}

export default function ReservationOptionsDisplay({ reservationId }: ReservationOptionsDisplayProps) {
	const [optionSelections, setOptionSelections] = useState<OptionSelection[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		loadOptionSelections();
	}, [reservationId]);

	const loadOptionSelections = async () => {
		try {
			setLoading(true);
			const data = await apiClient.get(`/api/admin/reservations/${reservationId}`);
			const reservation = data.reservation;
			console.log("Reservation data:", reservation);

			// Convert selected_options from reservation to the expected format
			if (reservation.selected_options && Array.isArray(reservation.selected_options)) {
				console.log("Selected options data:", reservation.selected_options);
				// Check if we have the new format with full details or old format with just IDs
				const firstOption = reservation.selected_options[0];

				if (firstOption && firstOption.optionName) {
					// New format with full details
					const formattedSelections = reservation.selected_options.map((option: any, index: number) => ({
						id: `option-${index}`,
						option: {
							id: option.optionId,
							name: option.optionName,
							description: null,
						},
						quantity: option.quantity,
						unitPrice: option.unitPrice,
						totalPrice: option.totalPrice,
					}));
					setOptionSelections(formattedSelections);
				} else if (firstOption && typeof firstOption === "object" && firstOption.optionId) {
					// Old format with just optionId and quantity - need to fetch option details
					try {
						const optionIds = reservation.selected_options
							.map((opt: any) => opt.optionId)
							.filter((id: any) => typeof id === "string" && id.trim() !== "");

						if (optionIds.length === 0) {
							setOptionSelections([]);
							return;
						}

						const optionsData = await apiClient.get(
							`/api/admin/service-options?ids=${optionIds.join(",")}`
						);
						const optionsMap = new Map(optionsData.options.map((opt: any) => [opt.id, opt]));

						// Get service assignments to get custom pricing
						const serviceId = reservation.service_id || reservation.service?.id;
						if (!serviceId) {
							console.error("No service ID found in reservation data");
							setOptionSelections([]);
							return;
						}

						const serviceAssignments = await apiClient.get(
							`/api/admin/services/${serviceId}/option-assignments`
						);
						const assignmentsMap = new Map(
							serviceAssignments.assignments.map((a: any) => [a.option.id, a])
						);

						const formattedSelections = reservation.selected_options
							.map((selection: any, index: number) => {
								const option: any = optionsMap.get(selection.optionId);
								const assignment: any = assignmentsMap.get(selection.optionId);

								if (option) {
									const unitPrice = assignment?.option?.customPrice ?? option.base_price ?? 0;
									return {
										id: `option-${index}`,
										option: {
											id: option.id,
											name: option.name,
											description: option.description,
										},
										quantity: selection.quantity,
										unitPrice: unitPrice,
										totalPrice: unitPrice * selection.quantity,
									};
								}
								return null;
							})
							.filter(Boolean);

						setOptionSelections(formattedSelections);
					} catch (optionError) {
						console.error("Error fetching option details:", optionError);
						setError("Erreur lors du chargement des détails des options");
					}
				} else {
					setOptionSelections([]);
				}
			} else {
				setOptionSelections([]);
			}
		} catch (error) {
			console.error("Error loading option selections:", error);
			setError("Erreur lors du chargement des options");
		} finally {
			setLoading(false);
		}
	};

	if (loading) {
		return (
			<Card>
				<CardHeader>
					<CardTitle>Options sélectionnées</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex items-center justify-center py-4">
						<Loader2 className="h-6 w-6 animate-spin" />
					</div>
				</CardContent>
			</Card>
		);
	}

	if (error) {
		return (
			<Card>
				<CardHeader>
					<CardTitle>Options sélectionnées</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-red-600 text-sm">{error}</div>
				</CardContent>
			</Card>
		);
	}

	if (optionSelections.length === 0) {
		return (
			<Card>
				<CardHeader>
					<CardTitle>Options sélectionnées</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-gray-500 text-sm">Aucune option sélectionnée</div>
				</CardContent>
			</Card>
		);
	}

	const totalOptionsPrice = optionSelections.reduce((sum, selection) => sum + selection.totalPrice, 0);

	return (
		<Card>
			<CardHeader>
				<CardTitle>Options sélectionnées</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-3">
					{optionSelections.map((selection) => (
						<div key={selection.id} className="flex items-center justify-between p-3 border rounded-lg">
							<div className="flex-1">
								<div className="flex items-center gap-2">
									<h4 className="font-medium text-gray-900">{selection.option.name}</h4>
									{selection.group && (
										<Badge variant="outline" className="text-xs">
											{selection.group.name}
										</Badge>
									)}
								</div>
								{selection.option.description && (
									<p className="text-sm text-gray-600 mt-1">{selection.option.description}</p>
								)}
								{selection.quantity > 1 && (
									<p className="text-sm text-gray-500">
										Quantité: {selection.quantity} × {selection.unitPrice}€
									</p>
								)}
							</div>
							<div className="text-right">
								<span className="font-medium text-gray-900">{selection.totalPrice}€</span>
							</div>
						</div>
					))}

					{optionSelections.length > 1 && (
						<div className="border-t pt-3 mt-3">
							<div className="flex justify-between items-center">
								<span className="font-medium text-gray-900">Total options:</span>
								<span className="font-semibold text-lg text-gray-900">{totalOptionsPrice}€</span>
							</div>
						</div>
					)}
				</div>
			</CardContent>
		</Card>
	);
}

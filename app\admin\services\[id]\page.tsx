"use client";

export const dynamic = "force-dynamic";

import AdminLayout from "@/components/admin/AdminLayout";
import ServiceBlackoutDates from "@/components/admin/ServiceBlackoutDates";
import ServiceEdit from "@/components/admin/ServiceEdit";
import ServiceEmployees from "@/components/admin/ServiceEmployees";
import ServiceEquipment from "@/components/admin/ServiceEquipment";
import ServiceSchedulingRules from "@/components/admin/ServiceSchedulingRules";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { adminApi } from "@/lib/api-client";
import { AlertTriangle, ArrowLeft, Clock, Edit, Settings } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Service {
	id: string;
	name: string;
	description: string;
	duration_minutes: number;
	max_participants: number;
	base_price: number;
	is_active: boolean;
	requires_employee?: boolean;
}

export default function ServiceDetailPage() {
	const params = useParams();
	const router = useRouter();
	const serviceId = params.id as string;

	const [service, setService] = useState<Service | null>(null);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		fetchService();
	}, [serviceId]);

	const fetchService = async () => {
		try {
			const data = await adminApi.getService(serviceId);
			setService(data.service);
		} catch (error) {
			console.error("Error fetching service:", error);
		} finally {
			setLoading(false);
		}
	};

	if (loading) {
		return (
			<AdminLayout>
				<div className="space-y-6">
					<div className="flex items-center gap-4">
						<Button
							variant="outline"
							onClick={() => router.push("/admin/services")}
							className="flex items-center gap-2"
						>
							<ArrowLeft className="w-4 h-4" />
							Retour
						</Button>
						<div>
							<h1 className="text-2xl font-bold text-gray-900">Chargement...</h1>
							<p className="text-gray-600">Récupération des informations du service</p>
						</div>
					</div>

					{/* Loading skeleton */}
					<div className="space-y-6">
						<Card>
							<CardContent className="p-6">
								<div className="animate-pulse space-y-4">
									<div className="h-4 bg-gray-200 rounded w-1/4"></div>
									<div className="grid grid-cols-2 md:grid-cols-5 gap-4">
										{[...Array(5)].map((_, i) => (
											<div key={i} className="space-y-2">
												<div className="h-3 bg-gray-200 rounded w-3/4"></div>
												<div className="h-6 bg-gray-200 rounded w-1/2"></div>
											</div>
										))}
									</div>
								</div>
							</CardContent>
						</Card>

						<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
							{[...Array(3)].map((_, i) => (
								<Card key={i}>
									<CardContent className="p-6">
										<div className="animate-pulse space-y-4">
											<div className="h-4 bg-gray-200 rounded w-1/3"></div>
											<div className="h-20 bg-gray-200 rounded"></div>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					</div>
				</div>
			</AdminLayout>
		);
	}

	if (!service) {
		return (
			<div className="p-6">
				<div className="text-center">
					<h1 className="text-2xl font-bold text-gray-900 mb-4">Service non trouvé</h1>
					<Button onClick={() => router.push("/admin/services")}>Retour aux services</Button>
				</div>
			</div>
		);
	}

	return (
		<AdminLayout>
			<div className="space-y-6">
				{/* Header */}
				<div className="flex items-center gap-4">
					<Button
						variant="outline"
						onClick={() => router.push("/admin/services")}
						className="flex items-center gap-2"
					>
						<ArrowLeft className="w-4 h-4" />
						Retour
					</Button>
					<div>
						<h1 className="text-2xl font-bold text-gray-900">{service.name}</h1>
						<p className="text-gray-600">Gestion des paramètres de disponibilité</p>
					</div>
				</div>

				{/* Service Info Card */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Settings className="w-5 h-5" />
							Informations du service
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-2 md:grid-cols-5 gap-4">
							<div>
								<label className="text-sm font-medium text-gray-500">Durée</label>
								<p className="text-lg font-semibold">{service.duration_minutes} min</p>
							</div>
							<div>
								<label className="text-sm font-medium text-gray-500">Participants max</label>
								<p className="text-lg font-semibold">{service.max_participants}</p>
							</div>
							<div>
								<label className="text-sm font-medium text-gray-500">Prix de base</label>
								<p className="text-lg font-semibold">{service.base_price}€</p>
							</div>
							<div>
								<label className="text-sm font-medium text-gray-500">Nécessite employé</label>
								<p
									className={`text-lg font-semibold ${
										service.requires_employee ? "text-blue-600" : "text-gray-600"
									}`}
								>
									{service.requires_employee ? "Oui" : "Non"}
								</p>
							</div>
							<div>
								<label className="text-sm font-medium text-gray-500">Statut</label>
								<p
									className={`text-lg font-semibold ${
										service.is_active ? "text-green-600" : "text-red-600"
									}`}
								>
									{service.is_active ? "Actif" : "Inactif"}
								</p>
							</div>
						</div>
						{service.description && (
							<div className="mt-4">
								<label className="text-sm font-medium text-gray-500">Description</label>
								<p className="text-gray-700 mt-1">{service.description}</p>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Service Management Tabs */}
				<Tabs defaultValue="edit" className="space-y-6">
					<TabsList className="grid w-full grid-cols-5">
						<TabsTrigger value="edit" className="flex items-center gap-2">
							<Edit className="w-4 h-4" />
							Modifier
						</TabsTrigger>
						<TabsTrigger value="employees" className="flex items-center gap-2">
							<Settings className="w-4 h-4" />
							Employés
						</TabsTrigger>
						<TabsTrigger value="equipment" className="flex items-center gap-2">
							<Settings className="w-4 h-4" />
							Équipements
						</TabsTrigger>
						<TabsTrigger value="scheduling" className="flex items-center gap-2">
							<Clock className="w-4 h-4" />
							Planification
						</TabsTrigger>
						<TabsTrigger value="blackouts" className="flex items-center gap-2">
							<AlertTriangle className="w-4 h-4" />
							Indisponibilités
						</TabsTrigger>
					</TabsList>

					<TabsContent value="edit">
						<ServiceEdit serviceId={serviceId} />
					</TabsContent>

					<TabsContent value="employees">
						<ServiceEmployees serviceId={serviceId} serviceName={service.name} />
					</TabsContent>

					<TabsContent value="equipment">
						<ServiceEquipment serviceId={serviceId} serviceName={service.name} />
					</TabsContent>

					<TabsContent value="scheduling">
						<ServiceSchedulingRules
							serviceId={serviceId}
							serviceName={service.name}
							serviceDuration={service.duration_minutes}
						/>
					</TabsContent>

					<TabsContent value="blackouts">
						<ServiceBlackoutDates serviceId={serviceId} serviceName={service.name} />
					</TabsContent>
				</Tabs>
			</div>
		</AdminLayout>
	);
}

import { supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// GET /api/health - Health check endpoint
export async function GET(request: NextRequest) {
	try {
		const health = {
			status: "ok",
			timestamp: new Date().toISOString(),
			environment: process.env.NODE_ENV,
			checks: {
				supabaseConfig: {
					url: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
					anonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
					serviceRoleKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
				},
				supabaseAdmin: {
					available: supabaseAdmin !== null,
				},
			},
		};

		// Test database connection if supabaseAdmin is available
		if (supabaseAdmin) {
			try {
				const { data, error } = await supabaseAdmin.from("profiles").select("count").limit(1);
				(health.checks.supabaseAdmin as any).databaseConnection = !error;
				(health.checks.supabaseAdmin as any).error = error?.message || null;
			} catch (dbError) {
				(health.checks.supabaseAdmin as any).databaseConnection = false;
				(health.checks.supabaseAdmin as any).error =
					dbError instanceof Error ? dbError.message : "Unknown database error";
			}
		}

		const allChecksPass =
			health.checks.supabaseConfig.url &&
			health.checks.supabaseConfig.anonKey &&
			health.checks.supabaseConfig.serviceRoleKey &&
			health.checks.supabaseAdmin.available;

		return NextResponse.json(health, {
			status: allChecksPass ? 200 : 500,
		});
	} catch (error) {
		return NextResponse.json(
			{
				status: "error",
				timestamp: new Date().toISOString(),
				error: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}

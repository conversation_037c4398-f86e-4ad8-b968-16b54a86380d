"use client";

import { useState, useEffect } from "react";
import { Calendar, Clock, Loader2, AlertCircle } from "lucide-react";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";

interface TimeSlot {
	id?: string;
	start_time: string;
	end_time: string;
	available_capacity: number;
	is_available: boolean;
	total_capacity: number;
	booked_participants: number;
}

interface Service {
	id: string;
	name: string;
	duration_minutes: number;
}

interface TimeSlotSelectorProps {
	service: Service | null;
	selectedDate: Date | null;
	selectedTimeSlot: TimeSlot | null;
	participantCount: number;
	onDateSelect: (date: Date | null) => void;
	onTimeSlotSelect: (timeSlot: TimeSlot | null) => void;
	disabled?: boolean;
}

export default function TimeSlotSelector({
	service,
	selectedDate,
	selectedTimeSlot,
	participantCount,
	onDateSelect,
	onTimeSlotSelect,
	disabled = false,
}: TimeSlotSelectorProps) {
	const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Load time slots when service, date, or participant count changes
	useEffect(() => {
		if (service && selectedDate && participantCount > 0) {
			loadTimeSlots();
		} else {
			setTimeSlots([]);
			onTimeSlotSelect(null);
		}
	}, [service, selectedDate, participantCount]);

	const loadTimeSlots = async () => {
		if (!service || !selectedDate) return;

		setLoading(true);
		setError(null);
		onTimeSlotSelect(null);

		try {
			// Format date for API call
			const year = selectedDate.getFullYear();
			const month = String(selectedDate.getMonth() + 1).padStart(2, "0");
			const day = String(selectedDate.getDate()).padStart(2, "0");
			const dateString = `${year}-${month}-${day}`;

			const response = await fetch(
				`/api/services/${service.id}/timeslots?date=${dateString}&participants=${participantCount}`
			);

			if (!response.ok) {
				throw new Error("Erreur lors du chargement des créneaux");
			}

			const data = await response.json();
			console.log("API Response:", data); // Debug log

			// Add timeSlotId to each slot for admin booking compatibility
			const slotsWithIds = (data.data || []).map((slot: TimeSlot) => {
				// Extract time from start_time (format: YYYY-MM-DDTHH:MM:SS.sssZ)
				const startTime = new Date(slot.start_time);
				const timeStr = startTime.toISOString().substring(11, 16); // HH:MM

				return {
					...slot,
					id: `${dateString}_${timeStr}`, // Format expected by admin API: date_time
				};
			});

			setTimeSlots(slotsWithIds);
		} catch (err) {
			console.error("Error loading time slots:", err);
			setError("Erreur lors du chargement des créneaux disponibles");
			setTimeSlots([]);
		} finally {
			setLoading(false);
		}
	};

	const formatTime = (timeString: string) => {
		try {
			const date = new Date(timeString);
			return date.toLocaleTimeString("fr-FR", {
				hour: "2-digit",
				minute: "2-digit",
				timeZone: "UTC",
			});
		} catch {
			return timeString;
		}
	};

	const formatTimeRange = (startTime: string, endTime: string) => {
		return `${formatTime(startTime)} - ${formatTime(endTime)}`;
	};

	const formatDate = (date: Date) => {
		return date.toLocaleDateString("fr-FR", {
			weekday: "long",
			year: "numeric",
			month: "long",
			day: "numeric",
		});
	};

	const isDateDisabled = (date: Date) => {
		// Disable past dates
		const today = new Date();
		today.setHours(0, 0, 0, 0);
		return date < today;
	};

	return (
		<div className="space-y-6">
			{/* Date Selection */}
			<div>
				<label className="block text-sm font-medium text-gray-700 mb-3">
					Date <span className="text-red-500">*</span>
				</label>

				<div className="flex justify-center">
					<CalendarComponent
						mode="single"
						selected={selectedDate || undefined}
						onSelect={(date) => onDateSelect(date || null)}
						disabled={disabled ? () => true : isDateDisabled}
						initialFocus
						fromDate={new Date()}
						toDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))}
						className="rounded-md border"
					/>
				</div>
			</div>

			{/* Time Slot Selection */}
			{service && selectedDate && (
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-3">
						Créneau horaire <span className="text-red-500">*</span>
					</label>

					{selectedTimeSlot && (
						<div className="mb-4 p-3 bg-emerald-50 border border-emerald-200 rounded-lg">
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-2">
									<Clock className="h-5 w-5 text-emerald-600" />
									<span className="font-medium text-gray-900">
										{formatTimeRange(selectedTimeSlot.start_time, selectedTimeSlot.end_time)}
									</span>
									<span className="text-sm text-gray-600">
										({selectedTimeSlot.available_capacity} places disponibles)
									</span>
								</div>
								<button
									onClick={() => onTimeSlotSelect(null)}
									className="text-gray-400 hover:text-gray-600"
									disabled={disabled}
								>
									✕
								</button>
							</div>
						</div>
					)}

					{!selectedTimeSlot && (
						<div className="space-y-3">
							{loading ? (
								<div className="flex items-center justify-center py-8">
									<Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
									<span className="ml-2 text-gray-600">Chargement des créneaux...</span>
								</div>
							) : error ? (
								<div className="bg-red-50 border border-red-200 rounded-lg p-4">
									<div className="flex items-center">
										<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
										<span className="text-red-700">{error}</span>
									</div>
									<button
										onClick={loadTimeSlots}
										className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
									>
										Réessayer
									</button>
								</div>
							) : timeSlots.length === 0 ? (
								<div className="text-center py-8 text-gray-500">
									<Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
									<p>Aucun créneau disponible pour cette date</p>
									<p className="text-sm">
										Essayez une autre date ou réduisez le nombre de participants
									</p>
								</div>
							) : (
								<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-64 overflow-y-auto">
									{timeSlots.map((slot, index) => (
										<button
											key={`${slot.start_time}-${slot.end_time}-${index}`}
											onClick={() => !disabled && slot.is_available && onTimeSlotSelect(slot)}
											disabled={disabled || !slot.is_available}
											className={`p-3 border rounded-lg text-left transition-colors ${
												slot.is_available
													? "border-gray-200 hover:border-emerald-300 hover:bg-emerald-50"
													: "border-gray-100 bg-gray-50 text-gray-400 cursor-not-allowed"
											}`}
										>
											<div className="flex items-center justify-between">
												<div>
													<div className="font-medium">
														{formatTimeRange(slot.start_time, slot.end_time)}
													</div>
													<div className="text-sm text-gray-600">
														{slot.available_capacity} places disponibles
													</div>
												</div>
												{!slot.is_available && (
													<div className="text-xs text-red-500">
														{slot.available_capacity < participantCount &&
															"Pas assez de places"}
													</div>
												)}
											</div>
										</button>
									))}
								</div>
							)}
						</div>
					)}
				</div>
			)}

			{/* Participant Count Info */}
			{service && selectedDate && (
				<div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
					<div className="flex items-center space-x-2 text-blue-800">
						<AlertCircle className="h-4 w-4" />
						<span className="text-sm">
							Recherche de créneaux pour {participantCount} participant{participantCount > 1 ? "s" : ""}
						</span>
					</div>
				</div>
			)}
		</div>
	);
}

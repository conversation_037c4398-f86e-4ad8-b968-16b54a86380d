"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { adminApi } from "@/lib/api-client";
import type { EmployeeAvailability } from "@/lib/types/employees";
import { Clock, Edit, Plus, Save, Trash2, User, X } from "lucide-react";
import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";

const DAYS_OF_WEEK = [
	{ value: 0, label: "Dimanche" },
	{ value: 1, label: "<PERSON><PERSON>" },
	{ value: 2, label: "<PERSON><PERSON>" },
	{ value: 3, label: "<PERSON><PERSON><PERSON><PERSON>" },
	{ value: 4, label: "<PERSON><PERSON>" },
	{ value: 5, label: "Vendredi" },
	{ value: 6, label: "Samedi" },
];

interface Employee {
	id: string;
	first_name: string;
	last_name: string;
	email: string;
}

export function EmployeeAvailabilityManager() {
	const [employees, setEmployees] = useState<Employee[]>([]);
	const [employeeAvailability, setEmployeeAvailability] = useState<Record<string, EmployeeAvailability["Row"][]>>({});
	const [loading, setLoading] = useState(true);
	const [addingToEmployee, setAddingToEmployee] = useState<string | null>(null);
	const [editing, setEditing] = useState<string | null>(null);
	const [formData, setFormData] = useState<Partial<EmployeeAvailability["Insert"]>>({});

	useEffect(() => {
		fetchData();
	}, []);

	const fetchData = async () => {
		try {
			const [empData] = await Promise.all([adminApi.getEmployees({ limit: 100 })]);

			const employees = empData.data || [];
			setEmployees(employees);

			// Fetch availability for all employees
			const {
				data: { session },
			} = await supabase.auth.getSession();

			const availabilityPromises = employees.map(async (employee: Employee) => {
				try {
					const headers: Record<string, string> = { "Content-Type": "application/json" };
					if (session?.access_token) {
						headers["Authorization"] = `Bearer ${session.access_token}`;
					}

					const response = await fetch(`/api/admin/employees/${employee.id}/availability`, {
						headers,
					});
					if (response.ok) {
						const data = await response.json();
						return { employeeId: employee.id, availability: data.data || [] };
					}
				} catch (error) {
					console.error(`Error fetching availability for employee ${employee.id}:`, error);
				}
				return { employeeId: employee.id, availability: [] };
			});

			const availabilityResults = await Promise.all(availabilityPromises);
			const availabilityMap: Record<string, EmployeeAvailability["Row"][]> = {};
			availabilityResults.forEach((result) => {
				availabilityMap[result.employeeId] = result.availability;
			});
			setEmployeeAvailability(availabilityMap);
		} catch (error) {
			console.error("Error fetching data:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleAddAvailability = (employeeId: string) => {
		setAddingToEmployee(employeeId);
		setFormData({
			employee_id: employeeId,
			day_of_week: 1,
			start_time: "08:00",
			end_time: "18:00",
			is_available: true,
		});
	};

	const handleEdit = (item: EmployeeAvailability["Row"]) => {
		setEditing(item.id);
		setFormData({ ...item });
	};

	const handleSave = async () => {
		try {
			// Get current session for auth
			const {
				data: { session },
			} = await supabase.auth.getSession();

			const headers: Record<string, string> = { "Content-Type": "application/json" };
			if (session?.access_token) {
				headers["Authorization"] = `Bearer ${session.access_token}`;
			}

			if (addingToEmployee) {
				// Create new availability
				const response = await fetch(`/api/admin/employees/${addingToEmployee}/availability`, {
					method: "POST",
					headers,
					body: JSON.stringify(formData),
				});
				if (!response.ok) throw new Error("Failed to create availability");
			} else if (editing) {
				// Update existing availability
				const response = await fetch(`/api/admin/employees/${formData.employee_id}/availability/${editing}`, {
					method: "PUT",
					headers,
					body: JSON.stringify(formData),
				});
				if (!response.ok) throw new Error("Failed to update availability");
			}

			await fetchData();
			handleCancel();
		} catch (error) {
			console.error("Error saving availability:", error);
		}
	};

	const handleDelete = async (employeeId: string, availabilityId: string) => {
		if (!confirm("Êtes-vous sûr de vouloir supprimer cette disponibilité ?")) return;

		try {
			// Get current session for auth
			const {
				data: { session },
			} = await supabase.auth.getSession();

			const headers: Record<string, string> = {};
			if (session?.access_token) {
				headers["Authorization"] = `Bearer ${session.access_token}`;
			}

			const response = await fetch(`/api/admin/employees/${employeeId}/availability/${availabilityId}`, {
				method: "DELETE",
				headers,
			});
			if (!response.ok) throw new Error("Failed to delete availability");

			await fetchData();
		} catch (error) {
			console.error("Error deleting availability:", error);
		}
	};

	const handleCancel = () => {
		setAddingToEmployee(null);
		setEditing(null);
		setFormData({});
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center py-8">
				<div className="text-center">
					<Clock className="w-8 h-8 mx-auto mb-4 animate-spin" />
					<p>Chargement des employés...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{employees.map((employee) => {
				const availability = employeeAvailability[employee.id] || [];
				const isAdding = addingToEmployee === employee.id;

				return (
					<Card key={employee.id}>
						<CardHeader>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									<User className="w-5 h-5" />
									<div>
										<CardTitle className="text-lg">
											{employee.first_name} {employee.last_name}
										</CardTitle>
										<p className="text-sm text-gray-500">{employee.email}</p>
									</div>
								</div>
								<Button
									onClick={() => handleAddAvailability(employee.id)}
									disabled={isAdding || editing !== null}
									size="sm"
								>
									<Plus className="w-4 h-4 mr-2" />
									Ajouter disponibilité
								</Button>
							</div>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								{/* Existing Availability */}
								{availability.map((item) => (
									<div key={item.id} className="border rounded-lg p-4">
										{editing === item.id ? (
											<AvailabilityForm
												formData={formData}
												setFormData={setFormData}
												onSave={handleSave}
												onCancel={handleCancel}
											/>
										) : (
											<div className="flex items-center justify-between">
												<div className="space-y-1">
													<div className="flex items-center gap-2">
														<Badge variant="outline">
															{
																DAYS_OF_WEEK.find((d) => d.value === item.day_of_week)
																	?.label
															}
														</Badge>
														<span className="font-medium">
															{item.start_time} - {item.end_time}
														</span>
														<Badge variant={item.is_available ? "default" : "secondary"}>
															{item.is_available ? "Disponible" : "Indisponible"}
														</Badge>
													</div>
													{(item.effective_from || item.effective_until) && (
														<div className="text-sm text-gray-500">
															{item.effective_from && `Du ${item.effective_from}`}
															{item.effective_until && ` au ${item.effective_until}`}
														</div>
													)}
												</div>
												<div className="flex gap-2">
													<Button
														variant="outline"
														size="sm"
														onClick={() => handleEdit(item)}
														disabled={isAdding || editing !== null}
													>
														<Edit className="w-4 h-4" />
													</Button>
													<Button
														variant="outline"
														size="sm"
														onClick={() => handleDelete(employee.id, item.id)}
														disabled={isAdding || editing !== null}
													>
														<Trash2 className="w-4 h-4" />
													</Button>
												</div>
											</div>
										)}
									</div>
								))}

								{/* Add Form */}
								{isAdding && (
									<div className="border rounded-lg p-4 bg-gray-50">
										<AvailabilityForm
											formData={formData}
											setFormData={setFormData}
											onSave={handleSave}
											onCancel={handleCancel}
										/>
									</div>
								)}

								{availability.length === 0 && !isAdding && (
									<div className="text-center py-6 text-gray-500">
										<Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
										<p className="text-sm">Aucune disponibilité configurée</p>
										<p className="text-xs">
											Cet employé utilisera les horaires par défaut (8h-18h)
										</p>
									</div>
								)}
							</div>
						</CardContent>
					</Card>
				);
			})}

			{employees.length === 0 && (
				<Card>
					<CardContent className="text-center py-8">
						<User className="w-12 h-12 mx-auto mb-4 opacity-50" />
						<p className="text-gray-500">Aucun employé trouvé</p>
					</CardContent>
				</Card>
			)}
		</div>
	);
}

interface AvailabilityFormProps {
	formData: Partial<EmployeeAvailability["Insert"]>;
	setFormData: (data: Partial<EmployeeAvailability["Insert"]>) => void;
	onSave: () => void;
	onCancel: () => void;
}

function AvailabilityForm({ formData, setFormData, onSave, onCancel }: AvailabilityFormProps) {
	return (
		<div className="space-y-4">
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<div>
					<Label htmlFor="day_of_week">Jour de la semaine</Label>
					<Select
						value={formData.day_of_week?.toString()}
						onValueChange={(value) => setFormData({ ...formData, day_of_week: parseInt(value) })}
					>
						<SelectTrigger>
							<SelectValue placeholder="Choisir un jour..." />
						</SelectTrigger>
						<SelectContent>
							{DAYS_OF_WEEK.map((day) => (
								<SelectItem key={day.value} value={day.value.toString()}>
									{day.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div>
					<Label htmlFor="start_time">Heure de début</Label>
					<Input
						type="time"
						value={formData.start_time || ""}
						onChange={(e) => setFormData({ ...formData, start_time: e.target.value })}
					/>
				</div>

				<div>
					<Label htmlFor="end_time">Heure de fin</Label>
					<Input
						type="time"
						value={formData.end_time || ""}
						onChange={(e) => setFormData({ ...formData, end_time: e.target.value })}
					/>
				</div>

				<div className="flex items-center space-x-2">
					<Switch
						checked={formData.is_available || false}
						onCheckedChange={(checked) => setFormData({ ...formData, is_available: checked })}
					/>
					<Label>Disponible</Label>
				</div>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div>
					<Label htmlFor="effective_from">Effectif à partir de (optionnel)</Label>
					<Input
						type="date"
						value={formData.effective_from || ""}
						onChange={(e) => setFormData({ ...formData, effective_from: e.target.value || null })}
					/>
				</div>

				<div>
					<Label htmlFor="effective_until">Effectif jusqu'au (optionnel)</Label>
					<Input
						type="date"
						value={formData.effective_until || ""}
						onChange={(e) => setFormData({ ...formData, effective_until: e.target.value || null })}
					/>
				</div>
			</div>

			<div className="flex gap-2">
				<Button onClick={onSave}>
					<Save className="w-4 h-4 mr-2" />
					Enregistrer
				</Button>
				<Button variant="outline" onClick={onCancel}>
					<X className="w-4 h-4 mr-2" />
					Annuler
				</Button>
			</div>
		</div>
	);
}

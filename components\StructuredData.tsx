"use client";

import { usePathname } from "next/navigation";

interface StructuredDataProps {
  type?: "homepage" | "service" | "contact";
  service?: {
    name: string;
    description: string;
    price: number;
    duration: number;
    image?: string;
  };
}

export default function StructuredData({ type = "homepage", service }: StructuredDataProps) {
  const pathname = usePathname();

  const getStructuredData = () => {
    const baseOrganization = {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "@id": "https://www.soleiletdecouverte.com/#organization",
      name: "Soleil et Découverte",
      description: "Excursions éco-responsables en Guadeloupe - WaterBikes, visites culturelles et rencontres avec les pélicans",
      url: "https://www.soleiletdecouverte.com",
      telephone: "+***********",
      email: "<EMAIL>",
      address: {
        "@type": "PostalAddress",
        addressLocality: "Petit-Canal",
        addressRegion: "Guadeloupe",
        addressCountry: "FR",
        postalCode: "97131"
      },
      geo: {
        "@type": "GeoCoordinates",
        latitude: "16.1833",
        longitude: "-61.5333"
      },
      openingHours: [
        "Mo-Su 08:00-18:00"
      ],
      priceRange: "€€",
      servesCuisine: "Caribbean",
      areaServed: {
        "@type": "Place",
        name: "Guadeloupe"
      },
      hasOfferCatalog: {
        "@type": "OfferCatalog",
        name: "Excursions éco-responsables",
        itemListElement: [
          {
            "@type": "Offer",
            itemOffered: {
              "@type": "Service",
              name: "Excursions WaterBike",
              description: "Découverte de la mangrove en WaterBike éco-responsable"
            }
          },
          {
            "@type": "Offer",
            itemOffered: {
              "@type": "Service", 
              name: "Visites culturelles guidées",
              description: "Exploration du patrimoine culturel guadeloupéen"
            }
          },
          {
            "@type": "Offer",
            itemOffered: {
              "@type": "Service",
              name: "Rencontres avec les pélicans",
              description: "Observation respectueuse de la faune locale"
            }
          }
        ]
      },
      sameAs: [
        "https://www.facebook.com/soleiletdecouverte",
        "https://www.instagram.com/soleiletdecouverte"
      ]
    };

    if (type === "service" && service) {
      return {
        "@context": "https://schema.org",
        "@type": "Service",
        name: service.name,
        description: service.description,
        provider: {
          "@id": "https://www.soleiletdecouverte.com/#organization"
        },
        areaServed: {
          "@type": "Place",
          name: "Guadeloupe"
        },
        offers: {
          "@type": "Offer",
          price: service.price,
          priceCurrency: "EUR",
          availability: "https://schema.org/InStock",
          validFrom: new Date().toISOString(),
          url: `https://www.soleiletdecouverte.com${pathname}`
        },
        ...(service.image && {
          image: service.image
        })
      };
    }

    if (type === "contact") {
      return {
        "@context": "https://schema.org",
        "@type": "ContactPage",
        mainEntity: {
          "@id": "https://www.soleiletdecouverte.com/#organization"
        }
      };
    }

    // Homepage with website schema
    return [
      baseOrganization,
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "@id": "https://www.soleiletdecouverte.com/#website",
        url: "https://www.soleiletdecouverte.com",
        name: "Soleil et Découverte",
        description: "Excursions éco-responsables en Guadeloupe",
        publisher: {
          "@id": "https://www.soleiletdecouverte.com/#organization"
        },
        potentialAction: {
          "@type": "SearchAction",
          target: {
            "@type": "EntryPoint",
            urlTemplate: "https://www.soleiletdecouverte.com/services?search={search_term_string}"
          },
          "query-input": "required name=search_term_string"
        }
      }
    ];
  };

  const structuredData = getStructuredData();

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2)
      }}
    />
  );
}

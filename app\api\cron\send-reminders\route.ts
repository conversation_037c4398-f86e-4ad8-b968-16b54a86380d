import { NextRequest, NextResponse } from "next/server";
import { supabaseAdmin } from "@/lib/supabase";
import { sendBookingReminderEmail } from "@/lib/email-service";

/**
 * Cron job to send reminder emails 24 hours before scheduled appointments
 * This endpoint should be called by a cron service (like Vercel Cron or external service)
 *
 * Expected to run every hour to check for reservations that need reminders
 */
export async function GET(request: NextRequest) {
	try {
		console.log("=== REMINDER EMAIL CRON JOB STARTED ===");

		// Verify this is a legitimate cron request
		const authHeader = request.headers.get("authorization");
		const cronSecret = process.env.CRON_SECRET;

		if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
			console.log("Unauthorized cron request");
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Calculate the time window for reminders (24 hours from now, with 1-hour buffer)
		const now = new Date();
		const reminderStart = new Date(now.getTime() + 23 * 60 * 60 * 1000); // 23 hours from now
		const reminderEnd = new Date(now.getTime() + 25 * 60 * 60 * 1000); // 25 hours from now

		console.log("Looking for reservations between:", {
			start: reminderStart.toISOString(),
			end: reminderEnd.toISOString(),
		});

		// Get confirmed reservations that need reminders
		if (!supabaseAdmin) {
			console.error("Supabase admin client not available");
			return NextResponse.json({ error: "Database connection error" }, { status: 500 });
		}

		const { data: reservations, error } = await supabaseAdmin
			.from("reservations")
			.select(
				`
        id,
        reservation_number,
        start_time,
        participant_count,
        total_amount,
        reminder_sent,
        customer:customers(first_name, last_name, email),
        service:services(name)
      `
			)
			.eq("status", "confirmed")
			.gte("start_time", reminderStart.toISOString())
			.lte("start_time", reminderEnd.toISOString())
			.eq("reminder_sent", false);

		if (error) {
			console.error("Error fetching reservations for reminders:", error);
			return NextResponse.json({ error: "Database error" }, { status: 500 });
		}

		console.log(`Found ${reservations?.length || 0} reservations needing reminders`);

		if (!reservations || reservations.length === 0) {
			return NextResponse.json({
				success: true,
				message: "No reservations need reminders at this time",
				processed: 0,
			});
		}

		let successCount = 0;
		let errorCount = 0;
		const errors: string[] = [];

		// Process each reservation
		for (const reservation of reservations) {
			try {
				if (!reservation.customer || !reservation.service) {
					console.error(`Missing customer or service data for reservation ${reservation.id}`);
					errorCount++;
					errors.push(`Missing data for reservation ${reservation.reservation_number}`);
					continue;
				}

				const customer = Array.isArray(reservation.customer) ? reservation.customer[0] : reservation.customer;
				const service = Array.isArray(reservation.service) ? reservation.service[0] : reservation.service;

				const customerName = `${customer?.first_name} ${customer?.last_name}`;
				const serviceName = service?.name;
				const date = new Date(reservation.start_time).toLocaleDateString("fr-FR");
				const time = new Date(reservation.start_time).toLocaleTimeString("fr-FR", {
					hour: "2-digit",
					minute: "2-digit",
				});

				console.log(`Sending reminder for reservation ${reservation.reservation_number} to ${customer?.email}`);

				// Send reminder email
				const emailResult = await sendBookingReminderEmail(customer?.email || "", customerName, {
					reservationNumber: reservation.reservation_number,
					serviceName,
					date,
					time,
					participants: reservation.participant_count,
					totalAmount: reservation.total_amount,
				});

				if (emailResult.success) {
					// Mark reminder as sent
					await supabaseAdmin
						.from("reservations")
						.update({
							reminder_sent: true,
							reminder_sent_at: new Date().toISOString(),
						})
						.eq("id", reservation.id);

					console.log(`Reminder sent successfully for reservation ${reservation.reservation_number}`);
					successCount++;
				} else {
					console.error(
						`Failed to send reminder for reservation ${reservation.reservation_number}:`,
						emailResult.error
					);
					errorCount++;
					errors.push(`${reservation.reservation_number}: ${emailResult.error}`);
				}
			} catch (error) {
				console.error(`Error processing reservation ${reservation.id}:`, error);
				errorCount++;
				errors.push(
					`${reservation.reservation_number}: ${error instanceof Error ? error.message : "Unknown error"}`
				);
			}
		}

		console.log("=== REMINDER EMAIL CRON JOB COMPLETED ===");
		console.log(`Processed: ${reservations.length}, Success: ${successCount}, Errors: ${errorCount}`);

		return NextResponse.json({
			success: true,
			message: "Reminder email job completed",
			processed: reservations.length,
			successful: successCount,
			errors: errorCount,
			errorDetails: errors.length > 0 ? errors : undefined,
		});
	} catch (error) {
		console.error("Cron job error:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}

/**
 * Manual trigger for testing (POST request)
 * This allows manual testing of the reminder system
 */
export async function POST(request: NextRequest) {
	try {
		console.log("=== MANUAL REMINDER EMAIL TRIGGER ===");

		// For manual testing, we'll look for reservations in the next 48 hours
		const now = new Date();
		const reminderStart = new Date(now.getTime() + 22 * 60 * 60 * 1000); // 22 hours from now
		const reminderEnd = new Date(now.getTime() + 48 * 60 * 60 * 1000); // 48 hours from now

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection error" }, { status: 500 });
		}

		const { data: reservations, error } = await supabaseAdmin
			.from("reservations")
			.select(
				`
        id,
        reservation_number,
        start_time,
        participant_count,
        total_amount,
        reminder_sent,
        customer:customers(first_name, last_name, email),
        service:services(name)
      `
			)
			.eq("status", "confirmed")
			.gte("start_time", reminderStart.toISOString())
			.lte("start_time", reminderEnd.toISOString());

		if (error) {
			return NextResponse.json({ error: "Database error" }, { status: 500 });
		}

		return NextResponse.json({
			success: true,
			message: "Manual reminder check completed",
			found: reservations?.length || 0,
			reservations: reservations?.map((r: any) => {
				const customer = Array.isArray(r.customer) ? r.customer[0] : r.customer;
				const service = Array.isArray(r.service) ? r.service[0] : r.service;
				return {
					id: r.id,
					reservation_number: r.reservation_number,
					start_time: r.start_time,
					reminder_sent: r.reminder_sent,
					customer_email: customer?.email,
					service_name: service?.name,
				};
			}),
		});
	} catch (error) {
		console.error("Manual trigger error:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}

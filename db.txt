admin_audit_log: id(uuid), admin_user_id(uuid), action(varchar), table_name(varchar), record_id(uuid), old_values(jsonb), new_values(jsonb), ip_address(inet), user_agent(text), session_id(varchar), created_at(timestamp)

business_settings: id(uuid), key(varchar), value(text), value_type(varchar), category(varchar), description(text), is_public(boolean), created_at(timestamp), updated_at(timestamp)

customer_analytics: id(uuid), customer_id(uuid), total_reservations(integer), completed_reservations(integer), cancelled_reservations(integer), no_show_reservations(integer), total_spent(numeric), total_participants(integer), average_rating(numeric), total_reviews(integer), first_reservation_date(timestamp), last_reservation_date(timestamp), favorite_service_id(uuid), preferred_time_slot(time), average_group_size(numeric), customer_lifetime_value(numeric), loyalty_tier(varchar), last_updated(timestamp), created_at(timestamp)

customer_feedback: id(uuid), reservation_id(uuid), customer_id(uuid), rating(integer), review_text(text), service_quality_rating(integer), staff_rating(integer), equipment_rating(integer), would_recommend(boolean), is_public(boolean), response_text(text), responded_by(uuid), responded_at(timestamp), created_at(timestamp), updated_at(timestamp)

customer_journey_events: id(uuid), customer_id(uuid), event_type(varchar), event_data(jsonb), reservation_id(uuid), service_id(uuid), session_id(varchar), user_agent(text), ip_address(inet), referrer_url(text), created_at(timestamp)

customers: id(uuid), date_of_birth(date), nationality(text), emergency_contact_name(text), emergency_contact_phone(text), dietary_restrictions(text), medical_conditions(text), marketing_consent(boolean), created_at(timestamp), updated_at(timestamp), email(varchar), first_name(varchar), last_name(varchar), phone(varchar)

daily_business_metrics: id(uuid), metric_date(date), total_reservations(integer), confirmed_reservations(integer), cancelled_reservations(integer), total_revenue(numeric), total_participants(integer), new_customers(integer), repeat_customers(integer), average_order_value(numeric), occupancy_rate(numeric), customer_satisfaction(numeric), weather_condition(varchar), special_events(array), created_at(timestamp), updated_at(timestamp)

discount_coupons: id(uuid), code(text), description(text), discount_type(text), discount_value(numeric), min_purchase_amount(numeric), max_discount_amount(numeric), usage_limit(integer), current_usage(integer), valid_from(timestamp), valid_until(timestamp), applicable_services(array), is_active(boolean), created_by(uuid), created_at(timestamp), updated_at(timestamp)

employee_analytics: id(uuid), employee_id(uuid), period_start(date), period_end(date), total_assignments(integer), completed_assignments(integer), total_revenue_generated(numeric), average_customer_rating(numeric), created_at(timestamp)

employee_availability: id(uuid), employee_id(uuid), day_of_week(integer), start_time(time), end_time(time), is_available(boolean), effective_from(date), effective_until(date), created_at(timestamp), updated_at(timestamp)

employee_service_qualifications: id(uuid), employee_id(uuid), service_id(uuid), qualification_level(varchar), certified_date(date), expiry_date(date), notes(text), is_active(boolean), created_at(timestamp), updated_at(timestamp)

employee_time_off: id(uuid), employee_id(uuid), start_date(date), end_date(date), start_time(time), end_time(time), reason(text), type(varchar), status(varchar), created_at(timestamp), updated_at(timestamp)

employees: id(uuid), employee_code(text), hire_date(date), hourly_rate(numeric), is_active(boolean), skills(array), languages(array), notes(text), created_at(timestamp), updated_at(timestamp), default_hourly_rate(numeric), is_available_for_scheduling(boolean), max_concurrent_services(integer), scheduling_priority(integer), first_name(varchar), last_name(varchar), email(varchar), phone(varchar), role(varchar)

equipment: id(uuid), name(text), description(text), number_of_units(integer), capacity_per_unit(integer), is_active(boolean), created_at(timestamp), updated_at(timestamp)

equipment_reservations: id(uuid), equipment_id(uuid), quantity_reserved(integer), status(text), created_at(timestamp), updated_at(timestamp), reservation_id(uuid), start_time(timestamp), end_time(timestamp)

equipment_utilization_history: id(uuid), equipment_id(uuid), usage_date(date), total_capacity_hours(numeric), utilized_capacity_hours(numeric), utilization_rate(numeric), maintenance_hours(numeric), downtime_hours(numeric), revenue_generated(numeric), bookings_count(integer), created_at(timestamp)

notifications: id(uuid), recipient_id(uuid), reservation_id(uuid), notification_type(text), subject(text), content(text), status(text), sent_at(timestamp), error_message(text), created_at(timestamp), priority(text), read_at(timestamp), is_read(boolean)

payments: id(uuid), reservation_id(uuid), payment_method(text), payment_intent_id(text), amount(numeric), currency(text), status(text), payment_date(timestamp), failure_reason(text), created_at(timestamp), updated_at(timestamp), payment_type(text), deposit_percentage(numeric), is_deposit(boolean), completed_manually(boolean), completed_by(uuid), completion_notes(text), manual_completion_date(timestamp)

pricing_tiers: id(uuid), service_id(uuid), tier_name(text), min_age(integer), max_age(integer), price(numeric), is_active(boolean), created_at(timestamp), updated_at(timestamp)

profiles: id(uuid), email(text), first_name(text), last_name(text), phone(text), role(text), created_at(timestamp), updated_at(timestamp)

refunds: id(uuid), payment_id(uuid), reservation_id(uuid), refund_amount(numeric), refund_reason(text), refund_method(text), status(text), processed_by(uuid), processed_at(timestamp), external_refund_id(text), created_at(timestamp), updated_at(timestamp)

reservation_status_history: id(uuid), reservation_id(uuid), old_status(varchar), new_status(varchar), changed_by(uuid), change_reason(text), automated_change(boolean), metadata(jsonb), created_at(timestamp)

reservations: id(uuid), customer_id(uuid), reservation_number(text), participant_count(integer), total_amount(numeric), currency(text), status(text), special_requests(text), discount_code(text), discount_amount(numeric), check_in_time(timestamp), qr_code(text), created_at(timestamp), updated_at(timestamp), service_id(uuid), assigned_employee_id(uuid), start_time(timestamp), end_time(timestamp), booking_source(varchar), admin_notes(text), requires_confirmation(boolean), confirmed_at(timestamp), confirmed_by(uuid), deposit_amount(numeric), remaining_amount(numeric), deposit_paid(boolean), deposit_payment_id(uuid), tier_participants(jsonb), created_by_admin(boolean), created_by_admin_id(uuid)

schedule_templates: id(uuid), name(varchar), description(text), service_id(uuid), template_data(jsonb), is_active(boolean), created_at(timestamp), updated_at(timestamp)

service_analytics: id(uuid), service_id(uuid), period_start(date), period_end(date), total_bookings(integer), confirmed_bookings(integer), cancelled_bookings(integer), no_show_bookings(integer), total_revenue(numeric), total_participants(integer), average_group_size(numeric), occupancy_rate(numeric), average_rating(numeric), total_reviews(integer), repeat_customer_rate(numeric), cancellation_rate(numeric), no_show_rate(numeric), peak_booking_hour(integer), peak_booking_day(integer), created_at(timestamp), updated_at(timestamp)

service_blackout_dates: id(uuid), service_id(uuid), start_date(date), end_date(date), reason(text), is_active(boolean), created_at(timestamp), updated_at(timestamp)

service_equipment_requirements: id(uuid), service_id(uuid), equipment_id(uuid), capacity_per_participant(integer)

service_scheduling_rules: id(uuid), service_id(uuid), day_of_week(integer), min_advance_booking_hours(integer), max_advance_booking_days(integer), operating_start_time(time), operating_end_time(time), booking_interval_minutes(integer), specific_times(array), max_bookings_per_day(integer), is_active(boolean), created_at(timestamp), updated_at(timestamp)

services: id(uuid), name(text), description(text), duration_minutes(integer), buffer_time_minutes(integer), base_price(numeric), max_participants(integer), min_age(integer), max_age(integer), is_family_friendly(boolean), is_active(boolean), image_url(text), created_at(timestamp), updated_at(timestamp), features(jsonb), gallery(jsonb), location(text), category(text), default_employee_id(uuid), requires_qualification(boolean), auto_assign_employees(boolean), requires_employee(boolean), fixed_price(boolean)
"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Footer from "@/components/Footer";
import { motion } from "framer-motion";
import { Award, Calendar, Heart, Leaf, MapPin, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const team = [
	{
		name: "<PERSON><PERSON>y SINNAN",
		role: "Co-Gérant & Fondateur",
		image: "/images/djany_sinnan_team.png",
		description:
			"<PERSON>é à Petit-Canal, Djany connaît chaque recoin de la Guadeloupe. Sa passion pour l'île et son patrimoine naturel l'a poussé à créer des expériences authentiques et respectueuses.",
		specialties: ["Patrimoine local", "Écotourisme", "Navigation"],
	},
	{
		name: "<PERSON><PERSON><PERSON><PERSON>INNA<PERSON>",
		role: "Co-Gérant & Fondateur",
		image: "/images/djovanni_sinnan_team.png",
		description:
			"Également natif de <PERSON>-<PERSON>, Djovanni partage avec son associé l'amour de leur terre natale. Ensemble, ils ont lancé Soleil & Découverte en 2022 pour partager leurs trésors cachés.",
		specialties: ["Histoire locale", "Faune et flore", "Expériences immersives"],
	},
	{
		name: "Sophie Leroy",
		role: "Guide Culturelle",
		image: "/placeholder.svg",
		description:
			"Historienne de formation, Sophie vous fera découvrir les secrets et légendes de la Guadeloupe avec passion et authenticité.",
		specialties: ["Histoire", "Culture créole", "Gastronomie locale"],
	},
	{
		name: "Antoine Moreau",
		role: "Guide Nature",
		image: "/placeholder.svg",
		description:
			"Biologiste marin et guide naturaliste, Antoine vous initiera aux merveilles de la faune et flore guadeloupéennes.",
		specialties: ["Biologie marine", "Botanique", "Photographie nature"],
	},
];

const timeline = [
	{
		year: "2022",
		title: "Création de Soleil & Découverte",
		description:
			"Djany et Djovanni SINNAN fondent l'entreprise à Petit-Canal avec une vision : faire découvrir la vraie Guadeloupe.",
	},
	{
		year: "2022",
		title: "Première excursion WaterBike",
		description: "Lancement de nos excursions écologiques en WaterBike depuis le port de pêche de Petit-Canal.",
	},
	{
		year: "2023",
		title: "Développement des visites culturelles",
		description: "Ajout des visites guidées avec chasse au trésor pour faire découvrir l'histoire locale.",
	},
	{
		year: "2023",
		title: "Rencontre avec les pélicans",
		description: "Lancement de l'excursion unique vers l'îlet sauvage pour observer les pélicans.",
	},
	{
		year: "2024",
		title: "Expansion éco-responsable",
		description: "Développement des explorations de mangrove et des dégustations de produits locaux.",
	},
	{
		year: "2024",
		title: "Reconnaissance locale",
		description: "Soleil & Découverte devient une référence pour le tourisme authentique à Petit-Canal.",
	},
];

const values = [
	{
		icon: <Leaf className="w-8 h-8" />,
		title: "Respect de l'Environnement",
		description:
			"Nous privilégions un tourisme durable qui préserve la beauté naturelle de la Guadeloupe pour les générations futures.",
	},
	{
		icon: <Heart className="w-8 h-8" />,
		title: "Passion Authentique",
		description:
			"Notre amour pour la Guadeloupe transparaît dans chaque excursion, chaque récit, chaque moment partagé avec nos visiteurs.",
	},
	{
		icon: <Users className="w-8 h-8" />,
		title: "Expérience Personnalisée",
		description:
			"Chaque groupe est unique. Nous adaptons nos excursions pour créer des souvenirs inoubliables et sur mesure.",
	},
	{
		icon: <Award className="w-8 h-8" />,
		title: "Excellence & Sécurité",
		description:
			"La qualité de nos services et la sécurité de nos clients sont au cœur de toutes nos préoccupations.",
	},
];

export default function AboutPage() {
	return (
		<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50">
			{/* Header */}
			<header className="bg-white/90 backdrop-blur-md shadow-sm sticky top-0 z-50">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center justify-between">
						<Link href="/" className="flex items-center space-x-3">
							<Image
								src="/images/logo-hd.png"
								alt="Soleil & Découverte"
								width={60}
								height={60}
								className="object-contain"
							/>
							<div>
								<h1 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-yellow-500 bg-clip-text text-transparent">
									Soleil & Découverte
								</h1>
								<p className="text-sm text-emerald-600">L'aventure au cœur de la Guadeloupe</p>
							</div>
						</Link>

						<nav className="hidden md:flex items-center space-x-8">
							<Link
								href="/"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Accueil
							</Link>
							<Link
								href="/services"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Services
							</Link>
							<Link href="/about" className="text-emerald-600 font-semibold">
								À propos
							</Link>
							<Link
								href="/gallery"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Galerie
							</Link>
							<Link
								href="/contact"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Contact
							</Link>
							<Link href="/reservation">
								<Button className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white px-6 py-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-300">
									Réserver
								</Button>
							</Link>
						</nav>
					</div>
				</div>
			</header>

			{/* Hero Section */}
			<section className="relative py-20 overflow-hidden">
				<Image
					src="/images/about_hero.png"
					alt="Notre Histoire"
					fill
					className="object-cover"
					style={{ zIndex: 1 }}
				/>
				<div className="absolute inset-0 bg-black/20" style={{ zIndex: 2 }}></div>

				<div className="container mx-auto px-4 relative z-10">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center max-w-4xl mx-auto"
					>
						<Badge className="mb-6 bg-white/20 text-white border-white/30 backdrop-blur-sm px-4 py-2 text-lg">
							🌴 Notre Histoire
						</Badge>
						<h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
							Passionnés par la
							<span className="block bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
								Guadeloupe Authentique
							</span>
						</h1>
						<p className="text-xl text-white/90 max-w-3xl mx-auto">
							Depuis plus de 10 ans, nous partageons notre amour pour cette île exceptionnelle et guidons
							les voyageurs vers ses trésors les mieux gardés.
						</p>
					</motion.div>
				</div>
			</section>

			{/* Story Section */}
			<section className="py-20 bg-white">
				<div className="container mx-auto px-4">
					<div className="grid lg:grid-cols-2 gap-12 items-center">
						<motion.div
							initial={{ opacity: 0, x: -50 }}
							whileInView={{ opacity: 1, x: 0 }}
							transition={{ duration: 0.8 }}
						>
							<Badge className="mb-4 bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-700 border-emerald-200 px-4 py-2">
								🏝️ Notre Mission
							</Badge>
							<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
								Révéler la Vraie
								<span className="block text-emerald-600">Guadeloupe</span>
							</h2>
							<p className="text-lg text-gray-600 mb-6 leading-relaxed">
								Tout a commencé en 2022 par une passion commune : celle de Djany et Djovanni SINNAN pour
								la Guadeloupe, leur île natale. Nés et élevés à Petit-Canal, ils ont grandi en explorant
								chaque recoin de ce paradis tropical, bercés par la mer et la terre de leur enfance.
							</p>
							<p className="text-lg text-gray-600 mb-6 leading-relaxed">
								En tant que co-gérants, ils ont décidé de créer Soleil & Découverte avec une vision
								claire : faire découvrir aux visiteurs la Guadeloupe authentique, loin des sentiers
								battus, dans le respect de l'environnement et des traditions locales de leur commune
								natale.
							</p>
							<p className="text-lg text-gray-600 mb-8 leading-relaxed">
								Aujourd'hui, leur équipe de guides passionnés continue de porter cette mission, en
								créant des expériences immersives où écologie et découverte vont de pair, directement
								depuis le port de pêche de Petit-Canal.
							</p>

							<div className="grid grid-cols-2 gap-6">
								<div className="text-center">
									<div className="text-3xl font-bold text-emerald-600 mb-2">2022</div>
									<div className="text-sm text-gray-600">Année de création</div>
								</div>
								<div className="text-center">
									<div className="text-3xl font-bold text-orange-600 mb-2">100%</div>
									<div className="text-sm text-gray-600">Guides locaux</div>
								</div>
								<div className="text-center">
									<div className="text-3xl font-bold text-blue-600 mb-2">5</div>
									<div className="text-sm text-gray-600">Activités uniques</div>
								</div>
								<div className="text-center">
									<div className="text-3xl font-bold text-teal-600 mb-2">4.9</div>
									<div className="text-sm text-gray-600">Note moyenne</div>
								</div>
							</div>
						</motion.div>

						<motion.div
							initial={{ opacity: 0, x: 50 }}
							whileInView={{ opacity: 1, x: 0 }}
							transition={{ duration: 0.8 }}
							className="relative"
						>
							<div className="relative overflow-hidden rounded-2xl shadow-2xl">
								<Image
									src="/images/founders_story_gallery.png"
									alt="Djany & Djovanni SINNAN"
									width={500}
									height={600}
									className="w-full h-96 object-cover"
								/>
								<div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
								<div className="absolute bottom-6 left-6 text-white">
									<p className="text-lg font-semibold">Djany & Djovanni SINNAN</p>
									<p className="text-sm opacity-90">Co-Gérants & Fondateurs</p>
								</div>
							</div>
						</motion.div>
					</div>
				</div>
			</section>

			{/* Values Section */}
			<section className="py-20 bg-gradient-to-br from-emerald-50 to-teal-50">
				<div className="container mx-auto px-4">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center mb-16"
					>
						<Badge className="mb-4 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 border-blue-200 px-4 py-2">
							💎 Nos Valeurs
						</Badge>
						<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
							Ce Qui Nous
							<span className="block text-emerald-600">Guide Chaque Jour</span>
						</h2>
						<p className="text-xl text-gray-600 max-w-3xl mx-auto">
							Nos valeurs sont le fondement de chaque excursion, de chaque rencontre, de chaque moment
							partagé avec nos visiteurs.
						</p>
					</motion.div>

					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
						{values.map((value, index) => (
							<motion.div
								key={index}
								initial={{ opacity: 0, y: 50 }}
								whileInView={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.6, delay: index * 0.2 }}
								whileHover={{ y: -10 }}
							>
								<Card className="text-center p-6 h-full shadow-lg hover:shadow-2xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
									<CardContent className="p-0">
										<div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 text-white rounded-2xl mb-4">
											{value.icon}
										</div>
										<h3 className="text-xl font-bold text-gray-900 mb-3">{value.title}</h3>
										<p className="text-gray-600 leading-relaxed">{value.description}</p>
									</CardContent>
								</Card>
							</motion.div>
						))}
					</div>
				</div>
			</section>

			{/* Team Section */}
			<section className="py-20 bg-white">
				<div className="container mx-auto px-4">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center mb-16"
					>
						<Badge className="mb-4 bg-gradient-to-r from-orange-100 to-yellow-100 text-orange-700 border-orange-200 px-4 py-2">
							👥 Notre Équipe
						</Badge>
						<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
							Rencontrez Nos
							<span className="block text-emerald-600">Guides Passionnés</span>
						</h2>
						<p className="text-xl text-gray-600 max-w-3xl mx-auto">
							Chaque membre de notre équipe apporte son expertise unique pour vous offrir des expériences
							inoubliables en Guadeloupe.
						</p>
					</motion.div>

					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
						{team.map((member, index) => (
							<motion.div
								key={index}
								initial={{ opacity: 0, y: 50 }}
								whileInView={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.6, delay: index * 0.2 }}
								whileHover={{ y: -10 }}
							>
								<Card className="overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 border-0">
									<div className="relative">
										<Image
											src={member.image || "/placeholder.svg"}
											alt={member.name}
											width={300}
											height={300}
											className="w-full h-64 object-cover"
										/>
										<div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
										<div className="absolute bottom-4 left-4 text-white">
											<h3 className="text-lg font-bold">{member.name}</h3>
											<p className="text-sm opacity-90">{member.role}</p>
										</div>
									</div>

									<CardContent className="p-6">
										<p className="text-gray-600 mb-4 text-sm leading-relaxed">
											{member.description}
										</p>

										<div>
											<h4 className="font-semibold text-gray-900 mb-2 text-sm">Spécialités :</h4>
											<div className="flex flex-wrap gap-1">
												{member.specialties.map((specialty, idx) => (
													<Badge
														key={idx}
														variant="outline"
														className="text-xs text-emerald-600 border-emerald-200"
													>
														{specialty}
													</Badge>
												))}
											</div>
										</div>
									</CardContent>
								</Card>
							</motion.div>
						))}
					</div>
				</div>
			</section>

			{/* Timeline Section */}
			<section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
				<div className="container mx-auto px-4">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center mb-16"
					>
						<Badge className="mb-4 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 border-purple-200 px-4 py-2">
							📅 Notre Parcours
						</Badge>
						<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
							10 Années
							<span className="block text-emerald-600">d'Aventures</span>
						</h2>
						<p className="text-xl text-gray-600 max-w-3xl mx-auto">
							Découvrez les moments clés qui ont façonné l'histoire de Soleil & Découverte et notre
							évolution au fil des années.
						</p>
					</motion.div>

					<div className="max-w-4xl mx-auto">
						<div className="relative">
							{/* Timeline line */}
							<div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-emerald-500 to-teal-600 rounded-full"></div>

							{timeline.map((event, index) => (
								<motion.div
									key={index}
									initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
									whileInView={{ opacity: 1, x: 0 }}
									transition={{ duration: 0.6, delay: index * 0.2 }}
									className={`relative flex items-center mb-12 ${
										index % 2 === 0 ? "flex-row" : "flex-row-reverse"
									}`}
								>
									<div className={`w-1/2 ${index % 2 === 0 ? "pr-8 text-right" : "pl-8 text-left"}`}>
										<Card className="p-6 shadow-lg border-0 bg-white/80 backdrop-blur-sm">
											<CardContent className="p-0">
												<div className="flex items-center gap-2 mb-2">
													<Calendar className="w-4 h-4 text-emerald-600" />
													<Badge className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white">
														{event.year}
													</Badge>
												</div>
												<h3 className="text-lg font-bold text-gray-900 mb-2">{event.title}</h3>
												<p className="text-gray-600 text-sm leading-relaxed">
													{event.description}
												</p>
											</CardContent>
										</Card>
									</div>

									{/* Timeline dot */}
									<div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full border-4 border-white shadow-lg"></div>

									<div className="w-1/2"></div>
								</motion.div>
							))}
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-20 bg-gradient-to-br from-emerald-600 to-teal-700 relative overflow-hidden">
				<div className="absolute inset-0 bg-black/20"></div>
				<Image src="/images/join_us_cta.png" alt="Join Us" fill className="object-cover -z-10" />

				<div className="container mx-auto px-4 relative z-10">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center max-w-4xl mx-auto"
					>
						<h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Rejoignez l'Aventure !</h2>
						<p className="text-xl text-white/90 mb-8">
							Découvrez pourquoi des milliers de voyageurs nous font confiance pour explorer la vraie
							Guadeloupe.
						</p>
						<div className="flex flex-col sm:flex-row gap-4 justify-center">
							<Link href="/reservation">
								<Button
									size="lg"
									className="bg-white text-emerald-600 hover:bg-gray-100 px-8 py-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 text-lg font-semibold"
								>
									Découvrir Nos Excursions
								</Button>
							</Link>
							<Link href="/contact">
								<Button
									size="lg"
									variant="outline"
									className="border-2 border-white text-white hover:bg-white hover:text-emerald-600 px-8 py-4 rounded-full backdrop-blur-sm bg-white/10 transition-all duration-300 text-lg font-semibold"
								>
									Nous Contacter
								</Button>
							</Link>
						</div>
					</motion.div>
				</div>
			</section>

			<Footer />
		</div>
	);
}

import { NextRequest, NextResponse } from "next/server";
import { createSecurityDepositAuthorization, eurosToCents } from "@/lib/security-deposit";
import { supabaseAdmin } from "@/lib/supabase";

export async function POST(request: NextRequest) {
	try {
		const { reservationId, amount } = await request.json();

		console.log("=== SECURITY DEPOSIT AUTHORIZATION DEBUG ===");
		console.log("Request body:", { reservationId, amount });

		if (!reservationId) {
			console.log("ERROR: No reservation ID provided");
			return NextResponse.json({ error: "Reservation ID is required" }, { status: 400 });
		}

		if (!amount || amount <= 0) {
			console.log("ERROR: Invalid amount provided");
			return NextResponse.json({ error: "Valid amount is required" }, { status: 400 });
		}

		if (!supabaseAdmin) {
			console.log("ERROR: Database connection not available");
			return NextResponse.json({ error: "Database connection not available" }, { status: 500 });
		}

		// Get reservation details
		const { data: reservation, error: reservationError } = await supabaseAdmin
			.from("reservations")
			.select(
				`
        *,
        service:services(name, security_deposit_amount, requires_security_deposit),
        customer:customers(first_name, last_name, email)
      `
			)
			.eq("id", reservationId)
			.single();

		if (reservationError || !reservation) {
			console.log("ERROR: Reservation not found:", reservationError);
			return NextResponse.json({ error: "Reservation not found" }, { status: 404 });
		}

		// Verify service requires security deposit
		if (!reservation.service?.requires_security_deposit) {
			console.log("ERROR: Service does not require security deposit");
			return NextResponse.json({ error: "Service does not require security deposit" }, { status: 400 });
		}

		// Verify amount matches service security deposit amount
		const expectedAmount = reservation.service.security_deposit_amount;
		if (Math.abs(amount - expectedAmount) > 0.01) {
			console.log("ERROR: Amount mismatch", { provided: amount, expected: expectedAmount });
			return NextResponse.json({ error: `Amount must be ${expectedAmount}€` }, { status: 400 });
		}

		// Check if security deposit already authorized or pending
		if (reservation.security_deposit_status === "authorized") {
			console.log("ERROR: Security deposit already authorized");
			return NextResponse.json({ error: "Security deposit already authorized" }, { status: 400 });
		}

		if (reservation.security_deposit_status === "pending") {
			console.log("ERROR: Security deposit authorization already pending");
			// Return existing payment intent if available
			if (reservation.security_deposit_payment_intent_id) {
				return NextResponse.json(
					{
						error: "Security deposit authorization already in progress",
						existingPaymentIntentId: reservation.security_deposit_payment_intent_id,
					},
					{ status: 409 }
				);
			}
			return NextResponse.json({ error: "Security deposit authorization already pending" }, { status: 400 });
		}

		const customerName =
			`${reservation.customer?.first_name || ""} ${reservation.customer?.last_name || ""}`.trim();

		// Create authorization
		const result = await createSecurityDepositAuthorization({
			amount: eurosToCents(amount),
			reservationId,
			customerEmail: reservation.customer?.email,
			customerName,
		});

		console.log("Security deposit authorization result:", result);

		if (!result.success) {
			console.log("ERROR: Failed to create authorization:", result.error);
			return NextResponse.json({ error: result.error || "Failed to create authorization" }, { status: 500 });
		}

		// Update reservation with security deposit info
		if (supabaseAdmin && result.paymentIntent) {
			const { error: updateError } = await supabaseAdmin
				.from("reservations")
				.update({
					security_deposit_method: "authorization",
					security_deposit_status: "pending",
					security_deposit_payment_intent_id: result.paymentIntent.id,
					updated_at: new Date().toISOString(),
				})
				.eq("id", reservationId);

			if (updateError) {
				console.error("Error updating reservation:", updateError);
				// Don't fail the request for update errors
			}
		}

		return NextResponse.json({
			success: true,
			clientSecret: result.clientSecret,
			paymentIntentId: result.paymentIntent?.id,
			amount: amount,
		});
	} catch (error) {
		console.error("Error in security deposit authorization:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}

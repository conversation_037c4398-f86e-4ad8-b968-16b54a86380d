import { withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// GET /api/admin/analytics - Get dashboard analytics and KPIs
export const GET = withAdminAuth(async (request: NextRequest) => {
	try {
		const { searchParams } = new URL(request.url);
		const period = searchParams.get("period") || "30"; // days
		const startDate = searchParams.get("start_date");
		const endDate = searchParams.get("end_date");

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection not available" }, { status: 500 });
		}

		// Calculate date range
		const now = new Date();
		const periodStart = startDate
			? new Date(startDate)
			: new Date(now.getTime() - parseInt(period) * 24 * 60 * 60 * 1000);
		const periodEnd = endDate ? new Date(endDate) : now;

		// Get reservations data for the period
		const { data: reservations, error: reservationsError } = await supabaseAdmin
			.from("reservations")
			.select(
				`
        id,
        status,
        total_amount,
        participant_count,
        start_time,
        created_at,
        service:services (
          id,
          name,
          category
        ),
        customer_feedback (
          rating
        )
      `
			)
			.gte("created_at", periodStart.toISOString())
			.lte("created_at", periodEnd.toISOString());

		if (reservationsError) {
			console.error("Error fetching reservations:", reservationsError);
			return NextResponse.json({ error: "Failed to fetch analytics data" }, { status: 500 });
		}

		// Get previous period for comparison
		const previousPeriodStart = new Date(periodStart.getTime() - (periodEnd.getTime() - periodStart.getTime()));
		const { data: previousReservations } = await supabaseAdmin
			.from("reservations")
			.select("id, status, total_amount, participant_count, created_at")
			.gte("created_at", previousPeriodStart.toISOString())
			.lt("created_at", periodStart.toISOString());

		// Calculate KPIs
		const currentReservations = reservations || [];
		const prevReservations = previousReservations || [];

		const totalReservations = currentReservations.length;
		const prevTotalReservations = prevReservations.length;
		const reservationsChange =
			prevTotalReservations > 0 ? ((totalReservations - prevTotalReservations) / prevTotalReservations) * 100 : 0;

		const confirmedReservations = currentReservations.filter(
			(r) => r.status === "confirmed" || r.status === "completed"
		);
		const totalRevenue = confirmedReservations.reduce((sum, r) => sum + (r.total_amount || 0), 0);
		const prevRevenue = prevReservations
			.filter((r) => r.status === "confirmed" || r.status === "completed")
			.reduce((sum, r) => sum + (r.total_amount || 0), 0);
		const revenueChange = prevRevenue > 0 ? ((totalRevenue - prevRevenue) / prevRevenue) * 100 : 0;

		const totalParticipants = confirmedReservations.reduce((sum, r) => sum + (r.participant_count || 0), 0);
		const prevParticipants = prevReservations
			.filter((r) => r.status === "confirmed" || r.status === "completed")
			.reduce((sum, r) => sum + (r.participant_count || 0), 0);
		const participantsChange =
			prevParticipants > 0 ? ((totalParticipants - prevParticipants) / prevParticipants) * 100 : 0;

		// Calculate occupancy rate (assuming max capacity per service)
		const { data: services } = await supabaseAdmin.from("services").select("id, max_participants");

		const serviceCapacityMap = new Map(services?.map((s) => [s.id, s.max_participants]) || []);
		const totalCapacity = confirmedReservations.reduce((sum, r: any) => {
			const maxCapacity = serviceCapacityMap.get(r.service?.id) || 0;
			return sum + maxCapacity;
		}, 0);
		const occupancyRate = totalCapacity > 0 ? (totalParticipants / totalCapacity) * 100 : 0;

		// Get customer data with analytics
		const { data: customers } = await supabaseAdmin
			.from("customers")
			.select("id, created_at, marketing_consent")
			.gte("created_at", periodStart.toISOString())
			.lte("created_at", periodEnd.toISOString());

		const newCustomers = customers?.length || 0;

		// Get customer analytics summary
		const { data: customerAnalytics } = await supabaseAdmin
			.from("customer_summary")
			.select("*")
			.order("total_spent", { ascending: false })
			.limit(10);

		// Get service performance data
		const { data: servicePerformance } = await supabaseAdmin
			.from("service_performance")
			.select("*")
			.order("total_revenue", { ascending: false });

		// Get daily business metrics for trend analysis
		const { data: dailyMetrics } = await supabaseAdmin
			.from("daily_business_metrics")
			.select("*")
			.gte("metric_date", periodStart.toISOString().split("T")[0])
			.lte("metric_date", periodEnd.toISOString().split("T")[0])
			.order("metric_date", { ascending: true });

		// Calculate average rating
		const ratingsData = currentReservations
			.flatMap((r) => r.customer_feedback || [])
			.map((f) => f.rating)
			.filter((rating) => rating !== null);

		const averageRating =
			ratingsData.length > 0 ? ratingsData.reduce((sum, rating) => sum + rating, 0) / ratingsData.length : 0;

		// Revenue by service category
		const revenueByCategory = currentReservations
			.filter((r) => r.status === "confirmed" || r.status === "completed")
			.reduce((acc, r) => {
				const category = (r as any).service?.category || "Other";
				acc[category] = (acc[category] || 0) + (r.total_amount || 0);
				return acc;
			}, {} as Record<string, number>);

		// Daily revenue trend
		const dailyRevenue = currentReservations
			.filter((r) => r.status === "confirmed" || r.status === "completed")
			.reduce((acc, r) => {
				const date = new Date(r.created_at).toISOString().split("T")[0];
				acc[date] = (acc[date] || 0) + (r.total_amount || 0);
				return acc;
			}, {} as Record<string, number>);

		// Status distribution
		const statusDistribution = currentReservations.reduce((acc, r) => {
			acc[r.status] = (acc[r.status] || 0) + 1;
			return acc;
		}, {} as Record<string, number>);

		// Top services by revenue
		const serviceRevenue = currentReservations
			.filter((r) => r.status === "confirmed" || r.status === "completed")
			.reduce((acc, r) => {
				const serviceName = (r as any).service?.name || "Unknown";
				acc[serviceName] = (acc[serviceName] || 0) + (r.total_amount || 0);
				return acc;
			}, {} as Record<string, number>);

		const topServices = Object.entries(serviceRevenue)
			.sort(([, a], [, b]) => b - a)
			.slice(0, 5)
			.map(([name, revenue]) => ({ name, revenue }));

		return NextResponse.json({
			period: {
				start: periodStart.toISOString(),
				end: periodEnd.toISOString(),
				days: Math.ceil((periodEnd.getTime() - periodStart.getTime()) / (24 * 60 * 60 * 1000)),
			},
			kpis: {
				totalReservations: {
					value: totalReservations,
					change: reservationsChange,
					trend: reservationsChange >= 0 ? "up" : "down",
				},
				totalRevenue: {
					value: totalRevenue,
					change: revenueChange,
					trend: revenueChange >= 0 ? "up" : "down",
					currency: "EUR",
				},
				totalParticipants: {
					value: totalParticipants,
					change: participantsChange,
					trend: participantsChange >= 0 ? "up" : "down",
				},
				occupancyRate: {
					value: Math.round(occupancyRate * 100) / 100,
					trend: "neutral", // Would need historical data for trend
				},
				averageRating: {
					value: Math.round(averageRating * 100) / 100,
					totalRatings: ratingsData.length,
				},
				newCustomers: {
					value: newCustomers,
				},
			},
			charts: {
				revenueByCategory: Object.entries(revenueByCategory).map(([category, revenue]) => ({
					category,
					revenue,
				})),
				dailyRevenue:
					dailyMetrics?.map((metric) => ({
						date: metric.metric_date,
						revenue: metric.total_revenue || 0,
						reservations: metric.total_reservations || 0,
						participants: metric.total_participants || 0,
					})) ||
					Object.entries(dailyRevenue)
						.sort(([a], [b]) => a.localeCompare(b))
						.map(([date, revenue]) => ({
							date,
							revenue,
						})),
				statusDistribution: Object.entries(statusDistribution).map(([status, count]) => ({
					status,
					count,
					percentage: Math.round((count / totalReservations) * 100),
				})),
				topServices,
				servicePerformance: servicePerformance?.slice(0, 10) || [],
			},
			analytics: {
				topCustomers:
					customerAnalytics?.map((customer) => ({
						id: customer.id,
						name: `${customer.first_name || ""} ${customer.last_name || ""}`.trim(),
						email: customer.email,
						totalSpent: customer.total_spent || 0,
						totalReservations: customer.total_reservations || 0,
						averageRating: customer.average_rating || 0,
						customerSince: customer.customer_since,
						lastReservation: customer.last_reservation_date,
					})) || [],
				serviceInsights:
					servicePerformance?.map((service) => ({
						id: service.id,
						name: service.name,
						category: service.category,
						totalRevenue: service.total_revenue || 0,
						totalBookings: service.total_bookings || 0,
						averageRating: service.average_rating || 0,
						cancellationRate: service.cancellation_rate || 0,
						avgGroupSize: service.avg_group_size || 0,
					})) || [],
				trends: {
					dailyMetrics: dailyMetrics || [],
					revenueGrowth: revenueChange,
					customerGrowth: newCustomers,
					occupancyTrend: occupancyRate,
				},
			},
			summary: {
				totalReservations,
				confirmedReservations: confirmedReservations.length,
				cancelledReservations: currentReservations.filter((r) => r.status === "cancelled").length,
				pendingReservations: currentReservations.filter((r) => r.status === "pending").length,
				totalRevenue,
				averageOrderValue: confirmedReservations.length > 0 ? totalRevenue / confirmedReservations.length : 0,
				totalParticipants,
				newCustomers,
				repeatCustomerRate: customerAnalytics
					? (customerAnalytics.filter((c) => (c.total_reservations || 0) > 1).length /
							customerAnalytics.length) *
					  100
					: 0,
				topSpendingCustomers: customerAnalytics?.slice(0, 5).length || 0,
				averageCustomerLifetimeValue:
					customerAnalytics && customerAnalytics.length > 0
						? customerAnalytics.reduce((sum, c) => sum + (c.total_spent || 0), 0) / customerAnalytics.length
						: 0,
			},
		});
	} catch (error) {
		console.error("Analytics GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "analytics:read");
